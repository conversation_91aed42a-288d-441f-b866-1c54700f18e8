'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url admin/job/recommend/sys/add 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, ids, user_id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		let id = await vk.baseDao.add({
			dbName: "job-recommend",
			dataJson: {
				creator_id: uid,
				job_ids: ids,
				user_id
			}
		});

		res.data = id
		res.msg = '生成成功!'

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}