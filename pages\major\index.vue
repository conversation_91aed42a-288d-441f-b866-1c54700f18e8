<template>
	<view class="page-body">
		<!-- 页面内容开始 -->

		<!-- 表格搜索组件开始 -->
		<!-- <vk-data-table-query v-model="queryForm1.formData" :columns="queryForm1.columns" @search="search"></vk-data-table-query> -->
		<!-- 表格搜索组件结束 -->

		<!-- 自定义按钮区域开始 -->
		<view>
			<el-row>
				<el-button type="primary" size="small" icon="el-icon-download" @click="downloadExcel">导出数据</el-button>
				<el-button type="success" size="small" icon="el-icon-upload" @click="uploadExcel">更新数据</el-button>
			</el-row>
		</view>
		<!-- 自定义按钮区域结束 -->

		<!-- 表格组件开始 -->
		<vk-data-table ref="table1" :action="table1.action" :columns="table1.columns" :default-expand-all="false" :query-form-param="queryForm1"></vk-data-table>
		<!-- 表格组件结束 -->

		<!-- 添加或编辑的弹窗开始 -->
		<vk-data-dialog v-model="form1.props.show" :title="form1.props.title" width="500px" mode="form" :close-on-click-modal="false">
			<vk-data-form v-model="form1.data" :rules="form1.props.rules" :action="form1.props.action" :form-type="form1.props.formType" :columns='form1.props.columns' label-width="120px" @success="form1.props.show = false;refresh();"></vk-data-form>
		</vk-data-dialog>
		<!-- 添加或编辑的弹窗结束 -->

		<!-- 页面内容结束 -->
	</view>
</template>

<script>
	let that; // 当前页面对象
	let vk = uni.vk; // vk实例
	let originalForms = {}; // 表单初始化数据

	export default {
		data() {
			// 页面数据变量
			return {
				// 页面是否请求中或加载中
				loading: false,
				// init请求返回的数据
				data: {

				},
				// 表格相关开始 -----------------------------------------------------------
				table1: {
					// 表格数据请求地址
					action: "admin/dict/sys/major",
					// 表格字段显示规则
					columns: [
						{ key: "name", title: "专业名称", type: "text", align: "left", },
					],
					// 多选框选中的值
					multipleSelection: [],
					// 当前高亮的记录
					selectItem: ""
				},
				// 表格相关结束 -----------------------------------------------------------
				// 表单相关开始 -----------------------------------------------------------
				// 查询表单请求数据
				queryForm1: {
					// 查询表单数据源，可在此设置默认值
					formData: {

					},
					// 查询表单的字段规则 fieldName:指定数据库字段名,不填默认等于key
					columns: [
						{ key: "name", title: "专业名称", type: "text", width: 160, mode: "%%" },
						{ key: "value", title: "专业编号", type: "text", width: 160, mode: "%%" },
					]
				},
				form1: {
					// 表单请求数据，此处可以设置默认值
					data: {

					},
					// 表单属性
					props: {
						// 表单请求地址
						action: "",
						// 表单字段显示规则
						columns: [
							{ key: "name", title: "专业名称", type: "text" },
							{ key: "code", title: "专业编号", type: "text" },
						],
						// 表单验证规则
						rules: {
							name: [{ required: true, message: '专业名称为必填字段', trigger: 'blur' }]
						},
						// add 代表添加 update 代表修改
						formType: "",
						// 弹窗标题
						title: "",
						// 是否显示表单的弹窗
						show: false
					}
				},
				// 其他弹窗表单
				formDatas: {},
				// 表单相关结束 -----------------------------------------------------------
			};
		},
		// 监听 - 页面每次【加载时】执行(如：前进)
		onLoad(options = {}) {
			that = this;
			vk = that.vk;
			that.options = options;
			that.init(options);
		},
		// 监听 - 页面【首次渲染完成时】执行。注意如果渲染速度快，会在页面进入动画完成前触发
		onReady() {},
		// 监听 - 页面每次【显示时】执行(如：前进和返回) (页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面)
		onShow() {},
		// 监听 - 页面每次【隐藏时】执行(如：返回)
		onHide() {},
		// 函数
		methods: {
			async downloadExcel() {
				let data = await vk.callFunction({
					url: 'admin/dict/sys/major',
					title: '请求中...',
				})
				let tableData = []
				data.rows.forEach(item => {
					item.children.forEach(items => {
						items.children.forEach(major => {
							tableData.push({
								大类: item.name,
								专业类别: items.name,
								专业代码: major.value,
								专业名称: major.name
							})
						})
					})
				})

				this.$refs.table1.exportExcel({
					fileName: "专业数据",
					data: tableData,
					showNo: false,
					columns: [
						{ key: "大类", title: "大类", type: "text" },
						{ key: "专业类别", title: "专业类别", type: "text" },
						{ key: "专业代码", title: "专业代码", type: "text" },
						{ key: "专业名称", title: "专业名称", type: "text" },
					]
				});
			},
			// 上传excel
			uploadExcel() {
				uni.chooseFile({
					extension: ['.xlsx', '.xls'],
					success: (res) => {
						console.log(res);
						this.urlTobase64(res.tempFilePaths[0], async (data) => {
							await vk.callFunction({
								url: 'admin/major/sys/import',
								title: '请求中...',
								data: {
									file: data
								},
							});
							vk.toast('导入成功');
							this.refresh()
						})
					}
				})
			},
			urlTobase64(url, callback) {
				uni.request({
					url: url,
					method: 'GET',
					responseType: 'arraybuffer',
					success: res => {
						let base64 = uni.arrayBufferToBase64(res.data); //把arraybuffer转成base64
						callback(base64)
					}
				});
			},
			// 页面数据初始化函数
			init(options) {
				originalForms["form1"] = vk.pubfn.copyObject(that.form1);
			},
			// 页面跳转
			pageTo(path) {
				vk.navigateTo(path);
			},
			// 表单重置
			resetForm() {
				vk.pubfn.resetForm(originalForms, that);
			},
			// 搜索
			search() {
				that.$refs.table1.search();
			},
			// 刷新
			refresh() {
				that.$refs.table1.refresh();
			},
			// 获取当前选中的行的数据
			getCurrentRow() {
				return that.$refs.table1.getCurrentRow();
			},
			// 监听 - 行的选中高亮事件
			currentChange(val) {
				that.table1.selectItem = val;
			},
			// 当选择项发生变化时会触发该事件
			selectionChange(list) {
				that.table1.multipleSelection = list;
			},
			// 显示添加页面
			addBtn() {
				that.resetForm();
				that.form1.props.action = 'admin/major/sys/add';
				that.form1.props.formType = 'add';
				that.form1.props.title = '添加';
				that.form1.props.show = true;
			},
			// 显示修改页面
			updateBtn({ item }) {
				that.form1.props.action = 'admin/major/sys/update';
				that.form1.props.formType = 'update';
				that.form1.props.title = '编辑';
				that.form1.props.show = true;
				that.form1.data = item;
			},
			// 删除按钮
			deleteBtn({ item, deleteFn }) {
				deleteFn({
					action: "admin/major/sys/delete",
					data: {
						_id: item._id
					},
				});
			},
		},
		// 监听属性
		watch: {

		},
		// 计算属性
		computed: {

		}
	};
</script>
<style lang="scss" scoped>
</style>