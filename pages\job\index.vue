<template>
	<view class="page-body">
		<!-- 页面内容开始 -->

		<!-- 表格搜索组件开始 -->
		<vk-data-table-query v-model="queryForm1.formData" :columns="queryForm1.columns" @search="search"></vk-data-table-query>
		<!-- 表格搜索组件结束 -->

		<!-- 自定义按钮区域开始 -->
		<view>
			<el-row>
				<el-button type="success" size="small" icon="el-icon-circle-plus-outline" @click="addBtn">添加</el-button>
				<el-button type="primary" size="small" icon="el-icon-upload" @click="uploadExcel">导入数据</el-button>
				<el-button type="primary" size="small" icon="el-icon-download">
					<a style="color: #fff;text-decoration: none;" href="https://mp-7f310963-042d-42a7-be33-1108df7c6dd9.cdn.bspapp.com/file/导入模板.xlsx">导入模板下载</a>
				</el-button>
				<!-- 批量操作 -->
				<el-button type="danger" size="small" :disabled="table1.multipleSelection.length === 0" @click="batchDelByIds">
					<i class="el-icon-delete"></i>批量删除
				</el-button>
			</el-row>
		</view>
		<view style="margin-top: 10px;">
			<!-- 推荐操作 -->
			<el-row>
				<el-button type="success" :disabled="table1.multipleSelection.length === 0" size="small" @click="joinRecommend(table1.multipleSelection)">加入推荐</el-button>
				<el-button type="primary" :disabled="recommendData.length === 0" size="small" @click="recommendShow=true">查看推荐清单</el-button>
			</el-row>
		</view>
		<!-- 自定义按钮区域结束 -->

		<!-- 表格组件开始 -->
		<vk-data-table ref="table1" :action="table1.action" :columns="table1.columns" :query-form-param="queryForm1" :right-btns="['detail_auto','more']" :custom-right-btns="table1.customRightBtns"
			:right-btns-more="table1.rightBtnsMore" :selection="true" :row-no="false" :pagination="true" @update="updateBtn" @delete="deleteBtn" @current-change="currentChange"
			@selection-change="selectionChange"></vk-data-table>
		<!-- 表格组件结束 -->

		<!-- 添加或编辑的弹窗开始 -->
		<vk-data-dialog v-model="form1.props.show" :title="form1.props.title" width="900px" mode="form" :close-on-click-modal="false">
			<vk-data-form v-model="form1.data" :rules="form1.props.rules" :action="form1.props.action" :form-type="form1.props.formType" :columns='form1.props.columns' label-width="120px"
				@success="form1.props.show = false;refresh();"></vk-data-form>
		</vk-data-dialog>
		<!-- 添加或编辑的弹窗结束 -->

		<!-- 推荐清单列表 -->
		<vk-data-dialog v-model="recommendShow" title="表单标题" width="1000px" mode="form">
			<vk-data-table ref="table2" :data="recommendData" :columns="table1.columns" :height="400" :right-btns="['detail_auto']" :row-no="false" :pagination="false"></vk-data-table>
			<view style="padding: 10px;box-sizing: border-box;display: flex;align-items: center;justify-content: space-between;">
				<vk-data-input-remote-select v-model="recommendUser" placeholder="请选择推荐的学生" action="admin/system/user/sys/getList" :props="{ list: 'rows', value: '_id', label: 'nickname' }"
					width="300px" show-all></vk-data-input-remote-select>
				<el-button type="primary" @click="createRecommend">生成推荐</el-button>
			</view>
		</vk-data-dialog>
		<!-- 页面内容结束 -->
	</view>
</template>

<script>
	let that; // 当前页面对象
	let vk = uni.vk; // vk实例
	let originalForms = {}; // 表单初始化数据

	export default {
		data() {
			// 页面数据变量
			return {
				// 推荐列表
				recommendData: [],
				recommendShow: false,
				recommendUser: null,
				// 页面是否请求中或加载中
				loading: false,
				// init请求返回的数据
				data: {

				},
				// 表格相关开始 -----------------------------------------------------------
				table1: {
					// 表格数据请求地址
					action: "admin/job/sys/getList",
					// 自定义按钮
					customRightBtns: [{
						title: '加入推荐',
						type: 'primary',
						onClick: (item) => {
							this.joinRecommend([item])
						}
					}],
					rightBtnsMore: [{
						title: '编辑岗位',
						onClick: (item) => {
							this.updateBtn(item)
						}
					}, {
						title: '删除岗位',
						onClick: (item) => {
							uni.showModal({
								title: "删除提示",
								content: `确认要删除${item.name}吗`,
								success: (res) => {
									if (res.confirm) {
										this.deleteBtn(item)
									}
								}
							})
						}
					}],
					// 表格字段显示规则
					columns: [
						{ key: "name", title: "岗位信息", type: "text", width: 180, fixed: true },
						{ key: "company_name", title: "企业名称", type: "text", width: 180 },
						{ key: "province_name", title: "省", type: "text" },
						{ key: "city_name", title: "市", type: "text" },
						{ key: "county_name", title: "县", type: "text" },
						{ key: "education", title: "最低学历", type: "text" },
						{
							key: "recruitment_type",
							title: "招聘类型",
							type: "tag",
							width: 150
						},
						{
							key: "job_property",
							title: "职位性质",
							type: 'tag'
						},
						{
							key: "graduation_year",
							title: "届别",
							type: "text",
							formatter: (val) => {
								if (val) {
									return val.substring(2, 4) + '届'
								} else {
									return '不限'
								}
							}
						},
						{
							key: "salary",
							title: "薪资",
							type: "text",
							width: 150
						},
						{
							key: "",
							title: "专业要求",
							type: "tag",
							formatter: (val, rows, col) => {
								let major = val.major_info.map(item => item.name)
								let classic = val.major_classic.map(item => item.name)
								let result = [...classic, ...major]
								if (result.length == 0) return ['不限制专业']
								return result
							},
							width: 220
						},
						{ key: "introduce", title: "岗位详情", type: "editor", show: ['detail'] },
						{ key: "original_url", title: "原文链接", type: "text", show: ['detail'] },
						{ key: "application_url", title: "投递链接", type: "text", show: ['detail'] },
						{
							key: "end_date",
							title: "截止时间",
							type: "time",
							width: 180,
							formatter: (val, rows, col) => {
								if (val) return val
								else return '招满即止'
							},
						},
						{ key: "tags", title: "标签", type: "tag", show: ['detail'] },
					],
					// 多选框选中的值
					multipleSelection: [],
					// 当前高亮的记录
					selectItem: ""
				},
				// 表格相关结束 -----------------------------------------------------------
				// 表单相关开始 -----------------------------------------------------------
				// 查询表单请求数据
				queryForm1: {
					// 查询表单数据源，可在此设置默认值
					formData: {

					},
					// 查询表单的字段规则 fieldName:指定数据库字段名,不填默认等于key
					columns: [
						{ key: "name", title: "岗位名称", type: "text", mode: '%%' },
						{ key: "company_name", title: "企业名称", type: "text", mode: '%%' },
						{
							key: "edu_value",
							title: "最低学历",
							type: "remote-select",
							action: "admin/dict/sys/education",
							props: { list: "rows", value: "level", label: "name" },
							mode: ">=",
							fieldName: "education_info.level",
							lastWhereJson: true,
							showAll: true,
							actionData: {
								pageSize: 1000,
							}
						},
						{ key: "province", title: "省", type: "text", hidden: true },
						{ key: "city", title: "市", type: "text", hidden: true },
						{ key: "county", title: "县", type: "text", hidden: true },
						{
							key: "area",
							title: "地区",
							type: "cascader",
							action: "admin/dict/sys/cityTree",
							props: {
								list: "rows",
								value: "value",
								label: "name",
								children: "children",
								checkStrictly: true
							},
							autoSearch: false
						},
						// {
						// 	key: "major",
						// 	title: "专业",
						// 	type: "cascader",
						// 	action: "admin/major/sys/dict",
						// 	props: {
						// 		list: "rows",
						// 		value: "_id",
						// 		label: "name",
						// 		children: "children",
						// 		checkStrictly: true
						// 	},
						// 	autoSearch: false
						// },
					]
				},
				form1: {
					// 表单请求数据，此处可以设置默认值
					data: {

					},
					// 表单属性
					props: {
						// 表单请求地址
						action: "",
						// 表单字段显示规则
						columns: [
							{ key: "name", title: "岗位名称", type: "text" },
							{
								key: "company_name",
								title: "公司",
								type: "text"
							},
							{
								key: "job_property",
								title: "职位性质",
								type: 'tag'
							},
							{
								key: "",
								title: "",
								type: "group",
								justify: "start",
								columns: [{
										key: "province",
										title: "省",
										type: "remote-select",
										placeholder: "请选择省",
										action: "admin/dict/sys/city",
										props: { list: "rows", value: "value", label: "name" },
										showAll: true,
										actionData: {
											pageSize: 1000,
											level: "province",
										},
										watch: ({ value, formData, column, index, option, $set }) => {
											// 此处演示根据选择的值动态改变text1的值
											$set("city", null);
											$set("county", null);
										}
									},
									{
										key: "city",
										title: "市",
										labelWidth: 50,
										type: "remote-select",
										placeholder: "请选择市",
										action: "admin/dict/sys/city",
										props: { list: "rows", value: "value", label: "name" },
										showAll: true,
										actionData: () => {
											return {
												pageSize: 1000,
												level: "city",
												parent_code: this.form1.data.province,
											}
										},
										showRule: () => !!this.form1.data.province,
										watch: ({ value, formData, column, index, option, $set }) => {
											// 此处演示根据选择的值动态改变text1的值
											$set("county", null);
										}
									},
									{
										key: "county",
										title: "县",
										labelWidth: 50,
										type: "remote-select",
										placeholder: "请选择县",
										action: "admin/dict/sys/city",
										props: { list: "rows", value: "value", label: "name" },
										showAll: true,
										actionData: () => {
											return {
												pageSize: 1000,
												level: "county",
												parent_code: this.form1.data.city,
											}
										},
										showRule: () => !!this.form1.data.city,
									},
								]
							},
							{
								key: "education_id",
								title: "学历要求",
								type: "remote-select",
								placeholder: "请选择最低学历要求，不选默认为不限制",
								action: "admin/base/education/sys/getList",
								props: { list: "rows", value: "_id", label: "name" },
								showAll: true,
								actionData: {
									pageSize: 1000,
								}
							},
							{
								key: "recruitment_type",
								title: "招聘类型",
								type: 'tag'
							},
							{
								key: "graduation_year",
								title: "届别",
								type: "date",
								dateType: "year",
								valueFormat: "yyyy",
								format: "yy届",
								placeholder: "请选择届别"
							},
							{
								key: "salary",
								title: "薪资",
								type: "text"
							},
							{
								key: "major",
								title: "专业要求",
								placeholder: "请选择专业",
								action: "major-classic",
								type: "cascader",
								action: "admin/dict/sys/major",
								filterable: true,
								props: {
									list: "rows",
									value: "value",
									label: "name",
									children: "children",
									checkStrictly: true,
									multiple: true,
									emitPath: false,
									collapseTags: true
								},
								showAll: true,
								actionData: {
									pageSize: 1000,
								}
							},
							{ key: "introduce", title: "岗位详情", type: "editor" },
							{ key: "original_url", title: "原文链接", type: "text" },
							{ key: "application_url", title: "投递链接", type: "text" },
							{ key: "end_date", title: "截止时间", type: "date", dateType: "datetime" },
							{ key: "tags", title: "标签", type: "tag" },
						],
						// 表单验证规则
						rules: {
							name: [{ required: true, message: '岗位名称不能为空', trigger: 'blur' }],
							company_name: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
							province: [{ required: true, message: '请选择所在省', trigger: 'blur' }],
							city: [{ required: true, message: '请选择所在市', trigger: 'blur' }],
							county: [{ required: true, message: '请选择所在县', trigger: 'blur' }],
						},
						// add 代表添加 update 代表修改
						formType: "",
						// 弹窗标题
						title: "",
						// 是否显示表单的弹窗
						show: false
					}
				},
				// 其他弹窗表单
				formDatas: {},
				// 表单相关结束 -----------------------------------------------------------
			};
		},
		// 监听 - 页面每次【加载时】执行(如：前进)
		onLoad(options = {}) {
			that = this;
			vk = that.vk;
			that.options = options;
			that.init(options);
		},
		// 监听 - 页面【首次渲染完成时】执行。注意如果渲染速度快，会在页面进入动画完成前触发
		onReady() {},
		// 监听 - 页面每次【显示时】执行(如：前进和返回) (页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面)
		onShow() {},
		// 监听 - 页面每次【隐藏时】执行(如：返回)
		onHide() {},
		// 函数
		methods: {
			// 生成推荐
			async createRecommend() {
				try {
					let ids = this.recommendData.map(item => item._id)
					let user_id = this.recommendUser
					if (!user_id) {
						vk.toast('请先选择要推荐的学生');
						return
					}
					let data = await vk.callFunction({
						url: 'admin/job/recommend/sys/add',
						title: '请求中...',
						data: {
							ids,
							user_id
						},
					});
					vk.toast('生成成功，请到小程序分享给相关学生', true, () => {
						this.initRecommend()
						this.refresh()
					});
				} catch (err) {
					vk.toast('生成失败');
				}
			},
			// 初始化推荐
			initRecommend() {
				this.recommendData = []
				this.recommendShow = false
				this.recommendUser = null
			},
			// 加入推荐
			joinRecommend(datas) {
				// 剔除已加入的数据
				let ary = datas.filter(item => !this.recommendData.some(items => items._id === item._id))
				this.recommendData.push(...ary)
				// 重置多选框状态
				let uTreeData = this.$refs.table1.getUTreeData()
				let selects = uTreeData.map(item => {
					return {
						row: item,
						selected: false
					}
				})
				this.$refs.table1.toggleRowSelection(selects)
				this.table1.multipleSelection = []
			},
			// 上传excel
			uploadExcel() {
				uni.chooseFile({
					extension: ['.xlsx', '.xls'],
					success: (res) => {
						console.log(res);
						this.urlTobase64(res.tempFilePaths[0], async (data) => {
							await vk.callFunction({
								url: 'admin/job/sys/importJob',
								title: '请求中...',
								data: {
									file: data
								},
							});
							vk.toast('导入成功');
							this.refresh()
						})
					}
				})
			},
			urlTobase64(url, callback) {
				uni.request({
					url: url,
					method: 'GET',
					responseType: 'arraybuffer',
					success: res => {
						let base64 = uni.arrayBufferToBase64(res.data); //把arraybuffer转成base64
						callback(base64)
					}
				});
			},
			// 页面数据初始化函数
			init(options) {
				originalForms["form1"] = vk.pubfn.copyObject(that.form1);
			},
			// 页面跳转
			pageTo(path) {
				vk.navigateTo(path);
			},
			// 表单重置
			resetForm() {
				vk.pubfn.resetForm(originalForms, that);
			},
			// 搜索
			search() {
				that.$refs.table1.search();
			},
			// 刷新
			refresh() {
				that.$refs.table1.refresh();
			},
			// 获取当前选中的行的数据
			getCurrentRow() {
				return that.$refs.table1.getCurrentRow();
			},
			// 监听 - 行的选中高亮事件
			currentChange(val) {
				that.table1.selectItem = val;
			},
			// 当选择项发生变化时会触发该事件
			selectionChange(list) {
				that.table1.multipleSelection = list;
			},
			// 显示添加页面
			addBtn() {
				that.resetForm();
				that.form1.props.action = 'admin/job/sys/add';
				that.form1.props.formType = 'add';
				that.form1.props.title = '添加';
				that.form1.props.show = true;
			},
			// 显示修改页面
			updateBtn(item) {
				console.log(item);
				that.form1.props.action = 'admin/job/sys/update';
				that.form1.props.formType = 'update';
				that.form1.props.title = '编辑';
				that.form1.props.show = true;
				that.form1.data = item;
			},
			// 删除按钮
			async deleteBtn(item) {
				try {
					let data = await vk.callFunction({
						url: "admin/job/sys/delete",
						title: '删除中...',
						data: {
							_id: item._id
						},
					});
					vk.toast('删除成功');
					this.refresh()
				} catch (err) {
					vk.toast('删除失败');
				}
			},
			// 批量删除
			batchDelByIds() {
				let ids = this.table1.multipleSelection.map(item => item._id)
				uni.showModal({
					title: "删除提示",
					content: "确认要删除选中的数据吗?",
					success: async (res) => {
						if (res.confirm) {
							try {
								let data = await vk.callFunction({
									url: 'admin/job/sys/batchDel',
									title: '请求中...',
									data: {
										ids
									},
								});
								this.refresh()
							} catch (err) {
								vk.toast('删除失败');
							}
						}
					}
				})
			}
		},
		// 监听属性
		watch: {

		},
		// 计算属性
		computed: {

		}
	};
</script>
<style lang="scss" scoped>
	.page-body {}
</style>