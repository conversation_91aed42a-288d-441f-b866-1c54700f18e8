## 统一用户中心
#### `router/service/user`
#### 此目录为集成`uni-id`账户系统的实现（开发者自己系统的逻辑尽量不要写在此处，应写在`service/admin`或`service/client`或`其他`目录下

```
.
├── user──────────────────────# 统一用户中心服务（已集成uniID）
│ ── └── kh───────────────────# kh函数为必须登录后才能访问的函数（客户端用户）
│ ── └── pub──────────────────# pub函数为所有人都可以访问，不限制
│ ── └── sys──────────────────# sys函数为需要授权才能访问的函数（如商家工作人员等）
│ ── └── util─────────────────# util为工具类，只能被云函数内调用，客户端无法直接访问
└─────────────────────────────
```