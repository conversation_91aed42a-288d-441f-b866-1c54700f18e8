{"id": "vk-unicloud", "displayName": "vk-unicloud-router开发框架核心库 - 已集成uni-id 框架内置了众多API。", "version": "2.19.7", "description": "此为vk-unicloud-router框架核心库（新手建议下载完整框架项目）已集成uni-id支持云函数url化。众多现成API，内置小白也能轻松上手的数据库API。使你项目刚起步进度就是百分之50", "keywords": ["vk-unicloud-router", "云函数路由、云对象路由", "vk云开发", "内置uni-id、数据库baseDao", "内置众多API、工具包"], "author": "VK", "repository": "https://gitee.com/vk-uni/vk-uni-cloud-router", "engines": {"HBuilderX": "^3.1.0"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": "370725567"}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": "https://vkdoc.fsq.pub/", "type": "unicloud-template-function"}, "uni_modules": {"dependencies": ["uni-config-center"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "y"}, "client": {"App": {"app-vue": "y", "app-nvue": "y", "app-harmony": "u", "app-uvue": "u"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y", "钉钉": "u", "快手": "u", "飞书": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}, "Vue": {"vue2": "y", "vue3": "u"}}}}}