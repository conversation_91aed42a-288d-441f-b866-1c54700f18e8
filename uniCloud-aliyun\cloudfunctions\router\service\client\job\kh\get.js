'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/job/pub/get 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		const { rows } = await vk.baseDao.selects({
			dbName: "jobs",
			getOne: true,
			// 主表where条件
			whereJson: {
				_id: id
			},
			addFields: {
				county_name: "$county_info.name",
				city_name: "$city_info.name",
				province_name: "$province_info.name",
				education: "$education_info.name",
				edu_value: "$education_info.level",
			},
			foreignDB: [{
				dbName: "city-dicts",
				localKey: "county",
				foreignKey: "value",
				as: "county_info",
				limit: 1
			}, {
				dbName: "city-dicts",
				localKey: "city",
				foreignKey: "value",
				as: "city_info",
				limit: 1
			}, {
				dbName: "city-dicts",
				localKey: "province",
				foreignKey: "value",
				as: "province_info",
				limit: 1
			}, {
				dbName: "education-list",
				localKey: "education_id",
				foreignKey: "_id",
				as: "education_info",
				limit: 1
			}, {
				dbName: "major-list",
				localKey: "major",
				foreignKey: "value",
				localKeyType: "array",
				as: "major_info",
				limit: 20
			}, {
				dbName: "major-classic",
				localKey: "major",
				foreignKey: "value",
				localKeyType: "array",
				as: "major_classic",
				limit: 20
			}]
		});
		if (rows) {
			res.msg = '获取成功'
			res.data = rows
		} else {
			res.msg = '查找不到相应信息'
			res.back = true
			res.code = -1
		}

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}