'use strict';
const {
	excelTojson,
	jsonToexcel
} = require('ml-excel-to-json');
module.exports = {
	/**
	 * XXXnameXXX
	 * @url admin/major/sys/import 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, file } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		// 数组与id匹配
		function idMatch(list, ids) {
			return list.map((item, index) => {
				item._id = ids[index]
				return item
			})
		}

		let json = await excelTojson(file);
		let sheetData = json.data.filter(item => item.专业代码)

		// ----------------大类分类处理------------------
		let main_classic = sheetData.reduce((ary, item) => {
			let index = ary.findIndex(items => item.大类 === items.name)
			if (index === -1) ary.push({ name: item.大类, value: item.大类, level: 2 })
			return ary
		}, [])

		let main_classic_name = main_classic.map(item => item.name)

		// 查找大类并添加合并同类项
		let has_main_classic = await vk.baseDao.select({
			dbName: "major-classic",
			getMain: true,
			getCount: false,
			pageIndex: 1,
			pageSize: 5000,
			whereJson: {
				level: 2,
				name: _.in(main_classic_name)
			},
		});

		// 寻找未添加的大类
		let not_main_classic = []
		main_classic.forEach(item => {
			let info = has_main_classic.find(items => items.name == item.name)
			if (!info) not_main_classic.push(item)
		})

		// 添加相应的专业类别
		let main_classic_ids = []
		if (not_main_classic.length > 0) {
			main_classic_ids = await vk.baseDao.adds({
				dbName: "major-classic",
				dataJson: not_main_classic
			});
		}

		const main_classic_result = await vk.baseDao.select({
			dbName: "major-classic",
			getCount: false,
			getMain: true,
			pageIndex: 1,
			pageSize: 1000,
			whereJson: {
				level: 2
			},
		});
		// -------------大类处理完毕------------------

		// ----------------处理专业类别------------------
		// 2. 处理专业类别
		let major_classic = sheetData.reduce((ary, item) => {
			let index = ary.findIndex(items => item.专业类别 === items.name)
			let parent = main_classic_result.find(items => item.大类 === items.name)
			if (index === -1 && parent) ary.push({ name: item.专业类别, value: item.专业类别, level: 1, classic_id: parent._id })
			return ary
		}, [])


		// 删除相关专业类别
		await vk.baseDao.del({
			dbName: "major-classic",
			whereJson: {
				level: 1
			}
		});

		// 添加相应的专业类别
		let major_classic_ids = []
		if (major_classic.length > 0) {
			major_classic_ids = await vk.baseDao.adds({
				dbName: "major-classic",
				dataJson: major_classic
			});
		}

		const major_classic_result = await vk.baseDao.select({
			dbName: "major-classic",
			getCount: false,
			getMain: true,
			pageIndex: 1,
			pageSize: 1000,
			whereJson: {
				level: 1
			},
		});
		// ----------------专业类别处理结束------------------

		// ----------------专业处理开始------------------
		let major = []
		sheetData.forEach(item => {
			let classic = major_classic_result.find(items => item.专业类别 === items.name)
			if (classic) {
				major.push({
					name: item.专业名称,
					value: item.专业代码,
					classic_id: classic._id
				})
			}
		})

		// 清空专业
		await vk.baseDao.del({
			dbName: "major-list",
			whereJson: {
				_id: _.exists(true),
			}
		});

		await vk.baseDao.adds({
			dbName: "major-list",
			dataJson: major
		});
		// ----------------专业处理结束------------------

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}