module.exports = {
	/**
	 * 查询多条记录 分页
	 * @url admin/job/pending/sys/getList 前端调用的url参数地址
	 * data 请求参数 说明
	 * @param {Number}         pageIndex 当前页码
	 * @param {Number}         pageSize  每页显示数量
	 * @param {Array<Object>}  sortRule  排序规则
	 * @param {object}         formData  查询条件数据源
	 * @param {Array<Object>}  columns   查询条件规则
	 * res 返回参数说明
	 * @param {Number}         code      错误码，0表示成功
	 * @param {String}         msg       详细信息
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid } = data;
		let res = { code: 0, msg: '' };
		// 业务逻辑开始-----------------------------------------------------------
		let dbName = "jobs-pending";
		res = await vk.baseDao.getTableData({
			dbName,
			data,
			addFields: {
				county_name: "$county_info.name",
				city_name: "$city_info.name",
				province_name: "$province_info.name",
				education: "$education_info.name",
				edu_value: "$education_info.level",
			},
			foreignDB: [{
				dbName: "city-dicts",
				localKey: "county",
				foreignKey: "value",
				as: "county_info",
				limit: 1
			}, {
				dbName: "city-dicts",
				localKey: "city",
				foreignKey: "value",
				as: "city_info",
				limit: 1
			}, {
				dbName: "city-dicts",
				localKey: "province",
				foreignKey: "value",
				as: "province_info",
				limit: 1
			}, {
				dbName: "education-list",
				localKey: "education_id",
				foreignKey: "_id",
				as: "education_info",
				limit: 1
			}, {
				dbName: "major-list",
				localKey: "major_code",
				foreignKey: "code",
				localKeyType: "array",
				as: "major_info",
				limit: 20
			}]
		});
		return res;
	}

}