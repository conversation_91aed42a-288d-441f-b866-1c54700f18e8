'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url admin/job/sys/batchDel 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, ids } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		if (!Array.isArray(ids) || ids.length === 0) return { code: -1, msg: '请传入正确的参数' }

		// 先加入回收站
		let jobs = await vk.baseDao.select({
			dbName: "jobs",
			getMain: true,
			getCount: false,
			pageIndex: 1,
			pageSize: 1000,
			whereJson: {
				_id: _.in(ids)
			},
		});

		jobs = jobs.map(item => {
			item.creator_id = uid
			return item
		})

		await vk.baseDao.adds({
			dbName: "jobs-recycle",
			dataJson: jobs
		});
		
		// 删除岗位信息
		await vk.baseDao.del({
			dbName: "jobs",
			whereJson: {
				_id: _.in(ids)
			}
		});

		res.msg = '删除成功'

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}