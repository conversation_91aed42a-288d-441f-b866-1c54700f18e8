"use strict";
const { excelTojson, jsonToexcel } = require("ml-excel-to-json");
module.exports = {
	/**
	 * XXXnameXXX
	 * @url admin/job/sys/importJob 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, file } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		let cityDicts = await vk.baseDao.select({
			dbName: "city-dicts",
			getMain: true,
			getCount: false,
			pageIndex: 1,
			pageSize: 5000,
		});

		let keyMap = [{
				label: "岗位",
				key: "name",
			},
			{
				label: "岗位详情",
				key: "introduce",
			},
			{
				label: "企业名称",
				key: "company_name",
			},
			{
				label: "工作地区",
				key: "work_area",
			},
			{
				label: "最低学历",
				key: "education",
			},
			{
				label: "届别",
				key: "graduation_year",
			},
			{
				label: "招聘性质",
				key: "recruitment_type",
			},
			{
				label: "专业要求",
				key: "major",
			},
			{
				label: "薪资",
				key: "salary",
			},
			{
				label: "职位性质",
				key: "job_property",
			},
			{
				label: "原文链接",
				key: "original_url",
			},
			{
				label: "投递链接",
				key: "application_url",
			},
			{
				label: "截止时间",
				key: "end_date",
			},
			{
				label: "标签",
				key: "tags",
			},
		];

		let json = await excelTojson(file);

		let sheetData = json.data;

		console.log(sheetData);

		// 查找相应的行政区信息
		function findAreaInfo(code) {
			return cityDicts.find((item) => item.value === code);
		}
		// 分隔字符
		function splitString(text) {
			if (!text) return [];
			// 处理不同的分隔符：，、,/\
			const separators = ["，", "、", ",", "/", "\\"];
			let result = [text];

			// 依次使用每个分隔符进行分割
			for (let separator of separators) {
				let tempResult = [];
				for (let item of result) {
					if (typeof item === "string") {
						// 使用当前分隔符分割字符串
						let splitItems = item.split(separator);
						// 过滤掉空字符串
						splitItems = splitItems.filter((str) => str.trim() !== "");
						tempResult.push(...splitItems);
					} else {
						tempResult.push(item);
					}
				}
				result = tempResult;
			}

			// 去重并返回结果
			return [...new Set(result)];
		}

		let edu_ary = [];
		let major_ary = [];
		let transformedData = sheetData.map((item) => {
			const newItem = {};
			keyMap.forEach((mapItem) => {
				let value = item[mapItem.label] || null;
				let city;
				switch (mapItem.key) {
					// 如果是时间格式则处理
					case "end_date":
						value = value ? new Date(value).valueOf() : null;
						break;
						// 轮询取出相应的行政区划
					case "work_area":
						city = cityDicts.filter((item) => item.name.includes(value));
						if (city.length === 1) {
							newItem[city[0].level] = city[0].value;
							city = findAreaInfo(city[0].parent_code);
							newItem[city.level] = city.value;
							while (city && city.parent_code !== "000000") {
								city = findAreaInfo(city.parent_code);
								newItem[city.level] = city.value;
							}
						}
						break;
						// 匹配招聘信息
					case "recruitment_type":
					case "tags":
					case "job_property":
						value = splitString(value);
						break;
					case "graduation_year":
						value = value ? value.toString() : null;
						break;
					case "education":
						edu_ary.push(value);
						break;
					case "major":
						value = splitString(value);
						major_ary.push(...value);
						break;
					default:
						break;
				}
				newItem[mapItem.key] = value;
			});
			return newItem;
		});

		// 查找相应的学历
		let edu_list = await vk.baseDao.select({
			dbName: "education-list",
			getMain: true,
			getCount: false,
			pageIndex: 1,
			pageSize: 5000,
			whereJson: {
				name: _.in(edu_ary),
			},
			fieldJson: {
				name: true,
				_id: true,
			},
		});

		// 查找相应的专业或者专业类别
		let major_list = await vk.baseDao.select({
			dbName: "major-list",
			getMain: true,
			getCount: false,
			pageIndex: 1,
			pageSize: 5000,
			whereJson: {
				name: _.in(major_ary),
			},
			fieldJson: {
				name: true,
				code: true,
			},
		});

		let major_classic_list = await vk.baseDao.select({
			dbName: "major-classic",
			getMain: true,
			getCount: false,
			pageIndex: 1,
			pageSize: 5000,
			whereJson: {
				level: 1,
				name: _.in(major_ary),
			},
			fieldJson: {
				name: true,
				_id: true,
			},
		});

		// 匹配信息
		transformedData = transformedData.map((item) => {
			// 匹配查询到的学历
			let edu_info = edu_list.find((items) => item.education === items.name);
			if (edu_info) item.education_id = edu_info._id;
			// 匹配查询到的专业
			let major_info = major_list.filter((aItem) => item.major.some((bItem) => aItem.name === bItem));
			const major_code = major_info.length > 0 ? major_info.map((items) => items.code) : [];
			// 匹配专业类别
			let major_classic_info = major_classic_list.filter((aItem) => item.major.some((bItem) => aItem.name === bItem));
			const major_classic = major_classic_info.length > 0 ? major_classic_info.map((items) => items.name) : [];
			// 将专业信息存储成数据
			item.major = [...major_classic, ...major_code]

			return item;
		});

		// 挑出异常数据和正常数据
		let error_data = [];
		let normal_data = [];

		transformedData.forEach((item) => {
			item.err = [];
			if (!item.province && !item.city && !item.county) item.err.push("地区信息不完整");

			if (item.err.length === 0) {
				delete item.err;
				normal_data.push(item);
			} else {
				error_data.push(item);
			}
		});

		// 把正常的数据加入职位列表
		if (normal_data.length > 0) {
			await vk.baseDao.adds({
				dbName: "jobs",
				dataJson: normal_data,
			});
		}
		if (error_data.length > 0) {
			await vk.baseDao.adds({
				dbName: "jobs-pending",
				dataJson: error_data,
			});
		}

		// 业务逻辑结束-----------------------------------------------------------
		return {
			code: 0,
			success_num: normal_data.length,
			errror_num: error_data.length,
			msg: "处理成功",
		};
	},
};