'use strict';
module.exports = {
	/**
	 * 获取今日推荐工作
	 * @url client/job/pub/recommend 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		const whereJson = {}

		let time = Date.now().valueOf() - 86400000

		res = await vk.baseDao.selects({
			dbName: "job-recommend",
			getCount: false,
			getOne: true,
			// 主表where条件
			whereJson: {
				user_id: uid,
				_add_time: _.gte(time)
			},
			// 主表排序规则
			sortArr: [{ name: "_id", type: "desc" }],
			// 副表列表
			foreignDB: [{
				dbName: "jobs",
				localKey: "job_ids",
				foreignKey: "_id",
				localKeyType: "array",
				as: "jobs",
				limit: 1000,
				addFields: {
					county_name: "$county_info.name",
					city_name: "$city_info.name",
					province_name: "$province_info.name",
					education: "$education_info.name",
					edu_value: "$education_info.level",
				},
				foreignDB: [{
					dbName: "city-dicts",
					localKey: "county",
					foreignKey: "value",
					as: "county_info",
					limit: 1
				}, {
					dbName: "city-dicts",
					localKey: "city",
					foreignKey: "value",
					as: "city_info",
					limit: 1
				}, {
					dbName: "city-dicts",
					localKey: "province",
					foreignKey: "value",
					as: "province_info",
					limit: 1
				}, {
					dbName: "education-list",
					localKey: "education_id",
					foreignKey: "_id",
					as: "education_info",
					limit: 1
				}, {
					dbName: "major-list",
					localKey: "major",
					foreignKey: "value",
					localKeyType: "array",
					as: "major_info",
					limit: 20
				}, {
					dbName: "major-classic",
					localKey: "major",
					foreignKey: "value",
					localKeyType: "array",
					as: "major_classic",
					limit: 20
				}]
			}]
		});

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}