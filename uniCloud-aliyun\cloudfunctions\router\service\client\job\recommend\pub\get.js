'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/job/recommend/kh/get 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		res = await vk.baseDao.selects({
			dbName: "job-recommend",
			getCount: false,
			getOne: true,
			// 主表where条件
			whereJson: {
				_id: id
			},
			// 主表排序规则
			sortArr: [{ name: "_id", type: "desc" }],
			// 副表列表
			foreignDB: [{
				dbName: "jobs",
				localKey: "job_ids",
				foreignKey: "_id",
				localKeyType: "array",
				as: "jobs",
				limit: 20
			}, {
				dbName: "uni-id-users",
				localKey: "user_id",
				foreignKey: "_id",
				as: "user_info",
				limit: 1
			}]
		});


		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}