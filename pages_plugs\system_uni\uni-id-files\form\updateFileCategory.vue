<template>
	<vk-data-dialog
		v-model="value.show"
		:title="page.title"
		:top="page.top"
		:width="page.width"
		:close-on-click-modal="true"
		mode="form"
	>
		<!-- 页面主体内容开始 -->
		<vk-data-form
			ref="form1"
			v-model="form1.data"
			:form-type="value.mode"
			:rules="form1.props.rules"
			:action="form1.props.action"
			:columns="form1.props.columns"
			:loading.sync="form1.props.loading"
			:labelWidth="form1.props.labelWidth"
			:show-cancel="page.showCancel"
			:cancel-text="page.cancelText"
			:submit-text="page.submitText"
			@success="onFormSuccess"
		>
			<template v-slot:category_id="{ form, keyName }">
				<vk-data-input-radio
					width="100%"
				  v-model="form[keyName]"
				  :localdata="value.list"
					:props="{ value:'_id', label:'name' }"
				  placeholder="请选择分组"
					border
					:item-width="300"
				></vk-data-input-radio>
			</template>
		</vk-data-form>
		<!-- 页面主体内容结束 -->
	</vk-data-dialog>
</template>

<script>
let that; // 当前页面对象
let vk = uni.vk; // vk实例
export default {
	props: {
		value: {
			type: Object,
			default: function() {
				return {
					show: false,
					mode: "",
					item: {},
					list:[]
				};
			}
		}
	},
	data: function() {
		// 组件创建时,进行数据初始化
		return {
			page: {
				title: "修改分组",
				submitText: "确定",
				cancelText: "关闭",
				showCancel: true,
				top: "14vh",
				width: "450px"
			},
			form1: {
				// 表单请求数据，此处可以设置默认值
				data: {},
				// 表单属性
				props: {
					// 表单请求地址
					action: "admin/system_uni/uni-id-files/files/kh/update",
					// 表单字段显示规则
					columns: [
						{
						  key:"category_id", title:"分组", type:"select", placeholder:"请选择分组",
						}
					],
					// 表单验证规则
					rules: {},
					labelWidth: "60px"
				}
			}
		};
	},
	mounted() {
		that = this;
		that.init();
	},
	methods: {
		// 初始化
		init() {
			let { value } = that;
			that.$emit("input", value);
		},
		// 监听 - 页面打开
		onOpen() {
			that = this;
			let { item } = that.value;
			that.form1.data = {
				_id: item._id,
				category_id: item.category_id
			};
		},
		// 监听 - 页面关闭
		onClose() {
			// that.$refs.form1.resetForm(); // 关闭时，重置表单
		},
		// 监听 - 提交成功后
		onFormSuccess() {
			that.value.show = false; // 关闭页面
			that.$emit("success");
		}
	},
	watch: {
		"value.show": {
			handler(newValue, oldValue) {
				let that = this;
				if (newValue) {
					that.onOpen();
				} else {
					that.onClose();
				}
			}
		}
	},
	// 计算属性
	computed: {}
};
</script>

<style lang="scss" scoped></style>
