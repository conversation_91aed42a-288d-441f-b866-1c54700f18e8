{"name": "router", "version": "1.0.0", "description": "【开箱即用】vk-uniCloud-router - 云函数路由模式 - uniCloud开发框架 - 已集成uni-id", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"uni-config-center": "file:../../../uni_modules/uni-config-center/uniCloud/cloudfunctions/common/uni-config-center", "uni-id": "file:../../../uni_modules/uni-id/uniCloud/cloudfunctions/common/uni-id", "vk-unicloud": "file:../../../uni_modules/vk-unicloud/uniCloud/cloudfunctions/common/vk-unicloud", "ml-excel-to-json": "file:../../../uni_modules/ml-excel-to-json/uniCloud/cloudfunctions/common/ml-excel-to-json"}, "private": true, "cloudfunction-config": {"concurrency": 1, "memorySize": 512, "path": "", "timeout": 60, "triggers": [], "runtime": "Nodejs18", "keepRunningAfterReturn": false}, "extensions": {}, "origin-plugin-dev-name": "vk-cloud-router", "origin-plugin-version": "2.19.8", "plugin-dev-name": "vk-cloud-router", "plugin-version": "2.19.8"}