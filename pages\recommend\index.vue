<template>
	<view class="page-body">
		<!-- 页面内容开始 -->

		<!-- 表格搜索组件开始 -->
		<vk-data-table-query v-model="queryForm1.formData" :columns="queryForm1.columns" @search="search"></vk-data-table-query>
		<!-- 表格搜索组件结束 -->

		<!-- 自定义按钮区域开始 -->

		<!-- 自定义按钮区域结束 -->

		<!-- 表格组件开始 -->
		<vk-data-table ref="table1" :action="table1.action" :columns="table1.columns" :query-form-param="queryForm1" :custom-right-btns="table1.customBtns" :right-btns="['delete']" :pagination="true"
			@delete="deleteBtn" @current-change="currentChange"></vk-data-table>
		<!-- 表格组件结束 -->

		<!-- 添加或编辑的弹窗开始 -->
		<!-- 添加或编辑的弹窗结束 -->

		<!-- 推荐清单列表 -->
		<vk-data-dialog v-model="recommendShow" title="表单标题" width="1000px" mode="form">
			<vk-data-table ref="table2" :data="recommendData" :columns="table1.recommend_columns" :height="400" :right-btns="['detail_auto']" :row-no="false" :pagination="false"></vk-data-table>
			<view style="padding: 10px;box-sizing: border-box;display: flex;align-items: center;justify-content: space-between;">
			</view>
		</vk-data-dialog>
		<!-- 页面内容结束 -->
	</view>
</template>

<script>
	let that; // 当前页面对象
	let vk = uni.vk; // vk实例
	let originalForms = {}; // 表单初始化数据

	export default {
		data() {
			// 页面数据变量
			return {
				recommendShow: false,
				recommendData: [],
				// 页面是否请求中或加载中
				loading: false,
				// init请求返回的数据
				data: {

				},
				// 表格相关开始 -----------------------------------------------------------
				table1: {
					// 表格数据请求地址
					action: "admin/job/recommend/sys/getList",
					// 自定义按钮
					customBtns: [{
						title: "推荐岗位",
						type: "primary",
						onClick: (item) => {
							this.viewRecommend(item)
						}
					}],
					// 表格字段显示规则
					columns: [
						{ key: "user_info.nickname", title: "学生名称", type: "text", },
						{ key: "job_ids.length", title: "岗位数量", type: "text" },
						{ key: "_add_time", title: "生成时间", type: "time", width: 160, sortable: "custom" },
						{ key: "_add_time", title: "距离现在", type: "dateDiff", width: 120 },
					],
					recommend_columns: [
						{ key: "name", title: "岗位信息", type: "text", width: 180, fixed: true },
						{ key: "company_name", title: "企业名称", type: "text", width: 180 },
						{ key: "province_name", title: "省", type: "text" },
						{ key: "city_name", title: "市", type: "text" },
						{ key: "county_name", title: "县", type: "text" },
						{ key: "education", title: "最低学历", type: "text" },
						{
							key: "recruitment_type",
							title: "招聘类型",
							type: "tag",
							width: 150
						},
						{
							key: "graduation_year",
							title: "届别",
							type: "text",
							formatter: (val) => {
								if (val) {
									return val.substring(2, 4) + '届'
								} else {
									return '不限'
								}
							}
						},
						{
							key: "salary",
							title: "薪资",
							type: "text",
							width: 150
						},
						{
							key: "",
							title: "专业要求",
							type: "tag",
							formatter: (val, rows, col) => {
								let major = val.major_info.map(item => item.name)
								let classic = val.major_classic.map(item => item.name)
								let result = [...classic, ...major]
								if (result.length == 0) return ['不限制专业']
								return result
							},
							width: 220
						},
						{ key: "introduce", title: "岗位详情", type: "editor", show: ['detail'] },
						{ key: "original_url", title: "原文链接", type: "text", show: ['detail'] },
						{ key: "application_url", title: "投递链接", type: "text", show: ['detail'] },
						{
							key: "end_date",
							title: "截止时间",
							type: "time",
							width: 180,
							formatter: (val, rows, col) => {
								if (val) return val
								else return '招满即止'
							},
						},
						{ key: "tags", title: "标签", type: "tag", show: ['detail'] },
					],
					// 多选框选中的值
					multipleSelection: [],
					// 当前高亮的记录
					selectItem: ""
				},
				// 表格相关结束 -----------------------------------------------------------
				// 表单相关开始 -----------------------------------------------------------
				// 查询表单请求数据
				queryForm1: {
					// 查询表单数据源，可在此设置默认值
					formData: {

					},
					// 查询表单的字段规则 fieldName:指定数据库字段名,不填默认等于key
					columns: [{
							key: "user_id",
							title: "学生",
							type: "remote-select",
							action: "admin/select/kh/user",
							mode: "=",
							placeholder: "请输入用户账号/昵称",
						},
						{ key: "_add_time", title: "添加时间", type: "datetimerange", width: 400, mode: "[]" },
					]
				},
				// 其他弹窗表单
				formDatas: {},
				// 表单相关结束 -----------------------------------------------------------
			};
		},
		// 监听 - 页面每次【加载时】执行(如：前进)
		onLoad(options = {}) {},
		// 函数
		methods: {
			async viewRecommend(data) {
				let res = await vk.callFunction({
					url: 'admin/job/sys/list',
					title: '获取中...',
					data: {
						ids: data.ids
					},
				});
				this.recommendShow = true
				this.recommendData = res.rows
			},
			// 页面跳转
			pageTo(path) {
				vk.navigateTo(path);
			},
			// 搜索
			search() {
				that.$refs.table1.search();
			},
			// 刷新
			refresh() {
				that.$refs.table1.refresh();
			},
			// 获取当前选中的行的数据
			getCurrentRow() {
				return that.$refs.table1.getCurrentRow();
			},
			// 监听 - 行的选中高亮事件
			currentChange(val) {
				that.table1.selectItem = val;
			},
			// 当选择项发生变化时会触发该事件
			selectionChange(list) {
				that.table1.multipleSelection = list;
			},
			// 删除按钮
			deleteBtn({ item, deleteFn }) {
				deleteFn({
					action: "template/db_api/sys/delete",
					data: {
						_id: item._id
					},
				});
			},
		},
		// 监听属性
		watch: {

		},
		// 计算属性
		computed: {

		}
	};
</script>
<style lang="scss" scoped>
	.page-body {}
</style>