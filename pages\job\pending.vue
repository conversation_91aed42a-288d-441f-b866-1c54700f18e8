<template>
	<view class="page-body">
		<!-- 页面内容开始 -->

		<!-- 表格搜索组件开始 -->
		<vk-data-table-query v-model="queryForm1.formData" :columns="queryForm1.columns" @search="search"></vk-data-table-query>
		<!-- 表格搜索组件结束 -->

		<!-- 自定义按钮区域开始 -->
		<view>
			<el-row>
				<!-- 批量操作 -->
				<el-button type="danger" size="small" :disabled="table1.multipleSelection.length === 0" @click="batchDelByIds">
					<i class="el-icon-delete"></i>批量删除
				</el-button>
			</el-row>
		</view>
		<!-- 自定义按钮区域结束 -->

		<!-- 表格组件开始 -->
		<vk-data-table ref="table1" :action="table1.action" :columns="table1.columns" :query-form-param="queryForm1" :right-btns="[,'update','delete']" :selection="true" :row-no="true"
			:pagination="true" @update="updateBtn" @delete="deleteBtn" @current-change="currentChange" @selection-change="selectionChange">
			<template v-slot:err="{ row, column, index }">
				<div>{{row.err.join(',')}}</div>
			</template>
		</vk-data-table>
		<!-- 表格组件结束 -->

		<!-- 添加或编辑的弹窗开始 -->
		<vk-data-dialog v-model="form1.props.show" :title="form1.props.title" width="900px" mode="form" :close-on-click-modal="false">
			<vk-data-form v-model="form1.data" submit-text="添加至岗位信息" :rules="form1.props.rules" :action="form1.props.action" :form-type="form1.props.formType" :columns='form1.props.columns'
				label-width="120px" @success="form1.props.show = false;refresh();">
				<template v-slot:err="{ form, keyName }">
					<view style="display: flex;align-items: center;">
						<el-tag type="danger" style="margin-right: 10px;" v-for="item in form[keyName]">{{item}}</el-tag>
					</view>
				</template>
			</vk-data-form>
		</vk-data-dialog>
		<!-- 添加或编辑的弹窗结束 -->

		<!-- 页面内容结束 -->
	</view>
</template>

<script>
	let that; // 当前页面对象
	let vk = uni.vk; // vk实例
	let originalForms = {}; // 表单初始化数据

	export default {
		data() {
			// 页面数据变量
			return {
				// 页面是否请求中或加载中
				loading: false,
				// init请求返回的数据
				data: {

				},
				// 表格相关开始 -----------------------------------------------------------
				table1: {
					// 表格数据请求地址
					action: "admin/job/pending/sys/getList",
					// 表格字段显示规则
					columns: [
						{ key: "name", title: "岗位名称", type: "text", width: 180 },
						{ key: "_add_time", title: "添加时间", type: "date", width: 180 },
						{ key: "err", title: "错误信息", type: "text", width: 400 },
					],
					// 多选框选中的值
					multipleSelection: [],
					// 当前高亮的记录
					selectItem: ""
				},
				// 表格相关结束 -----------------------------------------------------------
				// 表单相关开始 -----------------------------------------------------------
				// 查询表单请求数据
				queryForm1: {
					// 查询表单数据源，可在此设置默认值
					formData: {

					},
					// 查询表单的字段规则 fieldName:指定数据库字段名,不填默认等于key
					columns: [
						{ key: "name", title: "岗位名称", type: "text", width: 160, mode: "%%" },
						{ key: "_add_time", title: "添加时间", type: "datetimerange", width: 400, mode: "[]" },
					]
				},
				form1: {
					// 表单请求数据，此处可以设置默认值
					data: {

					},
					// 表单属性
					props: {
						// 表单请求地址
						action: "",
						// 表单字段显示规则
						columns: [
							{ key: "err", title: "错误信息", type: "text" },
							{ key: "name", title: "岗位名称", type: "text" },
							{
								key: "company_name",
								title: "公司",
								type: "text"
							},
							{
								key: "job_property",
								title: "职位性质",
								type: 'tag'
							},
							{
								key: "",
								title: "",
								type: "group",
								justify: "start",
								columns: [{
										key: "province",
										title: "省",
										type: "remote-select",
										placeholder: "请选择省",
										action: "admin/dict/sys/city",
										props: { list: "rows", value: "value", label: "name" },
										showAll: true,
										actionData: {
											pageSize: 1000,
											level: "province",
										},
										watch: ({ value, formData, column, index, option, $set }) => {
											// 此处演示根据选择的值动态改变text1的值
											$set("city", null);
											$set("county", null);
										}
									},
									{
										key: "city",
										title: "市",
										labelWidth: 50,
										type: "remote-select",
										placeholder: "请选择市",
										action: "admin/dict/sys/city",
										props: { list: "rows", value: "value", label: "name" },
										showAll: true,
										actionData: () => {
											return {
												pageSize: 1000,
												level: "city",
												parent_code: this.form1.data.province,
											}
										},
										showRule: () => {
											return !!this.form1.data.province
										},
										watch: ({ value, formData, column, index, option, $set }) => {
											// 此处演示根据选择的值动态改变text1的值
											$set("county", null);
										}
									},
									{
										key: "county",
										title: "县",
										labelWidth: 50,
										type: "remote-select",
										placeholder: "请选择县",
										action: "admin/dict/sys/city",
										props: { list: "rows", value: "value", label: "name" },
										showAll: true,
										actionData: () => {
											return {
												pageSize: 1000,
												level: "county",
												parent_code: this.form1.data.city,
											}
										},
										showRule: () => {
											return !!this.form1.data.city
										}
									},
								]
							},
							{
								key: "education_id",
								title: "学历要求",
								type: "remote-select",
								placeholder: "请选择分类",
								action: "admin/dict/sys/education",
								props: { list: "rows", value: "_id", label: "name" },
								showAll: true,
								actionData: {
									pageSize: 1000,
								}
							},
							{
								key: "recruitment_type",
								title: "招聘类型",
								type: 'tag'
							},
							{
								key: "graduation_year",
								title: "届别",
								type: "date",
								dateType: "year",
								valueFormat: "yyyy",
								format: "yy届",
								placeholder: "请选择届别"
							},
							{
								key: "salary",
								title: "薪资",
								type: "text"
							},
							{
								key: "major_code",
								title: "专业要求",
								type: "remote-select",
								placeholder: "请选择专业",
								action: "admin/major/sys/getList",
								props: { list: "rows", value: "code", label: "name" },
								multiple: true,
								showAll: true,
								actionData: {
									pageSize: 1000,
								}
							},
							{
								key: "major_classic",
								title: "专业类别",
								type: "remote-select",
								placeholder: "专业类别",
								action: "admin/major/classic/sys/get",
								props: { list: "rows", value: "name", label: "name" },
								multiple: true,
								showAll: true,
								actionData: {
									level: 2,
									pageSize: 1000,
								}
							},
							{ key: "introduce", title: "岗位详情", type: "editor" },
							{ key: "original_url", title: "原文链接", type: "text" },
							{ key: "application_url", title: "投递链接", type: "text" },
							{ key: "end_date", title: "截止时间", type: "date", dateType: "datetime" },
							{ key: "tags", title: "标签", type: "tag" },
						],
						// 表单验证规则
						rules: {

						},
						// add 代表添加 update 代表修改
						formType: "",
						// 弹窗标题
						title: "",
						// 是否显示表单的弹窗
						show: false
					}
				},
				// 其他弹窗表单
				formDatas: {},
				// 表单相关结束 -----------------------------------------------------------
			};
		},
		// 监听 - 页面每次【加载时】执行(如：前进)
		onLoad(options = {}) {
			that = this;
			vk = that.vk;
			that.options = options;
			that.init(options);
		},
		// 监听 - 页面【首次渲染完成时】执行。注意如果渲染速度快，会在页面进入动画完成前触发
		onReady() {},
		// 监听 - 页面每次【显示时】执行(如：前进和返回) (页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面)
		onShow() {},
		// 监听 - 页面每次【隐藏时】执行(如：返回)
		onHide() {},
		// 函数
		methods: {
			// 页面数据初始化函数
			init(options) {
				originalForms["form1"] = vk.pubfn.copyObject(that.form1);
			},
			// 页面跳转
			pageTo(path) {
				vk.navigateTo(path);
			},
			// 表单重置
			resetForm() {
				vk.pubfn.resetForm(originalForms, that);
			},
			// 搜索
			search() {
				that.$refs.table1.search();
			},
			// 刷新
			refresh() {
				that.$refs.table1.refresh();
			},
			// 获取当前选中的行的数据
			getCurrentRow() {
				return that.$refs.table1.getCurrentRow();
			},
			// 监听 - 行的选中高亮事件
			currentChange(val) {
				that.table1.selectItem = val;
			},
			// 当选择项发生变化时会触发该事件
			selectionChange(list) {
				that.table1.multipleSelection = list;
			},
			// 显示修改页面
			updateBtn({ item }) {
				that.form1.props.action = 'admin/job/pending/sys/examine';
				that.form1.props.formType = 'update';
				that.form1.props.title = '编辑';
				that.form1.props.show = true;
				that.form1.data = item;
			},
			// 删除按钮
			deleteBtn({ item, deleteFn }) {
				deleteFn({
					action: "admin/job/pending/sys/delete",
					data: {
						_id: item._id
					},
				});
			},
			// 批量删除
			batchDelByIds() {
				let ids = this.table1.multipleSelection.map(item => item._id)
				uni.showModal({
					title: "删除提示",
					content: "确认要删除选中的数据吗?",
					success: async (res) => {
						if (res.confirm) {
							try {
								let data = await vk.callFunction({
									url: 'admin/job/pending/sys/batchDel',
									title: '请求中...',
									data: {
										ids
									},
								});
								this.refresh()
							} catch (err) {
								vk.toast('删除失败');
							}
						}
					}
				})
			}
		},
		// 监听属性
		watch: {

		},
		// 计算属性
		computed: {

		}
	};
</script>
<style lang="scss" scoped>
	.page-body {}
</style>