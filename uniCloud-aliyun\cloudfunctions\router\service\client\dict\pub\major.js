'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/dict/pub/major 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		res = await vk.baseDao.selects({
			dbName: "major-classic",
			getCount: false,
			pageIndex: 1,
			pageSize: 1000,
			// 主表where条件
			whereJson: {
				level: 2
			},
			// 副表列表
			foreignDB: [{
				dbName: "major-classic",
				localKey: "_id",
				foreignKey: "classic_id",
				as: "children",
				limit: 1000,
				foreignDB: [{
					dbName: "major-list",
					localKey: "_id",
					foreignKey: "classic_id",
					as: "children",
					limit: 1000
				}]
			}]
		});



		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}