module.exports = {
	/**
	 * 审核数据添加至正式岗位信息
	 * @url admin/job/pending/sys/examine 前端调用的url参数地址
	 * data 请求参数 说明
	 * res 返回参数说明
	 * @param {Number} code 错误码，0表示成功
	 * @param {String} msg 详细信息
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid } = data;
		let res = { code: 0, msg: 'ok' };
		// 业务逻辑开始-----------------------------------------------------------
		let {
			_id,
			name,
			company_name,
			province,
			city,
			county,
			introduce,
			recruitment_type,
			job_property,
			major,
			education_id,
			end_date,
			salary,
			original_url,
			application_url,
			tags
		} = data;

		if (vk.pubfn.isNullOne(_id)) {
			return { code: -1, msg: '参数错误' };
		}

		let id = await vk.baseDao.add({
			dbName: "jobs",
			dataJson: {
				name,
				company_name,
				province,
				city,
				county,
				introduce,
				recruitment_type,
				job_property,
				major,
				education_id,
				end_date,
				salary,
				original_url,
				application_url,
				tags
			}
		});
		// 添加成功则删除相应的数据
		if (id) {
			await vk.baseDao.deleteById({
				dbName: "jobs-pending",
				id: _id
			});
		}
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}

}