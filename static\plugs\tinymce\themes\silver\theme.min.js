/**
 * TinyMCE version 7.0.0 (2024-03-20)
 */
!function(){"use strict";const e=Object.getPrototypeOf,t=(e,t,o)=>{var n;return!!o(e,t.prototype)||(null===(n=e.constructor)||void 0===n?void 0:n.name)===t.name},o=e=>o=>(e=>{const o=typeof e;return null===e?"null":"object"===o&&Array.isArray(e)?"array":"object"===o&&t(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":o})(o)===e,n=e=>t=>typeof t===e,s=e=>t=>e===t,r=o("string"),a=o("object"),i=o=>((o,n)=>a(o)&&t(o,n,((t,o)=>e(t)===o)))(o,Object),l=o("array"),c=s(null),d=n("boolean"),u=s(void 0),m=e=>null==e,g=e=>!m(e),p=n("function"),h=n("number"),f=(e,t)=>{if(l(e)){for(let o=0,n=e.length;o<n;++o)if(!t(e[o]))return!1;return!0}return!1},b=()=>{},v=e=>()=>e(),y=(e,t)=>(...o)=>e(t.apply(null,o)),x=e=>()=>e,w=e=>e,S=(e,t)=>e===t;function k(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const C=e=>t=>!e(t),O=e=>()=>{throw new Error(e)},_=e=>e(),T=x(!1),E=x(!0);class A{constructor(e,t){this.tag=e,this.value=t}static some(e){return new A(!0,e)}static none(){return A.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?A.some(e(this.value)):A.none()}bind(e){return this.tag?e(this.value):A.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:A.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return g(e)?A.some(e):A.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}A.singletonNone=new A(!1);const M=Array.prototype.slice,D=Array.prototype.indexOf,B=Array.prototype.push,I=(e,t)=>D.call(e,t),F=(e,t)=>{const o=I(e,t);return-1===o?A.none():A.some(o)},R=(e,t)=>I(e,t)>-1,N=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},L=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},z=(e,t)=>{const o=[];for(let n=0;n<e.length;n+=t){const s=M.call(e,n,n+t);o.push(s)}return o},V=(e,t)=>{const o=e.length,n=new Array(o);for(let s=0;s<o;s++){const o=e[s];n[s]=t(o,s)}return n},H=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},P=(e,t)=>{const o=[],n=[];for(let s=0,r=e.length;s<r;s++){const r=e[s];(t(r,s)?o:n).push(r)}return{pass:o,fail:n}},U=(e,t)=>{const o=[];for(let n=0,s=e.length;n<s;n++){const s=e[n];t(s,n)&&o.push(s)}return o},W=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),j=(e,t,o)=>(H(e,((e,n)=>{o=t(o,e,n)})),o),G=(e,t)=>((e,t,o)=>{for(let n=0,s=e.length;n<s;n++){const s=e[n];if(t(s,n))return A.some(s);if(o(s,n))break}return A.none()})(e,t,T),$=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return A.some(o);return A.none()},q=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);B.apply(t,e[o])}return t},Y=(e,t)=>q(V(e,t)),X=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},K=e=>{const t=M.call(e,0);return t.reverse(),t},J=(e,t)=>U(e,(e=>!R(t,e))),Z=(e,t)=>{const o={};for(let n=0,s=e.length;n<s;n++){const s=e[n];o[String(s)]=t(s,n)}return o},Q=e=>[e],ee=(e,t)=>{const o=M.call(e,0);return o.sort(t),o},te=(e,t)=>t>=0&&t<e.length?A.some(e[t]):A.none(),oe=e=>te(e,0),ne=e=>te(e,e.length-1),se=p(Array.from)?Array.from:e=>M.call(e),re=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return A.none()},ae=Object.keys,ie=Object.hasOwnProperty,le=(e,t)=>{const o=ae(e);for(let n=0,s=o.length;n<s;n++){const s=o[n];t(e[s],s)}},ce=(e,t)=>de(e,((e,o)=>({k:o,v:t(e,o)}))),de=(e,t)=>{const o={};return le(e,((e,n)=>{const s=t(e,n);o[s.k]=s.v})),o},ue=e=>(t,o)=>{e[o]=t},me=(e,t,o,n)=>{le(e,((e,s)=>{(t(e,s)?o:n)(e,s)}))},ge=(e,t)=>{const o={};return me(e,t,ue(o),b),o},pe=(e,t)=>{const o=[];return le(e,((e,n)=>{o.push(t(e,n))})),o},he=(e,t)=>{const o=ae(e);for(let n=0,s=o.length;n<s;n++){const s=o[n],r=e[s];if(t(r,s,e))return A.some(r)}return A.none()},fe=e=>pe(e,w),be=(e,t)=>ve(e,t)?A.from(e[t]):A.none(),ve=(e,t)=>ie.call(e,t),ye=(e,t)=>ve(e,t)&&void 0!==e[t]&&null!==e[t],xe=(e,t,o=S)=>e.exists((e=>o(e,t))),we=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},Se=(e,t,o)=>e.isSome()&&t.isSome()?A.some(o(e.getOrDie(),t.getOrDie())):A.none(),ke=(e,t)=>null!=e?A.some(t(e)):A.none(),Ce=(e,t)=>e?A.some(t):A.none(),Oe=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,_e=(e,t)=>Ee(e,t)?((e,t)=>e.substring(t))(e,t.length):e,Te=(e,t,o=0,n)=>{const s=e.indexOf(t,o);return-1!==s&&(!!u(n)||s+t.length<=n)},Ee=(e,t)=>Oe(e,t,0),Ae=(e,t)=>Oe(e,t,e.length-t.length),Me=(Do=/^\s+|\s+$/g,e=>e.replace(Do,"")),De=e=>e.length>0,Be=e=>!De(e),Ie=e=>void 0!==e.style&&p(e.style.getPropertyValue),Fe=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},Re=(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return Fe(o.childNodes[0])},Ne=(e,t)=>{const o=(t||document).createElement(e);return Fe(o)},Le=(e,t)=>{const o=(t||document).createTextNode(e);return Fe(o)},ze=Fe,Ve="undefined"!=typeof window?window:Function("return this;")(),He=(e,t)=>((e,t)=>{let o=null!=t?t:Ve;for(let t=0;t<e.length&&null!=o;++t)o=o[e[t]];return o})(e.split("."),t),Pe=Object.getPrototypeOf,Ue=e=>{const t=He("ownerDocument.defaultView",e);return a(e)&&((e=>((e,t)=>{const o=((e,t)=>He(e,t))(e,t);if(null==o)throw new Error(e+" not available on this browser");return o})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(Pe(e).constructor.name))},We=e=>e.dom.nodeName.toLowerCase(),je=e=>t=>(e=>e.dom.nodeType)(t)===e,Ge=e=>$e(e)&&Ue(e.dom),$e=je(1),qe=je(3),Ye=je(9),Xe=je(11),Ke=e=>t=>$e(t)&&We(t)===e,Je=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},Ze=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,Qe=(e,t)=>{const o=void 0===t?document:t.dom;return Ze(o)?A.none():A.from(o.querySelector(e)).map(ze)},et=(e,t)=>e.dom===t.dom,tt=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},ot=e=>ze(e.dom.ownerDocument),nt=e=>Ye(e)?e:ot(e),st=e=>ze(nt(e).dom.documentElement),rt=e=>ze(nt(e).dom.defaultView),at=e=>A.from(e.dom.parentNode).map(ze),it=e=>A.from(e.dom.parentElement).map(ze),lt=e=>A.from(e.dom.offsetParent).map(ze),ct=e=>V(e.dom.childNodes,ze),dt=(e,t)=>{const o=e.dom.childNodes;return A.from(o[t]).map(ze)},ut=e=>dt(e,0),mt=(e,t)=>({element:e,offset:t}),gt=(e,t)=>{const o=ct(e);return o.length>0&&t<o.length?mt(o[t],0):mt(e,t)},pt=e=>Xe(e)&&g(e.dom.host),ht=p(Element.prototype.attachShadow)&&p(Node.prototype.getRootNode),ft=x(ht),bt=ht?e=>ze(e.dom.getRootNode()):nt,vt=e=>pt(e)?e:ze(nt(e).dom.body),yt=e=>{const t=bt(e);return pt(t)?A.some(t):A.none()},xt=e=>ze(e.dom.host),wt=e=>{const t=qe(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return yt(ze(t)).fold((()=>o.body.contains(t)),(n=wt,s=xt,e=>n(s(e))));var n,s},St=()=>kt(ze(document)),kt=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return ze(t)},Ct=(e,t,o)=>{if(!(r(o)||d(o)||h(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},Ot=(e,t,o)=>{Ct(e.dom,t,o)},_t=(e,t)=>{const o=e.dom;le(t,((e,t)=>{Ct(o,t,e)}))},Tt=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},Et=(e,t)=>A.from(Tt(e,t)),At=(e,t)=>{const o=e.dom;return!(!o||!o.hasAttribute)&&o.hasAttribute(t)},Mt=(e,t)=>{e.dom.removeAttribute(t)},Dt=(e,t,o)=>{if(!r(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);Ie(e)&&e.style.setProperty(t,o)},Bt=(e,t)=>{Ie(e)&&e.style.removeProperty(t)},It=(e,t,o)=>{const n=e.dom;Dt(n,t,o)},Ft=(e,t)=>{const o=e.dom;le(t,((e,t)=>{Dt(o,t,e)}))},Rt=(e,t)=>{const o=e.dom;le(t,((e,t)=>{e.fold((()=>{Bt(o,t)}),(e=>{Dt(o,t,e)}))}))},Nt=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||wt(e)?n:Lt(o,t)},Lt=(e,t)=>Ie(e)?e.style.getPropertyValue(t):"",zt=(e,t)=>{const o=e.dom,n=Lt(o,t);return A.from(n).filter((e=>e.length>0))},Vt=e=>{const t={},o=e.dom;if(Ie(o))for(let e=0;e<o.style.length;e++){const n=o.style.item(e);t[n]=o.style[n]}return t},Ht=(e,t,o)=>{const n=Ne(e);return It(n,t,o),zt(n,t).isSome()},Pt=(e,t)=>{const o=e.dom;Bt(o,t),xe(Et(e,"style").map(Me),"")&&Mt(e,"style")},Ut=e=>e.dom.offsetWidth,Wt=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=Nt(o,e);return parseFloat(t)||0}return n},n=(e,t)=>j(t,((t,o)=>{const n=Nt(e,o),s=void 0===n?0:parseInt(n,10);return isNaN(s)?t:t+s}),0);return{set:(t,o)=>{if(!h(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;Ie(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const s=n(e,o);return t>s?t-s:0}}},jt=Wt("height",(e=>{const t=e.dom;return wt(e)?t.getBoundingClientRect().height:t.offsetHeight})),Gt=e=>jt.get(e),$t=e=>jt.getOuter(e),qt=(e,t)=>({left:e,top:t,translate:(o,n)=>qt(e+o,t+n)}),Yt=qt,Xt=(e,t)=>void 0!==e?e:void 0!==t?t:0,Kt=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,s=t.documentElement;if(o===e.dom)return Yt(o.offsetLeft,o.offsetTop);const r=Xt(null==n?void 0:n.pageYOffset,s.scrollTop),a=Xt(null==n?void 0:n.pageXOffset,s.scrollLeft),i=Xt(s.clientTop,o.clientTop),l=Xt(s.clientLeft,o.clientLeft);return Jt(e).translate(a-l,r-i)},Jt=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?Yt(o.offsetLeft,o.offsetTop):wt(e)?(e=>{const t=e.getBoundingClientRect();return Yt(t.left,t.top)})(t):Yt(0,0)},Zt=Wt("width",(e=>e.dom.offsetWidth)),Qt=e=>Zt.get(e),eo=e=>Zt.getOuter(e),to=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},oo=()=>no(0,0),no=(e,t)=>({major:e,minor:t}),so={nu:no,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?oo():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return no(n(1),n(2))})(e,o)},unknown:oo},ro=(e,t)=>{const o=String(t).toLowerCase();return G(e,(e=>e.search(o)))},ao=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,io=e=>t=>Te(t,e),lo=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>Te(e,"edge/")&&Te(e,"chrome")&&Te(e,"safari")&&Te(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,ao],search:e=>Te(e,"chrome")&&!Te(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>Te(e,"msie")||Te(e,"trident")},{name:"Opera",versionRegexes:[ao,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:io("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:io("firefox")},{name:"Safari",versionRegexes:[ao,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(Te(e,"safari")||Te(e,"mobile/"))&&Te(e,"applewebkit")}],co=[{name:"Windows",search:io("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>Te(e,"iphone")||Te(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:io("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:io("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:io("linux"),versionRegexes:[]},{name:"Solaris",search:io("sunos"),versionRegexes:[]},{name:"FreeBSD",search:io("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:io("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],uo={browsers:x(lo),oses:x(co)},mo="Edge",go="Chromium",po="Opera",ho="Firefox",fo="Safari",bo=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(mo),isChromium:n(go),isIE:n("IE"),isOpera:n(po),isFirefox:n(ho),isSafari:n(fo)}},vo=()=>bo({current:void 0,version:so.unknown()}),yo=bo,xo=(x(mo),x(go),x("IE"),x(po),x(ho),x(fo),"Windows"),wo="Android",So="Linux",ko="macOS",Co="Solaris",Oo="FreeBSD",_o="ChromeOS",To=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(xo),isiOS:n("iOS"),isAndroid:n(wo),isMacOS:n(ko),isLinux:n(So),isSolaris:n(Co),isFreeBSD:n(Oo),isChromeOS:n(_o)}},Eo=()=>To({current:void 0,version:so.unknown()}),Ao=To,Mo=(x(xo),x("iOS"),x(wo),x(So),x(ko),x(Co),x(Oo),x(_o),e=>window.matchMedia(e).matches);var Do;let Bo=to((()=>((e,t,o)=>{const n=uo.browsers(),s=uo.oses(),r=t.bind((e=>((e,t)=>re(t.brands,(t=>{const o=t.brand.toLowerCase();return G(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:so.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>ro(e,t).map((e=>{const o=so.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(vo,yo),a=((e,t)=>ro(e,t).map((e=>{const o=so.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(s,e).fold(Eo,Ao),i=((e,t,o,n)=>{const s=e.isiOS()&&!0===/ipad/i.test(o),r=e.isiOS()&&!s,a=e.isiOS()||e.isAndroid(),i=a||n("(pointer:coarse)"),l=s||!r&&a&&n("(min-device-width:768px)"),c=r||a&&!l,d=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),u=!c&&!l&&!d;return{isiPad:x(s),isiPhone:x(r),isTablet:x(l),isPhone:x(c),isTouch:x(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:x(d),isDesktop:x(u)}})(a,r,e,o);return{browser:r,os:a,deviceType:i}})(navigator.userAgent,A.from(navigator.userAgentData),Mo)));const Io=()=>Bo(),Fo=e=>{const t=ze((e=>{if(ft()&&g(e.target)){const t=ze(e.target);if($e(t)&&(e=>g(e.dom.shadowRoot))(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return oe(t)}}return A.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),s=y(n,o);return((e,t,o,n,s,r,a)=>({target:e,x:t,y:o,stop:n,prevent:s,kill:r,raw:a}))(t,e.clientX,e.clientY,o,n,s,e)},Ro=(e,t,o,n,s)=>{const r=((e,t)=>o=>{e(o)&&t(Fo(o))})(o,n);return e.dom.addEventListener(t,r,s),{unbind:k(No,e,t,r,s)}},No=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},Lo=(e,t)=>{at(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},zo=(e,t)=>{const o=(e=>A.from(e.dom.nextSibling).map(ze))(e);o.fold((()=>{at(e).each((e=>{Ho(e,t)}))}),(e=>{Lo(e,t)}))},Vo=(e,t)=>{ut(e).fold((()=>{Ho(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},Ho=(e,t)=>{e.dom.appendChild(t.dom)},Po=(e,t)=>{H(t,(t=>{Ho(e,t)}))},Uo=e=>{e.dom.textContent="",H(ct(e),(e=>{Wo(e)}))},Wo=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},jo=e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return Yt(o,n)},Go=(e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollTo(e,t)},$o=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),qo=e=>{const t=void 0===e?window:e,o=t.document,n=jo(ze(o));return(e=>{const t=void 0===e?window:e;return Io().browser.isFirefox()?A.none():A.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,o=e.clientWidth,s=e.clientHeight;return $o(n.left,n.top,o,s)}),(e=>$o(Math.max(e.pageLeft,n.left),Math.max(e.pageTop,n.top),e.width,e.height)))},Yo=()=>ze(document),Xo=(e,t)=>e.view(t).fold(x([]),(t=>{const o=e.owner(t),n=Xo(e,o);return[t].concat(n)}));var Ko=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?A.none():A.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(ze)},owner:e=>ot(e)});const Jo=e=>{const t=Yo(),o=jo(t),n=((e,t)=>{const o=t.owner(e),n=Xo(t,o);return A.some(n)})(e,Ko);return n.fold(k(Kt,e),(t=>{const n=Jt(e),s=W(t,((e,t)=>{const o=Jt(t);return{left:e.left+o.left,top:e.top+o.top}}),{left:0,top:0});return Yt(s.left+n.left+o.left,s.top+n.top+o.top)}))},Zo=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Qo=e=>{const t=Kt(e),o=eo(e),n=$t(e);return Zo(t.left,t.top,o,n)},en=e=>{const t=Jo(e),o=eo(e),n=$t(e);return Zo(t.left,t.top,o,n)},tn=(e,t)=>{const o=Math.max(e.x,t.x),n=Math.max(e.y,t.y),s=Math.min(e.right,t.right),r=Math.min(e.bottom,t.bottom);return Zo(o,n,s-o,r-n)},on=()=>qo(window);var nn=tinymce.util.Tools.resolve("tinymce.ThemeManager");const sn=e=>{const t=t=>t(e),o=x(e),n=()=>s,s={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:E,isError:T,map:t=>an.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>A.some(e)};return s},rn=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:T,isError:E,map:t,mapError:t=>an.error(t(e)),bind:t,exists:T,forall:E,getOr:w,or:w,getOrThunk:_,orThunk:_,getOrDie:O(String(e)),each:b,toOptional:A.none};return o},an={value:sn,error:rn,fromOption:(e,t)=>e.fold((()=>rn(t)),sn)};var ln;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(ln||(ln={}));const cn=(e,t,o)=>e.stype===ln.Error?t(e.serror):o(e.svalue),dn=e=>({stype:ln.Value,svalue:e}),un=e=>({stype:ln.Error,serror:e}),mn=dn,gn=un,pn=cn,hn=(e,t,o,n)=>({tag:"field",key:e,newKey:t,presence:o,prop:n}),fn=(e,t,o)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return o(e.newKey,e.instantiator)}},bn=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const o={};for(let n=0;n<t.length;n++){const s=t[n];for(const t in s)ve(s,t)&&(o[t]=e(o[t],s[t]))}return o},vn=bn(((e,t)=>i(e)&&i(t)?vn(e,t):t)),yn=bn(((e,t)=>t)),xn=e=>({tag:"defaultedThunk",process:e}),wn=e=>xn(x(e)),Sn=e=>({tag:"mergeWithThunk",process:e}),kn=e=>{const t=(e=>{const t=[],o=[];return H(e,(e=>{cn(e,(e=>o.push(e)),(e=>t.push(e)))})),{values:t,errors:o}})(e);return t.errors.length>0?(o=t.errors,y(gn,q)(o)):mn(t.values);var o},Cn=e=>a(e)&&ae(e).length>100?" removed due to size":JSON.stringify(e,null,2),On=(e,t)=>gn([{path:e,getErrorInfo:t}]),_n=e=>({extract:(t,o)=>((e,t)=>e.stype===ln.Error?t(e.serror):e)(e(o),(e=>((e,t)=>On(e,x(t)))(t,e))),toString:x("val")}),Tn=_n(mn),En=(e,t,o,n)=>n(be(e,t).getOrThunk((()=>o(e)))),An=(e,t,o,n,s)=>{const r=e=>s.extract(t.concat([n]),e),a=e=>e.fold((()=>mn(A.none())),(e=>((e,t)=>e.stype===ln.Value?{stype:ln.Value,svalue:t(e.svalue)}:e)(s.extract(t.concat([n]),e),A.some)));switch(e.tag){case"required":return((e,t,o,n)=>be(t,o).fold((()=>((e,t,o)=>On(e,(()=>'Could not find valid *required* value for "'+t+'" in '+Cn(o))))(e,o,t)),n))(t,o,n,r);case"defaultedThunk":return En(o,n,e.process,r);case"option":return((e,t,o)=>o(be(e,t)))(o,n,a);case"defaultedOptionThunk":return((e,t,o,n)=>n(be(e,t).map((t=>!0===t?o(e):t))))(o,n,e.process,a);case"mergeWithThunk":return En(o,n,x({}),(t=>{const n=vn(e.process(o),t);return r(n)}))}},Mn=e=>({extract:(t,o)=>e().extract(t,o),toString:()=>e().toString()}),Dn=e=>ae(ge(e,g)),Bn=e=>{const t=In(e),o=W(e,((e,t)=>fn(t,(t=>vn(e,{[t]:!0})),x(e))),{});return{extract:(e,n)=>{const s=d(n)?[]:Dn(n),r=U(s,(e=>!ye(o,e)));return 0===r.length?t.extract(e,n):((e,t)=>On(e,(()=>"There are unsupported fields: ["+t.join(", ")+"] specified")))(e,r)},toString:t.toString}},In=e=>({extract:(t,o)=>((e,t,o)=>{const n={},s=[];for(const r of o)fn(r,((o,r,a,i)=>{const l=An(a,e,t,o,i);pn(l,(e=>{s.push(...e)}),(e=>{n[r]=e}))}),((e,o)=>{n[e]=o(t)}));return s.length>0?gn(s):mn(n)})(t,o,e),toString:()=>{const t=V(e,(e=>fn(e,((e,t,o,n)=>e+" -> "+n.toString()),((e,t)=>"state("+e+")"))));return"obj{\n"+t.join("\n")+"}"}}),Fn=e=>({extract:(t,o)=>{const n=V(o,((o,n)=>e.extract(t.concat(["["+n+"]"]),o)));return kn(n)},toString:()=>"array("+e.toString()+")"}),Rn=(e,t)=>{const o=void 0!==t?t:w;return{extract:(t,n)=>{const s=[];for(const r of e){const e=r.extract(t,n);if(e.stype===ln.Value)return{stype:ln.Value,svalue:o(e.svalue)};s.push(e)}return kn(s)},toString:()=>"oneOf("+V(e,(e=>e.toString())).join(", ")+")"}},Nn=(e,t)=>({extract:(o,n)=>{const s=ae(n),r=((t,o)=>Fn(_n(e)).extract(t,o))(o,s);return((e,t)=>e.stype===ln.Value?t(e.svalue):e)(r,(e=>{const s=V(e,(e=>hn(e,e,{tag:"required",process:{}},t)));return In(s).extract(o,n)}))},toString:()=>"setOf("+t.toString()+")"}),Ln=y(Fn,In),zn=x(Tn),Vn=(e,t)=>_n((o=>{const n=typeof o;return e(o)?mn(o):gn(`Expected type: ${t} but got: ${n}`)})),Hn=Vn(h,"number"),Pn=Vn(r,"string"),Un=Vn(d,"boolean"),Wn=Vn(p,"function"),jn=e=>{if(Object(e)!==e)return!0;switch({}.toString.call(e).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(e).every((t=>jn(e[t])));default:return!1}},Gn=_n((e=>jn(e)?mn(e):gn("Expected value to be acceptable for sending via postMessage"))),$n=(e,t)=>({extract:(o,n)=>be(n,e).fold((()=>((e,t)=>On(e,(()=>'Choice schema did not contain choice key: "'+t+'"')))(o,e)),(e=>((e,t,o,n)=>be(o,n).fold((()=>((e,t,o)=>On(e,(()=>'The chosen schema: "'+o+'" did not exist in branches: '+Cn(t))))(e,o,n)),(o=>o.extract(e.concat(["branch: "+n]),t))))(o,n,t,e))),toString:()=>"chooseOn("+e+"). Possible values: "+ae(t)}),qn=e=>_n((t=>e(t).fold(gn,mn))),Yn=(e,t)=>Nn((t=>e(t).fold(un,dn)),t),Xn=(e,t,o)=>{return n=((e,t,o)=>((e,t)=>e.stype===ln.Error?{stype:ln.Error,serror:t(e.serror)}:e)(t.extract([e],o),(e=>({input:o,errors:e}))))(e,t,o),cn(n,an.error,an.value);var n},Kn=e=>e.fold((e=>{throw new Error(Zn(e))}),w),Jn=(e,t,o)=>Kn(Xn(e,t,o)),Zn=e=>"Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:x("... (only showing first ten failures)")}]):e;return V(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})(e.errors).join("\n")+"\n\nInput object: "+Cn(e.input),Qn=(e,t)=>$n(e,ce(t,In)),es=(e,t)=>((e,t)=>{const o=to(t);return{extract:(e,t)=>o().extract(e,t),toString:()=>o().toString()}})(0,t),ts=hn,os=(e,t)=>({tag:"custom",newKey:e,instantiator:t}),ns=e=>qn((t=>R(e,t)?an.value(t):an.error(`Unsupported value: "${t}", choose one of "${e.join(", ")}".`))),ss=e=>ts(e,e,{tag:"required",process:{}},zn()),rs=(e,t)=>ts(e,e,{tag:"required",process:{}},t),as=e=>rs(e,Hn),is=e=>rs(e,Pn),ls=(e,t)=>ts(e,e,{tag:"required",process:{}},ns(t)),cs=e=>rs(e,Wn),ds=(e,t)=>ts(e,e,{tag:"required",process:{}},In(t)),us=(e,t)=>ts(e,e,{tag:"required",process:{}},Ln(t)),ms=(e,t)=>ts(e,e,{tag:"required",process:{}},Fn(t)),gs=e=>ts(e,e,{tag:"option",process:{}},zn()),ps=(e,t)=>ts(e,e,{tag:"option",process:{}},t),hs=e=>ps(e,Hn),fs=e=>ps(e,Pn),bs=(e,t)=>ps(e,ns(t)),vs=e=>ps(e,Wn),ys=(e,t)=>ps(e,Fn(t)),xs=(e,t)=>ps(e,In(t)),ws=(e,t)=>ts(e,e,wn(t),zn()),Ss=(e,t,o)=>ts(e,e,wn(t),o),ks=(e,t)=>Ss(e,t,Hn),Cs=(e,t)=>Ss(e,t,Pn),Os=(e,t,o)=>Ss(e,t,ns(o)),_s=(e,t)=>Ss(e,t,Un),Ts=(e,t)=>Ss(e,t,Wn),Es=(e,t,o)=>Ss(e,t,Fn(o)),As=(e,t,o)=>Ss(e,t,In(o)),Ms=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},Ds=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return H(e,((n,s)=>{const r=ae(n);if(1!==r.length)throw new Error("one and only one name per case");const a=r[0],i=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(i))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[s].apply(null,o)},match:e=>{const n=ae(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!X(t,(e=>R(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o};Ds([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const Bs=(e,t)=>((e,t)=>({[e]:t}))(e,t),Is=e=>(e=>{const t={};return H(e,(e=>{t[e.key]=e.value})),t})(e),Fs=e=>p(e)?e:T,Rs=(e,t,o)=>{let n=e.dom;const s=Fs(o);for(;n.parentNode;){n=n.parentNode;const e=ze(n),o=t(e);if(o.isSome())return o;if(s(e))break}return A.none()},Ns=(e,t,o)=>{const n=t(e),s=Fs(o);return n.orThunk((()=>s(e)?A.none():Rs(e,t,s)))},Ls=(e,t)=>et(e.element,t.event.target),zs={can:E,abort:T,run:b},Vs=e=>{if(!ye(e,"can")&&!ye(e,"abort")&&!ye(e,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(e,null,2)+" does not have can, abort, or run!");return{...zs,...e}},Hs=x,Ps=Hs("touchstart"),Us=Hs("touchmove"),Ws=Hs("touchend"),js=Hs("touchcancel"),Gs=Hs("mousedown"),$s=Hs("mousemove"),qs=Hs("mouseout"),Ys=Hs("mouseup"),Xs=Hs("mouseover"),Ks=Hs("focusin"),Js=Hs("focusout"),Zs=Hs("keydown"),Qs=Hs("keyup"),er=Hs("input"),tr=Hs("change"),or=Hs("click"),nr=Hs("transitioncancel"),sr=Hs("transitionend"),rr=Hs("transitionstart"),ar=Hs("selectstart"),ir=e=>x("alloy."+e),lr={tap:ir("tap")},cr=ir("focus"),dr=ir("blur.post"),ur=ir("paste.post"),mr=ir("receive"),gr=ir("execute"),pr=ir("focus.item"),hr=lr.tap,fr=ir("longpress"),br=ir("sandbox.close"),vr=ir("typeahead.cancel"),yr=ir("system.init"),xr=ir("system.touchmove"),wr=ir("system.touchend"),Sr=ir("system.scroll"),kr=ir("system.resize"),Cr=ir("system.attached"),Or=ir("system.detached"),_r=ir("system.dismissRequested"),Tr=ir("system.repositionRequested"),Er=ir("focusmanager.shifted"),Ar=ir("slotcontainer.visibility"),Mr=ir("system.external.element.scroll"),Dr=ir("change.tab"),Br=ir("dismiss.tab"),Ir=ir("highlight"),Fr=ir("dehighlight"),Rr=(e,t)=>{Vr(e,e.element,t,{})},Nr=(e,t,o)=>{Vr(e,e.element,t,o)},Lr=e=>{Rr(e,gr())},zr=(e,t,o)=>{Vr(e,t,o,{})},Vr=(e,t,o,n)=>{const s={target:t,...n};e.getSystem().triggerEvent(o,t,s)},Hr=(e,t,o,n)=>{e.getSystem().triggerEvent(o,t,n.event)},Pr=e=>Is(e),Ur=(e,t)=>({key:e,value:Vs({abort:t})}),Wr=e=>({key:e,value:Vs({run:(e,t)=>{t.event.prevent()}})}),jr=(e,t)=>({key:e,value:Vs({run:t})}),Gr=(e,t,o)=>({key:e,value:Vs({run:(e,n)=>{t.apply(void 0,[e,n].concat(o))}})}),$r=e=>t=>({key:e,value:Vs({run:(e,o)=>{Ls(e,o)&&t(e,o)}})}),qr=(e,t,o)=>((e,t)=>jr(e,((o,n)=>{o.getSystem().getByUid(t).each((t=>{Hr(t,t.element,e,n)}))})))(e,t.partUids[o]),Yr=(e,t)=>jr(e,((e,o)=>{const n=o.event,s=e.getSystem().getByDom(n.target).getOrThunk((()=>Ns(n.target,(t=>e.getSystem().getByDom(t).toOptional()),T).getOr(e)));t(e,s,o)})),Xr=e=>jr(e,((e,t)=>{t.cut()})),Kr=e=>jr(e,((e,t)=>{t.stop()})),Jr=(e,t)=>$r(e)(t),Zr=$r(Cr()),Qr=$r(Or()),ea=$r(yr()),ta=(ia=gr(),e=>jr(ia,e)),oa=e=>e.dom.innerHTML,na=(e,t)=>{const o=ot(e).dom,n=ze(o.createDocumentFragment()),s=((e,t)=>{const o=(t||document).createElement("div");return o.innerHTML=e,ct(ze(o))})(t,o);Po(n,s),Uo(e),Ho(e,n)},sa=(e,t)=>ze(e.dom.cloneNode(t)),ra=e=>(e=>{if(pt(e))return"#shadow-root";{const t=(e=>sa(e,!1))(e);return(e=>{const t=Ne("div"),o=ze(e.dom.cloneNode(!0));return Ho(t,o),oa(t)})(t)}})(e),aa=Pr([((e,t)=>({key:e,value:Vs({can:(e,t)=>{const o=t.event,n=o.originator,s=o.target;return!((e,t,o)=>et(t,e.element)&&!et(t,o))(e,n,s)||(console.warn(cr()+" did not get interpreted by the desired target. \nOriginator: "+ra(n)+"\nTarget: "+ra(s)+"\nCheck the "+cr()+" event handlers"),!1)}})}))(cr())]);var ia,la=Object.freeze({__proto__:null,events:aa});let ca=0;const da=e=>{const t=(new Date).getTime(),o=Math.floor(1e9*Math.random());return ca++,e+"_"+o+ca+String(t)},ua=x("alloy-id-"),ma=x("data-alloy-id"),ga=ua(),pa=ma(),ha=(e,t)=>{Object.defineProperty(e.dom,pa,{value:t,writable:!0})},fa=e=>{const t=$e(e)?e.dom[pa]:null;return A.from(t)},ba=e=>da(e),va=w,ya=e=>{const t=t=>`The component must be in a context to execute: ${t}`+(e?"\n"+ra(e().element)+" is not in context.":""),o=e=>()=>{throw new Error(t(e))},n=e=>()=>{console.warn(t(e))};return{debugInfo:x("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),build:o("build"),buildOrPatch:o("buildOrPatch"),addToWorld:o("addToWorld"),removeFromWorld:o("removeFromWorld"),addToGui:o("addToGui"),removeFromGui:o("removeFromGui"),getByUid:o("getByUid"),getByDom:o("getByDom"),isConnected:T}},xa=ya(),wa=e=>V(e,(e=>Ae(e,"/*")?e.substring(0,e.length-2):e)),Sa=(e,t)=>{const o=e.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:wa(r)}),e},ka=da("alloy-premade"),Ca=e=>(Object.defineProperty(e.element.dom,ka,{value:e.uid,writable:!0}),Bs(ka,e)),Oa=e=>be(e,ka),_a=e=>((e,t)=>{const o=t.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:"OVERRIDE",parameters:wa(r.slice(1))}),e})(((t,...o)=>e(t.getApis(),t,...o)),e),Ta={init:()=>Ea({readState:x("No State required")})},Ea=e=>e,Aa=(e,t)=>{const o={};return le(e,((e,n)=>{le(e,((e,s)=>{const r=be(o,s).getOr([]);o[s]=r.concat([t(n,e)])}))})),o},Ma=e=>({classes:u(e.classes)?[]:e.classes,attributes:u(e.attributes)?{}:e.attributes,styles:u(e.styles)?{}:e.styles}),Da=e=>e.cHandler,Ba=(e,t)=>({name:e,handler:t}),Ia=(e,t)=>{const o={};return H(e,(e=>{o[e.name()]=e.handlers(t)})),o},Fa=(e,t,o)=>{const n=t[o];return n?((e,t,o,n)=>{try{const s=ee(o,((o,s)=>{const r=o[t],a=s[t],i=n.indexOf(r),l=n.indexOf(a);if(-1===i)throw new Error("The ordering for "+e+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(n,null,2));if(-1===l)throw new Error("The ordering for "+e+" does not have an entry for "+a+".\nOrder specified: "+JSON.stringify(n,null,2));return i<l?-1:l<i?1:0}));return an.value(s)}catch(e){return an.error([e])}})("Event: "+o,"name",e,n).map((e=>(e=>{const t=((e,t)=>(...t)=>j(e,((e,o)=>e&&(e=>e.can)(o).apply(void 0,t)),!0))(e),o=((e,t)=>(...t)=>j(e,((e,o)=>e||(e=>e.abort)(o).apply(void 0,t)),!1))(e);return{can:t,abort:o,run:(...t)=>{H(e,(e=>{e.run.apply(void 0,t)}))}}})(V(e,(e=>e.handler))))):((e,t)=>an.error(["The event ("+e+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(V(t,(e=>e.name)),null,2)]))(o,e)},Ra=(e,t)=>((e,t)=>{const o=(e=>{const t=[],o=[];return H(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{o.push(e)}))})),{errors:t,values:o}})(e);return o.errors.length>0?(n=o.errors,an.error(q(n))):((e,t)=>0===e.length?an.value(t):an.value(vn(t,yn.apply(void 0,e))))(o.values,t);var n})(pe(e,((e,o)=>(1===e.length?an.value(e[0].handler):Fa(e,t,o)).map((n=>{const s=(e=>{const t=(e=>p(e)?{can:E,abort:T,run:e}:e)(e);return(e,o,...n)=>{const s=[e,o].concat(n);t.abort.apply(void 0,s)?o.stop():t.can.apply(void 0,s)&&t.run.apply(void 0,s)}})(n),r=e.length>1?U(t[o],(t=>N(e,(e=>e.name===t)))).join(" > "):e[0].name;return Bs(o,((e,t)=>({handler:e,purpose:t}))(s,r))})))),{}),Na="alloy.base.behaviour",La=In([ts("dom","dom",{tag:"required",process:{}},In([ss("tag"),ws("styles",{}),ws("classes",[]),ws("attributes",{}),gs("value"),gs("innerHtml")])),ss("components"),ss("uid"),ws("events",{}),ws("apis",{}),ts("eventOrder","eventOrder",(mi={[gr()]:["disabling",Na,"toggling","typeaheadevents"],[cr()]:[Na,"focusing","keying"],[yr()]:[Na,"disabling","toggling","representing"],[er()]:[Na,"representing","streaming","invalidating"],[Or()]:[Na,"representing","item-events","toolbar-button-events","tooltipping"],[Gs()]:["focusing",Na,"item-type-events"],[Ps()]:["focusing",Na,"item-type-events"],[Xs()]:["item-type-events","tooltipping"],[mr()]:["receiving","reflecting","tooltipping"]},Sn(x(mi))),zn()),gs("domModification")]),za=e=>e.events,Va=(e,t)=>{const o=Tt(e,t);return void 0===o||""===o?[]:o.split(" ")},Ha=e=>void 0!==e.dom.classList,Pa=e=>Va(e,"class"),Ua=(e,t)=>((e,t,o)=>{const n=Va(e,t).concat([o]);return Ot(e,t,n.join(" ")),!0})(e,"class",t),Wa=(e,t)=>((e,t,o)=>{const n=U(Va(e,t),(e=>e!==o));return n.length>0?Ot(e,t,n.join(" ")):Mt(e,t),!1})(e,"class",t),ja=(e,t)=>{Ha(e)?e.dom.classList.add(t):Ua(e,t)},Ga=e=>{0===(Ha(e)?e.dom.classList:Pa(e)).length&&Mt(e,"class")},$a=(e,t)=>{Ha(e)?e.dom.classList.remove(t):Wa(e,t),Ga(e)},qa=(e,t)=>Ha(e)&&e.dom.classList.contains(t),Ya=(e,t)=>{H(t,(t=>{ja(e,t)}))},Xa=(e,t)=>{H(t,(t=>{$a(e,t)}))},Ka=e=>Ha(e)?(e=>{const t=e.dom.classList,o=new Array(t.length);for(let e=0;e<t.length;e++){const n=t.item(e);null!==n&&(o[e]=n)}return o})(e):Pa(e),Ja=e=>e.dom.value,Za=(e,t)=>{if(void 0===t)throw new Error("Value.set was undefined");e.dom.value=t},Qa=(e,t,o)=>{o.fold((()=>Ho(e,t)),(e=>{et(e,t)||(Lo(e,t),Wo(e))}))},ei=(e,t,o)=>{const n=V(t,o),s=ct(e);return H(s.slice(n.length),Wo),n},ti=(e,t,o,n)=>{const s=dt(e,t),r=n(o,s),a=((e,t,o)=>dt(e,t).map((e=>{if(o.exists((t=>!et(t,e)))){const t=o.map(We).getOr("span"),n=Ne(t);return Lo(e,n),n}return e})))(e,t,s);return Qa(e,r.element,a),r},oi=(e,t)=>{const o=ae(e),n=ae(t),s=J(n,o),r=((e,o)=>{const n={},s={};return me(e,((e,o)=>!ve(t,o)||e!==t[o]),ue(n),ue(s)),{t:n,f:s}})(e).t;return{toRemove:s,toSet:r}},ni=(e,t)=>{const o=t.filter((t=>We(t)===e.tag&&!(e=>e.innerHtml.isSome()&&e.domChildren.length>0)(e)&&!(e=>ve(e.dom,ka))(t))).bind((t=>((e,t)=>{try{const o=((e,t)=>{const{class:o,style:n,...s}=(e=>j(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}))(t),{toSet:r,toRemove:a}=oi(e.attributes,s),i=Vt(t),{toSet:l,toRemove:c}=oi(e.styles,i),d=Ka(t),u=J(d,e.classes),m=J(e.classes,d);return H(a,(e=>Mt(t,e))),_t(t,r),Ya(t,m),Xa(t,u),H(c,(e=>Pt(t,e))),Ft(t,l),e.innerHtml.fold((()=>{const o=e.domChildren;((e,t)=>{ei(e,t,((t,o)=>{const n=dt(e,o);return Qa(e,t,n),t}))})(t,o)}),(e=>{na(t,e)})),(()=>{const o=t,n=e.value.getOrUndefined();n!==Ja(o)&&Za(o,null!=n?n:"")})(),t})(e,t);return A.some(o)}catch(e){return A.none()}})(e,t))).getOrThunk((()=>(e=>{const t=Ne(e.tag);_t(t,e.attributes),Ya(t,e.classes),Ft(t,e.styles),e.innerHtml.each((e=>na(t,e)));const o=e.domChildren;return Po(t,o),e.value.each((e=>{Za(t,e)})),t})(e)));return ha(o,e.uid),o},si=e=>{const t=(e=>{const t=be(e,"behaviours").getOr({});return Y(ae(t),(e=>{const o=t[e];return g(o)?[o.me]:[]}))})(e);return((e,t)=>((e,t)=>{const o=V(t,(e=>xs(e.name(),[ss("config"),ws("state",Ta)]))),n=Xn("component.behaviours",In(o),e.behaviours).fold((t=>{throw new Error(Zn(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))}),w);return{list:t,data:ce(n,(e=>{const t=e.map((e=>({config:e.config,state:e.state.init(e.config)})));return x(t)}))}})(e,t))(e,t)},ri=(e,t)=>{const o=()=>m,n=Ms(xa),s=Kn((e=>Xn("custom.definition",La,e))(e)),r=si(e),a=(e=>e.list)(r),i=(e=>e.data)(r),l=((e,t,o)=>{const n={...(s=e).dom,uid:s.uid,domChildren:V(s.components,(e=>e.element))};var s;const r=(e=>e.domModification.fold((()=>Ma({})),Ma))(e),a={"alloy.base.modification":r},i=t.length>0?((e,t,o,n)=>{const s={...t};H(o,(t=>{s[t.name()]=t.exhibit(e,n)}));const r=Aa(s,((e,t)=>({name:e,modification:t}))),a=e=>W(e,((e,t)=>({...t.modification,...e})),{}),i=W(r.classes,((e,t)=>t.modification.concat(e)),[]),l=a(r.attributes),c=a(r.styles);return Ma({classes:i,attributes:l,styles:c})})(o,a,t,n):r;return l=n,c=i,{...l,attributes:{...l.attributes,...c.attributes},styles:{...l.styles,...c.styles},classes:l.classes.concat(c.classes)};var l,c})(s,a,i),c=ni(l,t),d=((e,t,o)=>{const n={"alloy.base.behaviour":za(e)};return((e,t,o,n)=>{const s=((e,t,o)=>{const n={...o,...Ia(t,e)};return Aa(n,Ba)})(e,o,n);return Ra(s,t)})(o,e.eventOrder,t,n).getOrDie()})(s,a,i),u=Ms(s.components),m={uid:e.uid,getSystem:n.get,config:t=>{const o=i;return(p(o[t.name()])?o[t.name()]:()=>{throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:e=>p(i[e.name()]),spec:e,readState:e=>i[e]().map((e=>e.state.readState())).getOr("not enabled"),getApis:()=>s.apis,connect:e=>{n.set(e)},disconnect:()=>{n.set(ya(o))},element:c,syncComponents:()=>{const e=ct(c),t=Y(e,(e=>n.get().getByDom(e).fold((()=>[]),Q)));u.set(t)},components:u.get,events:d};return m},ai=e=>{const t=Le(e);return ii({element:t})},ii=e=>{const t=Jn("external.component",Bn([ss("element"),gs("uid")]),e),o=Ms(ya()),n=t.uid.getOrThunk((()=>ba("external")));ha(t.element,n);const s={uid:n,getSystem:o.get,config:A.none,hasConfigured:T,connect:e=>{o.set(e)},disconnect:()=>{o.set(ya((()=>s)))},getApis:()=>({}),element:t.element,spec:e,readState:x("No state"),syncComponents:b,components:x([]),events:{}};return Ca(s)},li=ba,ci=(e,t)=>Oa(e).getOrThunk((()=>((e,t)=>{const{events:o,...n}=va(e),s=((e,t)=>{const o=be(e,"components").getOr([]);return t.fold((()=>V(o,di)),(e=>V(o,((t,o)=>ci(t,dt(e,o))))))})(n,t),r={...n,events:{...la,...o},components:s};return an.value(ri(r,t))})((e=>ve(e,"uid"))(e)?e:{uid:li(""),...e},t).getOrDie())),di=e=>ci(e,A.none()),ui=Ca;var mi,gi=(e,t,o,n,s)=>e(o,n)?A.some(o):p(s)&&s(o)?A.none():t(o,n,s);const pi=(e,t,o)=>{let n=e.dom;const s=p(o)?o:T;for(;n.parentNode;){n=n.parentNode;const e=ze(n);if(t(e))return A.some(e);if(s(e))break}return A.none()},hi=(e,t,o)=>gi(((e,t)=>t(e)),pi,e,t,o),fi=(e,t,o)=>hi(e,t,o).isSome(),bi=(e,t,o)=>pi(e,(e=>Je(e,t)),o),vi=(e,t)=>((e,o)=>G(e.dom.childNodes,(e=>{return o=ze(e),Je(o,t);var o})).map(ze))(e),yi=(e,t)=>Qe(t,e),xi=(e,t,o)=>gi(((e,t)=>Je(e,t)),bi,e,t,o),wi="aria-controls",Si=()=>{const e=da(wi);return{id:e,link:t=>{Ot(t,wi,e)},unlink:e=>{Mt(e,wi)}}},ki=(e,t)=>fi(t,(t=>et(t,e.element)),T)||((e,t)=>(e=>hi(e,(e=>{if(!$e(e))return!1;const t=Tt(e,"id");return void 0!==t&&t.indexOf(wi)>-1})).bind((e=>{const t=Tt(e,"id"),o=bt(e);return yi(o,`[${wi}="${t}"]`)})))(t).exists((t=>ki(e,t))))(e,t);var Ci;!function(e){e[e.STOP=0]="STOP",e[e.NORMAL=1]="NORMAL",e[e.LOGGING=2]="LOGGING"}(Ci||(Ci={}));const Oi=Ms({}),_i=["alloy/data/Fields","alloy/debugging/Debugging"],Ti=(e,t,o)=>((e,t,o)=>{switch(be(Oi.get(),e).orThunk((()=>{const t=ae(Oi.get());return re(t,(t=>e.indexOf(t)>-1?A.some(Oi.get()[t]):A.none()))})).getOr(Ci.NORMAL)){case Ci.NORMAL:return o(Ei());case Ci.LOGGING:{const n=((e,t)=>{const o=[],n=(new Date).getTime();return{logEventCut:(e,t,n)=>{o.push({outcome:"cut",target:t,purpose:n})},logEventStopped:(e,t,n)=>{o.push({outcome:"stopped",target:t,purpose:n})},logNoParent:(e,t,n)=>{o.push({outcome:"no-parent",target:t,purpose:n})},logEventNoHandlers:(e,t)=>{o.push({outcome:"no-handlers-left",target:t})},logEventResponse:(e,t,n)=>{o.push({outcome:"response",purpose:n,target:t})},write:()=>{const s=(new Date).getTime();R(["mousemove","mouseover","mouseout",yr()],e)||console.log(e,{event:e,time:s-n,target:t.dom,sequence:V(o,(e=>R(["cut","stopped","response"],e.outcome)?"{"+e.purpose+"} "+e.outcome+" at ("+ra(e.target)+")":e.outcome))})}}})(e,t),s=o(n);return n.write(),s}case Ci.STOP:return!0}})(e,t,o),Ei=x({logEventCut:b,logEventStopped:b,logNoParent:b,logEventNoHandlers:b,logEventResponse:b,write:b}),Ai=x([ss("menu"),ss("selectedMenu")]),Mi=x([ss("item"),ss("selectedItem")]);x(In(Mi().concat(Ai())));const Di=x(In(Mi())),Bi=ds("initSize",[ss("numColumns"),ss("numRows")]),Ii=()=>ds("markers",[ss("backgroundMenu")].concat(Ai()).concat(Mi())),Fi=e=>ds("markers",V(e,ss)),Ri=(e,t,o)=>((()=>{const e=new Error;if(void 0!==e.stack){const t=e.stack.split("\n");G(t,(e=>e.indexOf("alloy")>0&&!N(_i,(t=>e.indexOf(t)>-1)))).getOr("unknown")}})(),ts(t,t,o,qn((e=>an.value(((...t)=>e.apply(void 0,t))))))),Ni=e=>Ri(0,e,wn(b)),Li=e=>Ri(0,e,wn(A.none)),zi=e=>Ri(0,e,{tag:"required",process:{}}),Vi=e=>Ri(0,e,{tag:"required",process:{}}),Hi=(e,t)=>os(e,x(t)),Pi=e=>os(e,w),Ui=x(Bi),Wi=(e,t,o,n,s,r,a,i=!1)=>({x:e,y:t,bubble:o,direction:n,placement:s,restriction:r,label:`${a}-${s}`,alwaysFit:i}),ji=Ds([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),Gi=ji.southeast,$i=ji.southwest,qi=ji.northeast,Yi=ji.northwest,Xi=ji.south,Ki=ji.north,Ji=ji.east,Zi=ji.west,Qi=(e,t,o,n)=>{const s=e+t;return s>n?o:s<o?n:s},el=(e,t,o)=>Math.min(Math.max(e,t),o),tl=(e,t)=>Z(["left","right","top","bottom"],(o=>be(t,o).map((t=>((e,t)=>{switch(t){case 1:return e.x;case 0:return e.x+e.width;case 2:return e.y;case 3:return e.y+e.height}})(e,t))))),ol="layout",nl=e=>e.x,sl=(e,t)=>e.x+e.width/2-t.width/2,rl=(e,t)=>e.x+e.width-t.width,al=(e,t)=>e.y-t.height,il=e=>e.y+e.height,ll=(e,t)=>e.y+e.height/2-t.height/2,cl=(e,t,o)=>Wi(nl(e),il(e),o.southeast(),Gi(),"southeast",tl(e,{left:1,top:3}),ol),dl=(e,t,o)=>Wi(rl(e,t),il(e),o.southwest(),$i(),"southwest",tl(e,{right:0,top:3}),ol),ul=(e,t,o)=>Wi(nl(e),al(e,t),o.northeast(),qi(),"northeast",tl(e,{left:1,bottom:2}),ol),ml=(e,t,o)=>Wi(rl(e,t),al(e,t),o.northwest(),Yi(),"northwest",tl(e,{right:0,bottom:2}),ol),gl=(e,t,o)=>Wi(sl(e,t),al(e,t),o.north(),Ki(),"north",tl(e,{bottom:2}),ol),pl=(e,t,o)=>Wi(sl(e,t),il(e),o.south(),Xi(),"south",tl(e,{top:3}),ol),hl=(e,t,o)=>Wi((e=>e.x+e.width)(e),ll(e,t),o.east(),Ji(),"east",tl(e,{left:0}),ol),fl=(e,t,o)=>Wi(((e,t)=>e.x-t.width)(e,t),ll(e,t),o.west(),Zi(),"west",tl(e,{right:1}),ol),bl=()=>[cl,dl,ul,ml,pl,gl,hl,fl],vl=()=>[dl,cl,ml,ul,pl,gl,hl,fl],yl=()=>[ul,ml,cl,dl,gl,pl],xl=()=>[ml,ul,dl,cl,gl,pl],wl=()=>[cl,dl,ul,ml,pl,gl],Sl=()=>[dl,cl,ml,ul,pl,gl];var kl=Object.freeze({__proto__:null,events:e=>Pr([jr(mr(),((t,o)=>{const n=e.channels,s=ae(n),r=o,a=((e,t)=>t.universal?e:U(e,(e=>R(t.channels,e))))(s,r);H(a,(e=>{const o=n[e],s=o.schema,a=Jn("channel["+e+"] data\nReceiver: "+ra(t.element),s,r.data);o.onReceive(t,a)}))}))])}),Cl=[rs("channels",Yn(an.value,Bn([zi("onReceive"),ws("schema",zn())])))];const Ol=(e,t,o)=>ea(((n,s)=>{o(n,e,t)})),_l=e=>({key:e,value:void 0}),Tl=(e,t,o,n,s,r,a)=>{const i=e=>ye(e,o)?e[o]():A.none(),l=ce(s,((e,t)=>((e,t,o)=>((e,t,o)=>{const n=o.toString(),s=n.indexOf(")")+1,r=n.indexOf("("),a=n.substring(r+1,s-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:wa(a.slice(0,1).concat(a.slice(3)))}),e})(((n,...s)=>{const r=[n].concat(s);return n.config({name:x(e)}).fold((()=>{throw new Error("We could not find any behaviour configuration for: "+e+". Using API: "+o)}),(e=>{const o=Array.prototype.slice.call(r,1);return t.apply(void 0,[n,e.config,e.state].concat(o))}))}),o,t))(o,e,t))),c={...ce(r,((e,t)=>Sa(e,t))),...l,revoke:k(_l,o),config:t=>{const n=Jn(o+"-config",e,t);return{key:o,value:{config:n,me:c,configAsRaw:to((()=>Jn(o+"-config",e,t))),initialConfig:t,state:a}}},schema:x(t),exhibit:(e,t)=>Se(i(e),be(n,"exhibit"),((e,o)=>o(t,e.config,e.state))).getOrThunk((()=>Ma({}))),name:x(o),handlers:e=>i(e).map((e=>be(n,"events").getOr((()=>({})))(e.config,e.state))).getOr({})};return c},El=e=>Is(e),Al=Bn([ss("fields"),ss("name"),ws("active",{}),ws("apis",{}),ws("state",Ta),ws("extra",{})]),Ml=e=>{const t=Jn("Creating behaviour: "+e.name,Al,e);return((e,t,o,n,s,r)=>{const a=Bn(e),i=xs(t,[("config",l=e,ps("config",Bn(l)))]);var l;return Tl(a,i,t,o,n,s,r)})(t.fields,t.name,t.active,t.apis,t.extra,t.state)},Dl=Bn([ss("branchKey"),ss("branches"),ss("name"),ws("active",{}),ws("apis",{}),ws("state",Ta),ws("extra",{})]),Bl=e=>{const t=Jn("Creating behaviour: "+e.name,Dl,e);return((e,t,o,n,s,r)=>{const a=e,i=xs(t,[ps("config",e)]);return Tl(a,i,t,o,n,s,r)})(Qn(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)},Il=x(void 0),Fl=Ml({fields:Cl,name:"receiving",active:kl});var Rl=Object.freeze({__proto__:null,exhibit:(e,t)=>Ma({classes:[],styles:t.useFixed()?{}:{position:"relative"}})});const Nl=(e,t=!1)=>e.dom.focus({preventScroll:t}),Ll=e=>e.dom.blur(),zl=e=>{const t=bt(e).dom;return e.dom===t.activeElement},Vl=(e=Yo())=>A.from(e.dom.activeElement).map(ze),Hl=e=>Vl(bt(e)).filter((t=>e.dom.contains(t.dom))),Pl=(e,t)=>{const o=bt(t),n=Vl(o).bind((e=>{const o=t=>et(e,t);return o(t)?A.some(t):((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const s=ze(e.childNodes[n]);if(t(s))return A.some(s);const r=o(e.childNodes[n]);if(r.isSome())return r}return A.none()};return o(e.dom)})(t,o)})),s=e(t);return n.each((e=>{Vl(o).filter((t=>et(t,e))).fold((()=>{Nl(e)}),b)})),s},Ul=(e,t,o,n,s)=>{const r=e=>e+"px";return{position:e,left:t.map(r),top:o.map(r),right:n.map(r),bottom:s.map(r)}},Wl=(e,t)=>{Rt(e,(e=>({...e,position:A.some(e.position)}))(t))},jl=Ds([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),Gl=(e,t,o,n,s,r)=>{const a=t.rect,i=a.x-o,l=a.y-n,c=s-(i+a.width),d=r-(l+a.height),u=A.some(i),m=A.some(l),g=A.some(c),p=A.some(d),h=A.none();return t.direction.fold((()=>Ul(e,u,m,h,h)),(()=>Ul(e,h,m,g,h)),(()=>Ul(e,u,h,h,p)),(()=>Ul(e,h,h,g,p)),(()=>Ul(e,u,m,h,h)),(()=>Ul(e,u,h,h,p)),(()=>Ul(e,u,m,h,h)),(()=>Ul(e,h,m,g,h)))},$l=(e,t)=>e.fold((()=>{const e=t.rect;return Ul("absolute",A.some(e.x),A.some(e.y),A.none(),A.none())}),((e,o,n,s)=>Gl("absolute",t,e,o,n,s)),((e,o,n,s)=>Gl("fixed",t,e,o,n,s))),ql=(e,t)=>{const o=k(Jo,t),n=e.fold(o,o,(()=>{const e=jo();return Jo(t).translate(-e.left,-e.top)})),s=eo(t),r=$t(t);return Zo(n.left,n.top,s,r)},Yl=(e,t)=>t.fold((()=>e.fold(on,on,Zo)),(t=>e.fold(x(t),x(t),(()=>{const o=Xl(e,t.x,t.y);return Zo(o.left,o.top,t.width,t.height)})))),Xl=(e,t,o)=>{const n=Yt(t,o);return e.fold(x(n),x(n),(()=>{const e=jo();return n.translate(-e.left,-e.top)}))};jl.none;const Kl=jl.relative,Jl=jl.fixed,Zl="data-alloy-placement",Ql=e=>Et(e,Zl),ec=Ds([{fit:["reposition"]},{nofit:["reposition","visibleW","visibleH","isVisible"]}]),tc=(e,t,o,n)=>{const s=e.bubble,r=s.offset,a=((e,t,o)=>{const n=(n,s)=>t[n].map((t=>{const r="top"===n||"bottom"===n,a=r?o.top:o.left,i=("left"===n||"top"===n?Math.max:Math.min)(t,s)+a;return r?el(i,e.y,e.bottom):el(i,e.x,e.right)})).getOr(s),s=n("left",e.x),r=n("top",e.y),a=n("right",e.right),i=n("bottom",e.bottom);return Zo(s,r,a-s,i-r)})(n,e.restriction,r),i=e.x+r.left,l=e.y+r.top,c=Zo(i,l,t,o),{originInBounds:d,sizeInBounds:u,visibleW:m,visibleH:g}=((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,right:l,bottom:c,width:d,height:u}=e;return{originInBounds:a>=o&&a<=s&&i>=n&&i<=r,sizeInBounds:l<=s&&l>=o&&c<=r&&c>=n,visibleW:Math.min(d,a>=o?s-a:l-o),visibleH:Math.min(u,i>=n?r-i:c-n)}})(c,a),p=d&&u,h=p?c:((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,width:l,height:c}=e,d=Math.max(o,s-l),u=Math.max(n,r-c),m=el(a,o,d),g=el(i,n,u),p=Math.min(m+l,s)-m,h=Math.min(g+c,r)-g;return Zo(m,g,p,h)})(c,a),f=h.width>0&&h.height>0,{maxWidth:b,maxHeight:v}=((e,t,o)=>{const n=x(t.bottom-o.y),s=x(o.bottom-t.y),r=((e,t,o,n)=>e.fold(t,t,n,n,t,n,o,o))(e,s,s,n),a=x(t.right-o.x),i=x(o.right-t.x),l=((e,t,o,n)=>e.fold(t,n,t,n,o,o,t,n))(e,i,i,a);return{maxWidth:l,maxHeight:r}})(e.direction,h,n),y={rect:h,maxHeight:v,maxWidth:b,direction:e.direction,placement:e.placement,classes:{on:s.classesOn,off:s.classesOff},layout:e.label,testY:l};return p||e.alwaysFit?ec.fit(y):ec.nofit(y,m,g,f)},oc=e=>{const t=Ms(A.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(A.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(A.some(e))}}},nc=()=>oc((e=>e.unbind())),sc=()=>{const e=oc(b);return{...e,on:t=>e.get().each(t)}},rc=E,ac=(e,t,o)=>((e,t,o,n)=>Ro(e,t,o,n,!1))(e,t,rc,o),ic=(e,t,o)=>((e,t,o,n)=>Ro(e,t,o,n,!0))(e,t,rc,o),lc=Fo,cc=["top","bottom","right","left"],dc="data-alloy-transition-timer",uc=(e,t,o,n,s,a)=>{const i=((e,t,o)=>o.exists((o=>{const n=e.mode;return"all"===n||o[n]!==t[n]})))(n,s,a);if(i||((e,t)=>((e,t)=>X(t,(t=>qa(e,t))))(e,t.classes))(e,n)){It(e,"position",o.position);const a=ql(t,e),l=$l(t,{...s,rect:a}),c=Z(cc,(e=>l[e]));((e,t)=>{const o=e=>parseFloat(e).toFixed(3);return he(t,((t,n)=>!((e,t,o=S)=>Se(e,t,o).getOr(e.isNone()&&t.isNone()))(e[n].map(o),t.map(o)))).isSome()})(o,c)&&(Rt(e,c),i&&((e,t)=>{Ya(e,t.classes),Et(e,dc).each((t=>{clearTimeout(parseInt(t,10)),Mt(e,dc)})),((e,t)=>{const o=nc(),n=nc();let s;const a=t=>{var o;const n=null!==(o=t.raw.pseudoElement)&&void 0!==o?o:"";return et(t.target,e)&&Be(n)&&R(cc,t.raw.propertyName)},i=r=>{if(m(r)||a(r)){o.clear(),n.clear();const a=null==r?void 0:r.raw.type;(m(a)||a===sr())&&(clearTimeout(s),Mt(e,dc),Xa(e,t.classes))}},l=ac(e,rr(),(t=>{a(t)&&(l.unbind(),o.set(ac(e,sr(),i)),n.set(ac(e,nr(),i)))})),c=(e=>{const t=t=>{const o=Nt(e,t).split(/\s*,\s*/);return U(o,De)},o=e=>{if(r(e)&&/^[\d.]+/.test(e)){const t=parseFloat(e);return Ae(e,"ms")?t:1e3*t}return 0},n=t("transition-delay"),s=t("transition-duration");return j(s,((e,t,s)=>{const r=o(n[s])+o(t);return Math.max(e,r)}),0)})(e);requestAnimationFrame((()=>{s=setTimeout(i,c+17),Ot(e,dc,s)}))})(e,t)})(e,n),Ut(e))}else Xa(e,n.classes)},mc=(e,t)=>{((e,t)=>{const o=jt.max(e,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);It(e,"max-height",o+"px")})(e,Math.floor(t))},gc=x(((e,t)=>{mc(e,t),Ft(e,{"overflow-x":"hidden","overflow-y":"auto"})})),pc=x(((e,t)=>{mc(e,t)})),hc=(e,t,o)=>void 0===e[t]?o:e[t],fc=(e,t,o,n)=>{const s=((e,t,o,n)=>{Pt(t,"max-height"),Pt(t,"max-width");const s={width:eo(r=t),height:$t(r)};var r;return((e,t,o,n,s,r)=>{const a=n.width,i=n.height,l=(t,l,c,d,u)=>{const m=t(o,n,s,e,r),g=tc(m,a,i,r);return g.fold(x(g),((e,t,o,n)=>(u===n?o>d||t>c:!u&&n)?g:ec.nofit(l,c,d,u)))};return j(t,((e,t)=>{const o=k(l,t);return e.fold(x(e),o)}),ec.nofit({rect:o,maxHeight:n.height,maxWidth:n.width,direction:Gi(),placement:"southeast",classes:{on:[],off:[]},layout:"none",testY:o.y},-1,-1,!1)).fold(w,w)})(t,n.preference,e,s,o,n.bounds)})(e,t,o,n);return((e,t,o)=>{const n=$l(o.origin,t);o.transition.each((s=>{uc(e,o.origin,n,s,t,o.lastPlacement)})),Wl(e,n)})(t,s,n),((e,t)=>{((e,t)=>{Ot(e,Zl,t)})(e,t.placement)})(t,s),((e,t)=>{const o=t.classes;Xa(e,o.off),Ya(e,o.on)})(t,s),((e,t,o)=>{(0,o.maxHeightFunction)(e,t.maxHeight)})(t,s,n),((e,t,o)=>{(0,o.maxWidthFunction)(e,t.maxWidth)})(t,s,n),{layout:s.layout,placement:s.placement}},bc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right","inset"],vc=(e,t,o,n=1)=>{const s=e*n,r=t*n,a=e=>be(o,e).getOr([]),i=(e,t,o)=>{const n=J(bc,o);return{offset:Yt(e,t),classesOn:Y(o,a),classesOff:Y(n,a)}};return{southeast:()=>i(-e,t,["top","alignLeft"]),southwest:()=>i(e,t,["top","alignRight"]),south:()=>i(-e/2,t,["top","alignCentre"]),northeast:()=>i(-e,-t,["bottom","alignLeft"]),northwest:()=>i(e,-t,["bottom","alignRight"]),north:()=>i(-e/2,-t,["bottom","alignCentre"]),east:()=>i(e,-t/2,["valignCentre","left"]),west:()=>i(-e,-t/2,["valignCentre","right"]),insetNortheast:()=>i(s,r,["top","alignLeft","inset"]),insetNorthwest:()=>i(-s,r,["top","alignRight","inset"]),insetNorth:()=>i(-s/2,r,["top","alignCentre","inset"]),insetSoutheast:()=>i(s,-r,["bottom","alignLeft","inset"]),insetSouthwest:()=>i(-s,-r,["bottom","alignRight","inset"]),insetSouth:()=>i(-s/2,-r,["bottom","alignCentre","inset"]),insetEast:()=>i(-s,-r/2,["valignCentre","right","inset"]),insetWest:()=>i(s,-r/2,["valignCentre","left","inset"])}},yc=()=>vc(0,0,{}),xc=w,wc=(e,t)=>o=>"rtl"===Sc(o)?t:e,Sc=e=>"rtl"===Nt(e,"direction")?"rtl":"ltr";var kc;!function(e){e.TopToBottom="toptobottom",e.BottomToTop="bottomtotop"}(kc||(kc={}));const Cc="data-alloy-vertical-dir",Oc=e=>fi(e,(e=>$e(e)&&Tt(e,"data-alloy-vertical-dir")===kc.BottomToTop)),_c=()=>xs("layouts",[ss("onLtr"),ss("onRtl"),gs("onBottomLtr"),gs("onBottomRtl")]),Tc=(e,t,o,n,s,r,a)=>{const i=a.map(Oc).getOr(!1),l=t.layouts.map((t=>t.onLtr(e))),c=t.layouts.map((t=>t.onRtl(e))),d=i?t.layouts.bind((t=>t.onBottomLtr.map((t=>t(e))))).or(l).getOr(s):l.getOr(o),u=i?t.layouts.bind((t=>t.onBottomRtl.map((t=>t(e))))).or(c).getOr(r):c.getOr(n);return wc(d,u)(e)};var Ec=[ss("hotspot"),gs("bubble"),ws("overrides",{}),_c(),Hi("placement",((e,t,o)=>{const n=t.hotspot,s=ql(o,n.element),r=Tc(e.element,t,wl(),Sl(),yl(),xl(),A.some(t.hotspot.element));return A.some(xc({anchorBox:s,bubble:t.bubble.getOr(yc()),overrides:t.overrides,layouts:r}))}))],Ac=[ss("x"),ss("y"),ws("height",0),ws("width",0),ws("bubble",yc()),ws("overrides",{}),_c(),Hi("placement",((e,t,o)=>{const n=Xl(o,t.x,t.y),s=Zo(n.left,n.top,t.width,t.height),r=Tc(e.element,t,bl(),vl(),bl(),vl(),A.none());return A.some(xc({anchorBox:s,bubble:t.bubble,overrides:t.overrides,layouts:r}))}))];const Mc=Ds([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),Dc=e=>e.fold(w,((e,t,o)=>e.translate(-t,-o))),Bc=e=>e.fold(w,w),Ic=e=>j(e,((e,t)=>e.translate(t.left,t.top)),Yt(0,0)),Fc=e=>{const t=V(e,Bc);return Ic(t)},Rc=Mc.screen,Nc=Mc.absolute,Lc=(e,t,o)=>{const n=ot(e.element),s=jo(n),r=((e,t,o)=>{const n=rt(o.root).dom;return A.from(n.frameElement).map(ze).filter((t=>{const o=ot(t),n=ot(e.element);return et(o,n)})).map(Kt)})(e,0,o).getOr(s);return Nc(r,s.left,s.top)},zc=(e,t,o,n)=>{const s=Rc(Yt(e,t));return A.some(((e,t,o)=>({point:e,width:t,height:o}))(s,o,n))},Vc=(e,t,o,n,s)=>e.map((e=>{const r=[t,e.point],a=(i=()=>Fc(r),l=()=>Fc(r),c=()=>(e=>{const t=V(e,Dc);return Ic(t)})(r),n.fold(i,l,c));var i,l,c;const d=(p=a.left,h=a.top,f=e.width,b=e.height,{x:p,y:h,width:f,height:b}),u=o.showAbove?yl():wl(),m=o.showAbove?xl():Sl(),g=Tc(s,o,u,m,u,m,A.none());var p,h,f,b;return xc({anchorBox:d,bubble:o.bubble.getOr(yc()),overrides:o.overrides,layouts:g})}));var Hc=[ss("node"),ss("root"),gs("bubble"),_c(),ws("overrides",{}),ws("showAbove",!1),Hi("placement",((e,t,o)=>{const n=Lc(e,0,t);return t.node.filter(wt).bind((s=>{const r=s.dom.getBoundingClientRect(),a=zc(r.left,r.top,r.width,r.height),i=t.node.getOr(e.element);return Vc(a,n,t,o,i)}))}))];const Pc=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),Uc=Ds([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Wc=(Uc.before,Uc.on,Uc.after,e=>e.fold(w,w,w)),jc=Ds([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Gc={domRange:jc.domRange,relative:jc.relative,exact:jc.exact,exactFromRange:e=>jc.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>ze(e.startContainer),relative:(e,t)=>Wc(e),exact:(e,t,o,n)=>e}))(e);return rt(t)},range:Pc},$c=(e,t,o)=>{const n=e.document.createRange();var s;return s=n,t.fold((e=>{s.setStartBefore(e.dom)}),((e,t)=>{s.setStart(e.dom,t)}),(e=>{s.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},qc=(e,t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},Yc=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),Xc=Ds([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Kc=(e,t,o)=>t(ze(o.startContainer),o.startOffset,ze(o.endContainer),o.endOffset),Jc=(e,t)=>((e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:x(e),rtl:A.none}),relative:(t,o)=>({ltr:to((()=>$c(e,t,o))),rtl:to((()=>A.some($c(e,o,t))))}),exact:(t,o,n,s)=>({ltr:to((()=>qc(e,t,o,n,s))),rtl:to((()=>A.some(qc(e,n,s,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>Xc.rtl(ze(e.endContainer),e.endOffset,ze(e.startContainer),e.startOffset))).getOrThunk((()=>Kc(0,Xc.ltr,o))):Kc(0,Xc.ltr,o)})(0,o)})(e,t).match({ltr:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},rtl:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(n.dom,s),r.setEnd(t.dom,o),r}});Xc.ltr,Xc.rtl;const Zc=(e,t,o)=>U(((e,t)=>{const o=p(t)?t:T;let n=e.dom;const s=[];for(;null!==n.parentNode&&void 0!==n.parentNode;){const e=n.parentNode,t=ze(e);if(s.push(t),!0===o(t))break;n=e}return s})(e,o),t),Qc=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Ze(o)?[]:V(o.querySelectorAll(e),ze)})(t,e),ed=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return A.some(Pc(ze(t.startContainer),t.startOffset,ze(o.endContainer),o.endOffset))}return A.none()},td=e=>{if(null===e.anchorNode||null===e.focusNode)return ed(e);{const t=ze(e.anchorNode),o=ze(e.focusNode);return((e,t,o,n)=>{const s=((e,t,o,n)=>{const s=ot(e).dom.createRange();return s.setStart(e.dom,t),s.setEnd(o.dom,n),s})(e,t,o,n),r=et(e,o)&&t===n;return s.collapsed&&!r})(t,e.anchorOffset,o,e.focusOffset)?A.some(Pc(t,e.anchorOffset,o,e.focusOffset)):ed(e)}},od=(e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?A.some(o).map(Yc):A.none()})(Jc(e,t)),nd=((e,t)=>{const o=t=>e(t)?A.from(t.dom.nodeValue):A.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return o(t).getOr("")},getOption:o,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(qe),sd=(e,t)=>({element:e,offset:t}),rd=(e,t)=>qe(e)?sd(e,t):((e,t)=>{const o=ct(e);if(0===o.length)return sd(e,t);if(t<o.length)return sd(o[t],0);{const e=o[o.length-1],t=qe(e)?(e=>nd.get(e))(e).length:ct(e).length;return sd(e,t)}})(e,t),ad=e=>void 0!==e.foffset,id=(e,t)=>t.getSelection.getOrThunk((()=>()=>(e=>(e=>A.from(e.getSelection()))(e).filter((e=>e.rangeCount>0)).bind(td))(e)))().map((e=>{if(ad(e)){const t=rd(e.start,e.soffset),o=rd(e.finish,e.foffset);return Gc.range(t.element,t.offset,o.element,o.offset)}return e}));var ld=[gs("getSelection"),ss("root"),gs("bubble"),_c(),ws("overrides",{}),ws("showAbove",!1),Hi("placement",((e,t,o)=>{const n=rt(t.root).dom,s=Lc(e,0,t),r=id(n,t).bind((e=>{if(ad(e)){const t=((e,t)=>(e=>{const t=e.getBoundingClientRect();return t.width>0||t.height>0?A.some(t).map(Yc):A.none()})(Jc(e,t)))(n,Gc.exactFromRange(e)).orThunk((()=>{const t=Le("\ufeff");Lo(e.start,t);const o=od(n,Gc.exact(t,0,t,1));return Wo(t),o}));return t.bind((e=>zc(e.left,e.top,e.width,e.height)))}{const t=ce(e,(e=>e.dom.getBoundingClientRect())),o={left:Math.min(t.firstCell.left,t.lastCell.left),right:Math.max(t.firstCell.right,t.lastCell.right),top:Math.min(t.firstCell.top,t.lastCell.top),bottom:Math.max(t.firstCell.bottom,t.lastCell.bottom)};return zc(o.left,o.top,o.right-o.left,o.bottom-o.top)}})),a=id(n,t).bind((e=>ad(e)?$e(e.start)?A.some(e.start):it(e.start):A.some(e.firstCell))).getOr(e.element);return Vc(r,s,t,o,a)}))];const cd="link-layout",dd=e=>e.x+e.width,ud=(e,t)=>e.x-t.width,md=(e,t)=>e.y-t.height+e.height,gd=e=>e.y,pd=(e,t,o)=>Wi(dd(e),gd(e),o.southeast(),Gi(),"southeast",tl(e,{left:0,top:2}),cd),hd=(e,t,o)=>Wi(ud(e,t),gd(e),o.southwest(),$i(),"southwest",tl(e,{right:1,top:2}),cd),fd=(e,t,o)=>Wi(dd(e),md(e,t),o.northeast(),qi(),"northeast",tl(e,{left:0,bottom:3}),cd),bd=(e,t,o)=>Wi(ud(e,t),md(e,t),o.northwest(),Yi(),"northwest",tl(e,{right:1,bottom:3}),cd),vd=()=>[pd,hd,fd,bd],yd=()=>[hd,pd,bd,fd];var xd=[ss("item"),_c(),ws("overrides",{}),Hi("placement",((e,t,o)=>{const n=ql(o,t.item.element),s=Tc(e.element,t,vd(),yd(),vd(),yd(),A.none());return A.some(xc({anchorBox:n,bubble:yc(),overrides:t.overrides,layouts:s}))}))],wd=Qn("type",{selection:ld,node:Hc,hotspot:Ec,submenu:xd,makeshift:Ac});const Sd=[ms("classes",Pn),Os("mode","all",["all","layout","placement"])],kd=[ws("useFixed",T),gs("getBounds")],Cd=[rs("anchor",wd),xs("transition",Sd)],Od=(e,t,o,n,s,r)=>{const a=Jn("placement.info",In(Cd),s),i=a.anchor,l=n.element,c=o.get(n.uid);Pl((()=>{It(l,"position","fixed");const s=zt(l,"visibility");It(l,"visibility","hidden");const d=t.useFixed()?(()=>{const e=document.documentElement;return Jl(0,0,e.clientWidth,e.clientHeight)})():(e=>{const t=Kt(e.element),o=e.element.dom.getBoundingClientRect();return Kl(t.left,t.top,o.width,o.height)})(e);i.placement(e,i,d).each((e=>{const s=r.orThunk((()=>t.getBounds.map(_))),i=((e,t,o,n,s,r)=>((e,t,o,n,s,r,a,i)=>{const l=hc(a,"maxHeightFunction",gc()),c=hc(a,"maxWidthFunction",b),d=e.anchorBox,u=e.origin,m={bounds:Yl(u,r),origin:u,preference:n,maxHeightFunction:l,maxWidthFunction:c,lastPlacement:s,transition:i};return fc(d,t,o,m)})(((e,t)=>((e,t)=>({anchorBox:e,origin:t}))(e,t))(t.anchorBox,e),n.element,t.bubble,t.layouts,s,o,t.overrides,r))(d,e,s,n,c,a.transition);o.set(n.uid,i)})),s.fold((()=>{Pt(l,"visibility")}),(e=>{It(l,"visibility",e)})),zt(l,"left").isNone()&&zt(l,"top").isNone()&&zt(l,"right").isNone()&&zt(l,"bottom").isNone()&&xe(zt(l,"position"),"fixed")&&Pt(l,"position")}),l)};var _d=Object.freeze({__proto__:null,position:(e,t,o,n,s)=>{const r=A.none();Od(e,t,o,n,s,r)},positionWithinBounds:Od,getMode:(e,t,o)=>t.useFixed()?"fixed":"absolute",reset:(e,t,o,n)=>{const s=n.element;H(["position","left","right","top","bottom"],(e=>Pt(s,e))),(e=>{Mt(e,Zl)})(s),o.clear(n.uid)}});const Td=Ml({fields:kd,name:"positioning",active:Rl,apis:_d,state:Object.freeze({__proto__:null,init:()=>{let e={};return Ea({readState:()=>e,clear:t=>{g(t)?delete e[t]:e={}},set:(t,o)=>{e[t]=o},get:t=>be(e,t)})}})}),Ed=e=>e.getSystem().isConnected(),Ad=e=>{Rr(e,Or());const t=e.components();H(t,Ad)},Md=e=>{const t=e.components();H(t,Md),Rr(e,Cr())},Dd=(e,t)=>{e.getSystem().addToWorld(t),wt(e.element)&&Md(t)},Bd=e=>{Ad(e),e.getSystem().removeFromWorld(e)},Id=(e,t)=>{Ho(e.element,t.element)},Fd=(e,t)=>{Rd(e,t,Ho)},Rd=(e,t,o)=>{e.getSystem().addToWorld(t),o(e.element,t.element),wt(e.element)&&Md(t),e.syncComponents()},Nd=e=>{Ad(e),Wo(e.element),e.getSystem().removeFromWorld(e)},Ld=e=>{const t=at(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()));Nd(e),t.each((e=>{e.syncComponents()}))},zd=e=>{const t=e.components();H(t,Nd),Uo(e.element),e.syncComponents()},Vd=(e,t)=>{Pd(e,t,Ho)},Hd=(e,t)=>{Pd(e,t,zo)},Pd=(e,t,o)=>{o(e,t.element);const n=ct(t.element);H(n,(e=>{t.getByDom(e).each(Md)}))},Ud=e=>{const t=ct(e.element);H(t,(t=>{e.getByDom(t).each(Ad)})),Wo(e.element)},Wd=(e,t,o,n)=>{o.get().each((t=>{zd(e)}));const s=t.getAttachPoint(e);Fd(s,e);const r=e.getSystem().build(n);return Fd(e,r),o.set(r),r},jd=(e,t,o,n)=>{const s=Wd(e,t,o,n);return t.onOpen(e,s),s},Gd=(e,t,o)=>{o.get().each((n=>{zd(e),Ld(e),t.onClose(e,n),o.clear()}))},$d=(e,t,o)=>o.isOpen(),qd=(e,t,o)=>{const n=t.getAttachPoint(e);It(e.element,"position",Td.getMode(n)),((e,t,o,n)=>{zt(e.element,t).fold((()=>{Mt(e.element,o)}),(t=>{Ot(e.element,o,t)})),It(e.element,t,"hidden")})(e,"visibility",t.cloakVisibilityAttr)},Yd=(e,t,o)=>{(e=>N(["top","left","right","bottom"],(t=>zt(e,t).isSome())))(e.element)||Pt(e.element,"position"),((e,t,o)=>{Et(e.element,o).fold((()=>Pt(e.element,t)),(o=>It(e.element,t,o)))})(e,"visibility",t.cloakVisibilityAttr)};var Xd=Object.freeze({__proto__:null,cloak:qd,decloak:Yd,open:jd,openWhileCloaked:(e,t,o,n,s)=>{qd(e,t),jd(e,t,o,n),s(),Yd(e,t)},close:Gd,isOpen:$d,isPartOf:(e,t,o,n)=>$d(0,0,o)&&o.get().exists((o=>t.isPartOf(e,o,n))),getState:(e,t,o)=>o.get(),setContent:(e,t,o,n)=>o.get().map((()=>Wd(e,t,o,n)))}),Kd=Object.freeze({__proto__:null,events:(e,t)=>Pr([jr(br(),((o,n)=>{Gd(o,e,t)}))])}),Jd=[Ni("onOpen"),Ni("onClose"),ss("isPartOf"),ss("getAttachPoint"),ws("cloakVisibilityAttr","data-precloak-visibility")],Zd=Object.freeze({__proto__:null,init:()=>{const e=sc(),t=x("not-implemented");return Ea({readState:t,isOpen:e.isSet,clear:e.clear,set:e.set,get:e.get})}});const Qd=Ml({fields:Jd,name:"sandboxing",active:Kd,apis:Xd,state:Zd}),eu=x("dismiss.popups"),tu=x("reposition.popups"),ou=x("mouse.released"),nu=Bn([ws("isExtraPart",T),xs("fireEventInstead",[ws("event",_r())])]),su=e=>{const t=Jn("Dismissal",nu,e);return{[eu()]:{schema:Bn([ss("target")]),onReceive:(e,o)=>{Qd.isOpen(e)&&(Qd.isPartOf(e,o.target)||t.isExtraPart(e,o.target)||t.fireEventInstead.fold((()=>Qd.close(e)),(t=>Rr(e,t.event))))}}}},ru=Bn([xs("fireEventInstead",[ws("event",Tr())]),cs("doReposition")]),au=e=>{const t=Jn("Reposition",ru,e);return{[tu()]:{onReceive:e=>{Qd.isOpen(e)&&t.fireEventInstead.fold((()=>t.doReposition(e)),(t=>Rr(e,t.event)))}}}},iu=(e,t,o)=>{t.store.manager.onLoad(e,t,o)},lu=(e,t,o)=>{t.store.manager.onUnload(e,t,o)};var cu=Object.freeze({__proto__:null,onLoad:iu,onUnload:lu,setValue:(e,t,o,n)=>{t.store.manager.setValue(e,t,o,n)},getValue:(e,t,o)=>t.store.manager.getValue(e,t,o),getState:(e,t,o)=>o}),du=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.resetOnDom?[Zr(((o,n)=>{iu(o,e,t)})),Qr(((o,n)=>{lu(o,e,t)}))]:[Ol(e,t,iu)];return Pr(o)}});const uu=()=>{const e=Ms(null);return Ea({set:e.set,get:e.get,isNotSet:()=>null===e.get(),clear:()=>{e.set(null)},readState:()=>({mode:"memory",value:e.get()})})},mu=()=>{const e=Ms({}),t=Ms({});return Ea({readState:()=>({mode:"dataset",dataByValue:e.get(),dataByText:t.get()}),lookup:o=>be(e.get(),o).orThunk((()=>be(t.get(),o))),update:o=>{const n=e.get(),s=t.get(),r={},a={};H(o,(e=>{r[e.value]=e,be(e,"meta").each((t=>{be(t,"text").each((t=>{a[t]=e}))}))})),e.set({...n,...r}),t.set({...s,...a})},clear:()=>{e.set({}),t.set({})}})};var gu=Object.freeze({__proto__:null,memory:uu,dataset:mu,manual:()=>Ea({readState:b}),init:e=>e.store.manager.state(e)});const pu=(e,t,o,n)=>{const s=t.store;o.update([n]),s.setValue(e,n),t.onSetValue(e,n)};var hu=[gs("initialValue"),ss("getFallbackEntry"),ss("getDataKey"),ss("setValue"),Hi("manager",{setValue:pu,getValue:(e,t,o)=>{const n=t.store,s=n.getDataKey(e);return o.lookup(s).getOrThunk((()=>n.getFallbackEntry(s)))},onLoad:(e,t,o)=>{t.store.initialValue.each((n=>{pu(e,t,o,n)}))},onUnload:(e,t,o)=>{o.clear()},state:mu})],fu=[ss("getValue"),ws("setValue",b),gs("initialValue"),Hi("manager",{setValue:(e,t,o,n)=>{t.store.setValue(e,n),t.onSetValue(e,n)},getValue:(e,t,o)=>t.store.getValue(e),onLoad:(e,t,o)=>{t.store.initialValue.each((o=>{t.store.setValue(e,o)}))},onUnload:b,state:Ta.init})],bu=[gs("initialValue"),Hi("manager",{setValue:(e,t,o,n)=>{o.set(n),t.onSetValue(e,n)},getValue:(e,t,o)=>o.get(),onLoad:(e,t,o)=>{t.store.initialValue.each((e=>{o.isNotSet()&&o.set(e)}))},onUnload:(e,t,o)=>{o.clear()},state:uu})],vu=[Ss("store",{mode:"memory"},Qn("mode",{memory:bu,manual:fu,dataset:hu})),Ni("onSetValue"),ws("resetOnDom",!1)];const yu=Ml({fields:vu,name:"representing",active:du,apis:cu,extra:{setValueFrom:(e,t)=>{const o=yu.getValue(t);yu.setValue(e,o)}},state:gu}),xu=(e,t)=>As(e,{},V(t,(t=>{return o=t.name(),n="Cannot configure "+t.name()+" for "+e,ts(o,o,{tag:"option",process:{}},_n((e=>gn("The field: "+o+" is forbidden. "+n))));var o,n})).concat([os("dump",w)])),wu=e=>e.dump,Su=(e,t)=>({...El(t),...e.dump}),ku=xu,Cu=Su,Ou="placeholder",_u=Ds([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Tu=e=>ve(e,"uiType"),Eu=(e,t,o,n)=>((e,t,o,n)=>Tu(o)&&o.uiType===Ou?((e,t,o,n)=>e.exists((e=>e!==o.owner))?_u.single(!0,x(o)):be(n,o.name).fold((()=>{throw new Error("Unknown placeholder component: "+o.name+"\nKnown: ["+ae(n)+"]\nNamespace: "+e.getOr("none")+"\nSpec: "+JSON.stringify(o,null,2))}),(e=>e.replace())))(e,0,o,n):_u.single(!1,x(o)))(e,0,o,n).fold(((s,r)=>{const a=Tu(o)?r(t,o.config,o.validated):r(t),i=be(a,"components").getOr([]),l=Y(i,(o=>Eu(e,t,o,n)));return[{...a,components:l}]}),((e,n)=>{if(Tu(o)){const e=n(t,o.config,o.validated);return o.validated.preprocess.getOr(w)(e)}return n(t)})),Au=_u.single,Mu=_u.multiple,Du=x(Ou),Bu=Ds([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Iu=ws("factory",{sketch:w}),Fu=ws("schema",[]),Ru=ss("name"),Nu=ts("pname","pname",xn((e=>"<alloy."+da(e.name)+">")),zn()),Lu=os("schema",(()=>[gs("preprocess")])),zu=ws("defaults",x({})),Vu=ws("overrides",x({})),Hu=In([Iu,Fu,Ru,Nu,zu,Vu]),Pu=In([Iu,Fu,Ru,zu,Vu]),Uu=In([Iu,Fu,Ru,Nu,zu,Vu]),Wu=In([Iu,Lu,Ru,ss("unit"),Nu,zu,Vu]),ju=e=>e.fold(A.some,A.none,A.some,A.some),Gu=e=>{const t=e=>e.name;return e.fold(t,t,t,t)},$u=(e,t)=>o=>{const n=Jn("Converting part type",t,o);return e(n)},qu=$u(Bu.required,Hu),Yu=$u(Bu.external,Pu),Xu=$u(Bu.optional,Uu),Ku=$u(Bu.group,Wu),Ju=x("entirety");var Zu=Object.freeze({__proto__:null,required:qu,external:Yu,optional:Xu,group:Ku,asNamedPart:ju,name:Gu,asCommon:e=>e.fold(w,w,w,w),original:Ju});const Qu=(e,t,o,n)=>vn(t.defaults(e,o,n),o,{uid:e.partUids[t.name]},t.overrides(e,o,n)),em=(e,t)=>{const o={};return H(t,(t=>{ju(t).each((t=>{const n=tm(e,t.pname);o[t.name]=o=>{const s=Jn("Part: "+t.name+" in "+e,In(t.schema),o);return{...n,config:o,validated:s}}}))})),o},tm=(e,t)=>({uiType:Du(),owner:e,name:t}),om=(e,t,o)=>({uiType:Du(),owner:e,name:t,config:o,validated:{}}),nm=e=>Y(e,(e=>e.fold(A.none,A.some,A.none,A.none).map((e=>ds(e.name,e.schema.concat([Pi(Ju())])))).toArray())),sm=e=>V(e,Gu),rm=(e,t,o)=>((e,t,o)=>{const n={},s={};return H(o,(e=>{e.fold((e=>{n[e.pname]=Au(!0,((t,o,n)=>e.factory.sketch(Qu(t,e,o,n))))}),(e=>{const o=t.parts[e.name];s[e.name]=x(e.factory.sketch(Qu(t,e,o[Ju()]),o))}),(e=>{n[e.pname]=Au(!1,((t,o,n)=>e.factory.sketch(Qu(t,e,o,n))))}),(e=>{n[e.pname]=Mu(!0,((t,o,n)=>{const s=t[e.name];return V(s,(o=>e.factory.sketch(vn(e.defaults(t,o,n),o,e.overrides(t,o)))))}))}))})),{internals:x(n),externals:x(s)}})(0,t,o),am=(e,t,o)=>((e,t,o,n)=>{const s=ce(n,((e,t)=>((e,t)=>{let o=!1;return{name:x(e),required:()=>t.fold(((e,t)=>e),((e,t)=>e)),used:()=>o,replace:()=>{if(o)throw new Error("Trying to use the same placeholder more than once: "+e);return o=!0,t}}})(t,e))),r=((e,t,o,n)=>Y(o,(o=>Eu(e,t,o,n))))(e,t,o,s);return le(s,(o=>{if(!1===o.used()&&o.required())throw new Error("Placeholder: "+o.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))})),r})(A.some(e),t,t.components,o),im=(e,t,o)=>{const n=t.partUids[o];return e.getSystem().getByUid(n).toOptional()},lm=(e,t,o)=>im(e,t,o).getOrDie("Could not find part: "+o),cm=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return H(o,(e=>{n[e]=x(r.getByUid(s[e]))})),n},dm=(e,t)=>{const o=e.getSystem();return ce(t.partUids,((e,t)=>x(o.getByUid(e))))},um=e=>ae(e.partUids),mm=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return H(o,(e=>{n[e]=x(r.getByUid(s[e]).getOrDie())})),n},gm=(e,t)=>{const o=sm(t);return Is(V(o,(t=>({key:t,value:e+"-"+t}))))},pm=e=>ts("partUids","partUids",Sn((t=>gm(t.uid,e))),zn());var hm=Object.freeze({__proto__:null,generate:em,generateOne:om,schemas:nm,names:sm,substitutes:rm,components:am,defaultUids:gm,defaultUidsSchema:pm,getAllParts:dm,getAllPartNames:um,getPart:im,getPartOrDie:lm,getParts:cm,getPartsOrDie:mm});const fm=(e,t,o,n,s)=>{const r=((e,t)=>(e.length>0?[ds("parts",e)]:[]).concat([ss("uid"),ws("dom",{}),ws("components",[]),Pi("originalSpec"),ws("debug.sketcher",{})]).concat(t))(n,s);return Jn(e+" [SpecSchema]",Bn(r.concat(t)),o)},bm=(e,t,o,n,s)=>{const r=vm(s),a=nm(o),i=pm(o),l=fm(e,t,r,a,[i]),c=rm(0,l,o);return n(l,am(e,l,c.internals()),r,c.externals())},vm=e=>(e=>ve(e,"uid"))(e)?e:{...e,uid:ba("uid")},ym=Bn([ss("name"),ss("factory"),ss("configFields"),ws("apis",{}),ws("extraApis",{})]),xm=Bn([ss("name"),ss("factory"),ss("configFields"),ss("partFields"),ws("apis",{}),ws("extraApis",{})]),wm=e=>{const t=Jn("Sketcher for "+e.name,ym,e),o=ce(t.apis,_a),n=ce(t.extraApis,((e,t)=>Sa(e,t)));return{name:t.name,configFields:t.configFields,sketch:e=>((e,t,o,n)=>{const s=vm(n);return o(fm(e,t,s,[],[]),s)})(t.name,t.configFields,t.factory,e),...o,...n}},Sm=e=>{const t=Jn("Sketcher for "+e.name,xm,e),o=em(t.name,t.partFields),n=ce(t.apis,_a),s=ce(t.extraApis,((e,t)=>Sa(e,t)));return{name:t.name,partFields:t.partFields,configFields:t.configFields,sketch:e=>bm(t.name,t.configFields,t.partFields,t.factory,e),parts:o,...n,...s}},km=e=>Ke("input")(e)&&"radio"!==Tt(e,"type")||Ke("textarea")(e);var Cm=Object.freeze({__proto__:null,getCurrent:(e,t,o)=>t.find(e)});const Om=[ss("find")],_m=Ml({fields:Om,name:"composing",apis:Cm}),Tm=["input","button","textarea","select"],Em=(e,t,o)=>{(t.disabled()?Fm:Rm)(e,t)},Am=(e,t)=>!0===t.useNative&&R(Tm,We(e.element)),Mm=e=>{Ot(e.element,"disabled","disabled")},Dm=e=>{Mt(e.element,"disabled")},Bm=e=>{Ot(e.element,"aria-disabled","true")},Im=e=>{Ot(e.element,"aria-disabled","false")},Fm=(e,t,o)=>{t.disableClass.each((t=>{ja(e.element,t)})),(Am(e,t)?Mm:Bm)(e),t.onDisabled(e)},Rm=(e,t,o)=>{t.disableClass.each((t=>{$a(e.element,t)})),(Am(e,t)?Dm:Im)(e),t.onEnabled(e)},Nm=(e,t)=>Am(e,t)?(e=>At(e.element,"disabled"))(e):(e=>"true"===Tt(e.element,"aria-disabled"))(e);var Lm=Object.freeze({__proto__:null,enable:Rm,disable:Fm,isDisabled:Nm,onLoad:Em,set:(e,t,o,n)=>{(n?Fm:Rm)(e,t)}}),zm=Object.freeze({__proto__:null,exhibit:(e,t)=>Ma({classes:t.disabled()?t.disableClass.toArray():[]}),events:(e,t)=>Pr([Ur(gr(),((t,o)=>Nm(t,e))),Ol(e,t,Em)])}),Vm=[Ts("disabled",T),ws("useNative",!0),gs("disableClass"),Ni("onDisabled"),Ni("onEnabled")];const Hm=Ml({fields:Vm,name:"disabling",active:zm,apis:Lm}),Pm=(e,t,o,n)=>{const s=Qc(e.element,"."+t.highlightClass);H(s,(o=>{N(n,(e=>et(e.element,o)))||($a(o,t.highlightClass),e.getSystem().getByDom(o).each((o=>{t.onDehighlight(e,o),Rr(o,Fr())})))}))},Um=(e,t,o,n)=>{Pm(e,t,0,[n]),Wm(e,t,o,n)||(ja(n.element,t.highlightClass),t.onHighlight(e,n),Rr(n,Ir()))},Wm=(e,t,o,n)=>qa(n.element,t.highlightClass),jm=(e,t,o)=>yi(e.element,"."+t.itemClass).bind((t=>e.getSystem().getByDom(t).toOptional())),Gm=(e,t,o)=>{const n=Qc(e.element,"."+t.itemClass);return(n.length>0?A.some(n[n.length-1]):A.none()).bind((t=>e.getSystem().getByDom(t).toOptional()))},$m=(e,t,o,n)=>{const s=Qc(e.element,"."+t.itemClass);return $(s,(e=>qa(e,t.highlightClass))).bind((t=>{const o=Qi(t,n,0,s.length-1);return e.getSystem().getByDom(s[o]).toOptional()}))},qm=(e,t,o)=>{const n=Qc(e.element,"."+t.itemClass);return we(V(n,(t=>e.getSystem().getByDom(t).toOptional())))};var Ym=Object.freeze({__proto__:null,dehighlightAll:(e,t,o)=>Pm(e,t,0,[]),dehighlight:(e,t,o,n)=>{Wm(e,t,o,n)&&($a(n.element,t.highlightClass),t.onDehighlight(e,n),Rr(n,Fr()))},highlight:Um,highlightFirst:(e,t,o)=>{jm(e,t).each((n=>{Um(e,t,o,n)}))},highlightLast:(e,t,o)=>{Gm(e,t).each((n=>{Um(e,t,o,n)}))},highlightAt:(e,t,o,n)=>{((e,t,o,n)=>{const s=Qc(e.element,"."+t.itemClass);return A.from(s[n]).fold((()=>an.error(new Error("No element found with index "+n))),e.getSystem().getByDom)})(e,t,0,n).fold((e=>{throw e}),(n=>{Um(e,t,o,n)}))},highlightBy:(e,t,o,n)=>{const s=qm(e,t);G(s,n).each((n=>{Um(e,t,o,n)}))},isHighlighted:Wm,getHighlighted:(e,t,o)=>yi(e.element,"."+t.highlightClass).bind((t=>e.getSystem().getByDom(t).toOptional())),getFirst:jm,getLast:Gm,getPrevious:(e,t,o)=>$m(e,t,0,-1),getNext:(e,t,o)=>$m(e,t,0,1),getCandidates:qm}),Xm=[ss("highlightClass"),ss("itemClass"),Ni("onHighlight"),Ni("onDehighlight")];const Km=Ml({fields:Xm,name:"highlighting",apis:Ym}),Jm=[8],Zm=[9],Qm=[13],eg=[27],tg=[32],og=[37],ng=[38],sg=[39],rg=[40],ag=(e,t,o)=>{const n=K(e.slice(0,t)),s=K(e.slice(t+1));return G(n.concat(s),o)},ig=(e,t,o)=>{const n=K(e.slice(0,t));return G(n,o)},lg=(e,t,o)=>{const n=e.slice(0,t),s=e.slice(t+1);return G(s.concat(n),o)},cg=(e,t,o)=>{const n=e.slice(t+1);return G(n,o)},dg=e=>t=>{const o=t.raw;return R(e,o.which)},ug=e=>t=>X(e,(e=>e(t))),mg=e=>!0===e.raw.shiftKey,gg=e=>!0===e.raw.ctrlKey,pg=C(mg),hg=(e,t)=>({matches:e,classification:t}),fg=(e,t,o)=>{t.exists((e=>o.exists((t=>et(t,e)))))||Nr(e,Er(),{prevFocus:t,newFocus:o})},bg=()=>{const e=e=>Hl(e.element);return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().triggerFocus(o,t.element);const s=e(t);fg(t,n,s)}}},vg=()=>{const e=e=>Km.getHighlighted(e).map((e=>e.element));return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().getByDom(o).fold(b,(e=>{Km.highlight(t,e)}));const s=e(t);fg(t,n,s)}}};var yg;!function(e){e.OnFocusMode="onFocus",e.OnEnterOrSpaceMode="onEnterOrSpace",e.OnApiMode="onApi"}(yg||(yg={}));const xg=(e,t,o,n,s)=>{const r=(e,t,o,n,s)=>{return(r=o(e,t,n,s),a=t.event,G(r,(e=>e.matches(a))).map((e=>e.classification))).bind((o=>o(e,t,n,s)));var r,a},a={schema:()=>e.concat([ws("focusManager",bg()),Ss("focusInside","onFocus",qn((e=>R(["onFocus","onEnterOrSpace","onApi"],e)?an.value(e):an.error("Invalid value for focusInside")))),Hi("handler",a),Hi("state",t),Hi("sendFocusIn",s)]),processKey:r,toEvents:(e,t)=>{const a=e.focusInside!==yg.OnFocusMode?A.none():s(e).map((o=>jr(cr(),((n,s)=>{o(n,e,t),s.stop()})))),i=[jr(Zs(),((n,a)=>{r(n,a,o,e,t).fold((()=>{((o,n)=>{const r=dg(tg.concat(Qm))(n.event);e.focusInside===yg.OnEnterOrSpaceMode&&r&&Ls(o,n)&&s(e).each((s=>{s(o,e,t),n.stop()}))})(n,a)}),(e=>{a.stop()}))})),jr(Qs(),((o,s)=>{r(o,s,n,e,t).each((e=>{s.stop()}))}))];return Pr(a.toArray().concat(i))}};return a},wg=e=>{const t=[gs("onEscape"),gs("onEnter"),ws("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),ws("firstTabstop",0),ws("useTabstopAt",E),gs("visibilitySelector")].concat([e]),o=(e,t)=>{const o=e.visibilitySelector.bind((e=>xi(t,e))).getOr(t);return Gt(o)>0},n=(e,t)=>t.focusManager.get(e).bind((e=>xi(e,t.selector))),s=(e,t,n)=>{((e,t)=>{const n=Qc(e.element,t.selector),s=U(n,(e=>o(t,e)));return A.from(s[t.firstTabstop])})(e,t).each((o=>{t.focusManager.set(e,o)}))},r=(e,t,s,r)=>{const a=Qc(e.element,s.selector);return n(e,s).bind((t=>$(a,k(et,t)).bind((t=>((e,t,n,s,r)=>r(t,n,(e=>((e,t)=>o(e,t)&&e.useTabstopAt(t))(s,e))).fold((()=>s.cyclic?A.some(!0):A.none()),(t=>(s.focusManager.set(e,t),A.some(!0)))))(e,a,t,s,r)))))},a=(e,t,o)=>{const n=o.cyclic?ag:ig;return r(e,0,o,n)},i=(e,t,o)=>{const n=o.cyclic?lg:cg;return r(e,0,o,n)},l=x([hg(ug([mg,dg(Zm)]),a),hg(dg(Zm),i),hg(ug([pg,dg(Qm)]),((e,t,o)=>o.onEnter.bind((o=>o(e,t)))))]),c=x([hg(dg(eg),((e,t,o)=>o.onEscape.bind((o=>o(e,t))))),hg(dg(Zm),((e,t,o)=>n(e,o).filter((e=>!o.useTabstopAt(e))).bind((n=>((e=>(e=>at(e))(e).bind(ut).exists((t=>et(t,e))))(n)?a:i)(e,t,o)))))]);return xg(t,Ta.init,l,c,(()=>A.some(s)))};var Sg=wg(os("cyclic",T)),kg=wg(os("cyclic",E));const Cg=(e,t,o)=>km(o)&&dg(tg)(t.event)?A.none():((e,t,o)=>(zr(e,o,gr()),A.some(!0)))(e,0,o),Og=(e,t)=>A.some(!0),_g=[ws("execute",Cg),ws("useSpace",!1),ws("useEnter",!0),ws("useControlEnter",!1),ws("useDown",!1)],Tg=(e,t,o)=>o.execute(e,t,e.element);var Eg=xg(_g,Ta.init,((e,t,o,n)=>{const s=o.useSpace&&!km(e.element)?tg:[],r=o.useEnter?Qm:[],a=o.useDown?rg:[],i=s.concat(r).concat(a);return[hg(dg(i),Tg)].concat(o.useControlEnter?[hg(ug([gg,dg(Qm)]),Tg)]:[])}),((e,t,o,n)=>o.useSpace&&!km(e.element)?[hg(dg(tg),Og)]:[]),(()=>A.none()));const Ag=()=>{const e=sc();return Ea({readState:()=>e.get().map((e=>({numRows:String(e.numRows),numColumns:String(e.numColumns)}))).getOr({numRows:"?",numColumns:"?"}),setGridSize:(t,o)=>{e.set({numRows:t,numColumns:o})},getNumRows:()=>e.get().map((e=>e.numRows)),getNumColumns:()=>e.get().map((e=>e.numColumns))})};var Mg=Object.freeze({__proto__:null,flatgrid:Ag,init:e=>e.state(e)});const Dg=e=>(t,o,n,s)=>{const r=e(t.element);return Rg(r,t,o,n,s)},Bg=(e,t)=>{const o=wc(e,t);return Dg(o)},Ig=(e,t)=>{const o=wc(t,e);return Dg(o)},Fg=e=>(t,o,n,s)=>Rg(e,t,o,n,s),Rg=(e,t,o,n,s)=>n.focusManager.get(t).bind((o=>e(t.element,o,n,s))).map((e=>(n.focusManager.set(t,e),!0))),Ng=Fg,Lg=Fg,zg=Fg,Vg=e=>!(e=>e.offsetWidth<=0&&e.offsetHeight<=0)(e.dom),Hg=(e,t,o)=>{const n=Qc(e,o);return((e,o)=>$(e,(e=>et(e,t))).map((t=>({index:t,candidates:e}))))(U(n,Vg))},Pg=(e,t)=>$(e,(e=>et(t,e))),Ug=(e,t,o,n)=>n(Math.floor(t/o),t%o).bind((t=>{const n=t.row*o+t.column;return n>=0&&n<e.length?A.some(e[n]):A.none()})),Wg=(e,t,o,n,s)=>Ug(e,t,n,((t,r)=>{const a=t===o-1?e.length-t*n:n,i=Qi(r,s,0,a-1);return A.some({row:t,column:i})})),jg=(e,t,o,n,s)=>Ug(e,t,n,((t,r)=>{const a=Qi(t,s,0,o-1),i=a===o-1?e.length-a*n:n,l=el(r,0,i-1);return A.some({row:a,column:l})})),Gg=[ss("selector"),ws("execute",Cg),Li("onEscape"),ws("captureTab",!1),Ui()],$g=(e,t,o)=>{yi(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},qg=e=>(t,o,n,s)=>Hg(t,o,n.selector).bind((t=>e(t.candidates,t.index,s.getNumRows().getOr(n.initSize.numRows),s.getNumColumns().getOr(n.initSize.numColumns)))),Yg=(e,t,o)=>o.captureTab?A.some(!0):A.none(),Xg=qg(((e,t,o,n)=>Wg(e,t,o,n,-1))),Kg=qg(((e,t,o,n)=>Wg(e,t,o,n,1))),Jg=qg(((e,t,o,n)=>jg(e,t,o,n,-1))),Zg=qg(((e,t,o,n)=>jg(e,t,o,n,1))),Qg=x([hg(dg(og),Bg(Xg,Kg)),hg(dg(sg),Ig(Xg,Kg)),hg(dg(ng),Ng(Jg)),hg(dg(rg),Lg(Zg)),hg(ug([mg,dg(Zm)]),Yg),hg(ug([pg,dg(Zm)]),Yg),hg(dg(tg.concat(Qm)),((e,t,o,n)=>((e,t)=>t.focusManager.get(e).bind((e=>xi(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n)))))]),ep=x([hg(dg(eg),((e,t,o)=>o.onEscape(e,t))),hg(dg(tg),Og)]);var tp=xg(Gg,Ag,Qg,ep,(()=>A.some($g)));const op=(e,t,o,n,s)=>{const r=(e,t,o)=>s(e,t,n,0,o.length-1,o[t],(t=>{return n=o[t],"button"===We(n)&&"disabled"===Tt(n,"disabled")?r(e,t,o):A.from(o[t]);var n}));return Hg(e,o,t).bind((e=>{const t=e.index,o=e.candidates;return r(t,t,o)}))},np=(e,t,o,n)=>op(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=el(t+o,n,s);return i===e?A.from(r):a(i)})),sp=(e,t,o,n)=>op(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=Qi(t,o,n,s);return i===e?A.none():a(i)})),rp=[ss("selector"),ws("getInitial",A.none),ws("execute",Cg),Li("onEscape"),ws("executeOnMove",!1),ws("allowVertical",!0),ws("allowHorizontal",!0),ws("cycles",!0)],ap=(e,t,o)=>((e,t)=>t.focusManager.get(e).bind((e=>xi(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n))),ip=(e,t,o)=>{t.getInitial(e).orThunk((()=>yi(e.element,t.selector))).each((o=>{t.focusManager.set(e,o)}))},lp=(e,t,o)=>(o.cycles?sp:np)(e,o.selector,t,-1),cp=(e,t,o)=>(o.cycles?sp:np)(e,o.selector,t,1),dp=e=>(t,o,n,s)=>e(t,o,n,s).bind((()=>n.executeOnMove?ap(t,o,n):A.some(!0))),up=x([hg(dg(tg),Og),hg(dg(eg),((e,t,o)=>o.onEscape(e,t)))]);var mp=xg(rp,Ta.init,((e,t,o,n)=>{const s=[...o.allowHorizontal?og:[]].concat(o.allowVertical?ng:[]),r=[...o.allowHorizontal?sg:[]].concat(o.allowVertical?rg:[]);return[hg(dg(s),dp(Bg(lp,cp))),hg(dg(r),dp(Ig(lp,cp))),hg(dg(Qm),ap),hg(dg(tg),ap)]}),up,(()=>A.some(ip)));const gp=(e,t,o)=>A.from(e[t]).bind((e=>A.from(e[o]).map((e=>({rowIndex:t,columnIndex:o,cell:e}))))),pp=(e,t,o,n)=>{const s=e[t].length,r=Qi(o,n,0,s-1);return gp(e,t,r)},hp=(e,t,o,n)=>{const s=Qi(o,n,0,e.length-1),r=e[s].length,a=el(t,0,r-1);return gp(e,s,a)},fp=(e,t,o,n)=>{const s=e[t].length,r=el(o+n,0,s-1);return gp(e,t,r)},bp=(e,t,o,n)=>{const s=el(o+n,0,e.length-1),r=e[s].length,a=el(t,0,r-1);return gp(e,s,a)},vp=[ds("selectors",[ss("row"),ss("cell")]),ws("cycles",!0),ws("previousSelector",A.none),ws("execute",Cg)],yp=(e,t,o)=>{t.previousSelector(e).orThunk((()=>{const o=t.selectors;return yi(e.element,o.cell)})).each((o=>{t.focusManager.set(e,o)}))},xp=(e,t)=>(o,n,s)=>{const r=s.cycles?e:t;return xi(n,s.selectors.row).bind((e=>{const t=Qc(e,s.selectors.cell);return Pg(t,n).bind((t=>{const n=Qc(o,s.selectors.row);return Pg(n,e).bind((e=>{const o=((e,t)=>V(e,(e=>Qc(e,t.selectors.cell))))(n,s);return r(o,e,t).map((e=>e.cell))}))}))}))},wp=xp(((e,t,o)=>pp(e,t,o,-1)),((e,t,o)=>fp(e,t,o,-1))),Sp=xp(((e,t,o)=>pp(e,t,o,1)),((e,t,o)=>fp(e,t,o,1))),kp=xp(((e,t,o)=>hp(e,o,t,-1)),((e,t,o)=>bp(e,o,t,-1))),Cp=xp(((e,t,o)=>hp(e,o,t,1)),((e,t,o)=>bp(e,o,t,1))),Op=x([hg(dg(og),Bg(wp,Sp)),hg(dg(sg),Ig(wp,Sp)),hg(dg(ng),Ng(kp)),hg(dg(rg),Lg(Cp)),hg(dg(tg.concat(Qm)),((e,t,o)=>Hl(e.element).bind((n=>o.execute(e,t,n)))))]),_p=x([hg(dg(tg),Og)]);var Tp=xg(vp,Ta.init,Op,_p,(()=>A.some(yp)));const Ep=[ss("selector"),ws("execute",Cg),ws("moveOnTab",!1)],Ap=(e,t,o)=>o.focusManager.get(e).bind((n=>o.execute(e,t,n))),Mp=(e,t,o)=>{yi(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},Dp=(e,t,o)=>sp(e,o.selector,t,-1),Bp=(e,t,o)=>sp(e,o.selector,t,1),Ip=x([hg(dg(ng),zg(Dp)),hg(dg(rg),zg(Bp)),hg(ug([mg,dg(Zm)]),((e,t,o,n)=>o.moveOnTab?zg(Dp)(e,t,o,n):A.none())),hg(ug([pg,dg(Zm)]),((e,t,o,n)=>o.moveOnTab?zg(Bp)(e,t,o,n):A.none())),hg(dg(Qm),Ap),hg(dg(tg),Ap)]),Fp=x([hg(dg(tg),Og)]);var Rp=xg(Ep,Ta.init,Ip,Fp,(()=>A.some(Mp)));const Np=[Li("onSpace"),Li("onEnter"),Li("onShiftEnter"),Li("onLeft"),Li("onRight"),Li("onTab"),Li("onShiftTab"),Li("onUp"),Li("onDown"),Li("onEscape"),ws("stopSpaceKeyup",!1),gs("focusIn")];var Lp=xg(Np,Ta.init,((e,t,o)=>[hg(dg(tg),o.onSpace),hg(ug([pg,dg(Qm)]),o.onEnter),hg(ug([mg,dg(Qm)]),o.onShiftEnter),hg(ug([mg,dg(Zm)]),o.onShiftTab),hg(ug([pg,dg(Zm)]),o.onTab),hg(dg(ng),o.onUp),hg(dg(rg),o.onDown),hg(dg(og),o.onLeft),hg(dg(sg),o.onRight),hg(dg(tg),o.onSpace)]),((e,t,o)=>[...o.stopSpaceKeyup?[hg(dg(tg),Og)]:[],hg(dg(eg),o.onEscape)]),(e=>e.focusIn));const zp=Sg.schema(),Vp=kg.schema(),Hp=mp.schema(),Pp=tp.schema(),Up=Tp.schema(),Wp=Eg.schema(),jp=Rp.schema(),Gp=Lp.schema(),$p=Bl({branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:zp,cyclic:Vp,flow:Hp,flatgrid:Pp,matrix:Up,execution:Wp,menu:jp,special:Gp}),name:"keying",active:{events:(e,t)=>e.handler.toEvents(e,t)},apis:{focusIn:(e,t,o)=>{t.sendFocusIn(t).fold((()=>{e.getSystem().triggerFocus(e.element,e.element)}),(n=>{n(e,t,o)}))},setGridSize:(e,t,o,n,s)=>{(e=>ye(e,"setGridSize"))(o)?o.setGridSize(n,s):console.error("Layout does not support setGridSize")}},state:Mg}),qp=(e,t)=>{Pl((()=>{((e,t,o)=>{const n=e.components();(e=>{H(e.components(),(e=>Wo(e.element))),Uo(e.element),e.syncComponents()})(e);const s=o(t),r=J(n,s);H(r,(t=>{Ad(t),e.getSystem().removeFromWorld(t)})),H(s,(t=>{Ed(t)?Id(e,t):(e.getSystem().addToWorld(t),Id(e,t),wt(e.element)&&Md(t))})),e.syncComponents()})(e,t,(()=>V(t,e.getSystem().build)))}),e.element)},Yp=(e,t)=>{Pl((()=>{((o,n,s)=>{const r=o.components(),a=Y(n,(e=>Oa(e).toArray()));H(r,(e=>{R(a,e)||Bd(e)}));const i=((e,t,o)=>ei(e,t,((t,n)=>ti(e,n,t,o))))(e.element,t,e.getSystem().buildOrPatch),l=J(r,i);H(l,(e=>{Ed(e)&&Bd(e)})),H(i,(e=>{Ed(e)||Dd(o,e)})),o.syncComponents()})(e,t)}),e.element)},Xp=(e,t,o,n)=>{Bd(t);const s=ti(e.element,o,n,e.getSystem().buildOrPatch);Dd(e,s),e.syncComponents()},Kp=(e,t,o)=>{const n=e.getSystem().build(o);Rd(e,n,t)},Jp=(e,t,o,n)=>{Ld(t),Kp(e,((e,t)=>((e,t,o)=>{dt(e,o).fold((()=>{Ho(e,t)}),(e=>{Lo(e,t)}))})(e,t,o)),n)},Zp=(e,t)=>e.components(),Qp=(e,t,o,n,s)=>{const r=Zp(e);return A.from(r[n]).map((o=>(s.fold((()=>Ld(o)),(s=>{(t.reuseDom?Xp:Jp)(e,o,n,s)})),o)))};var eh=Object.freeze({__proto__:null,append:(e,t,o,n)=>{Kp(e,Ho,n)},prepend:(e,t,o,n)=>{Kp(e,Vo,n)},remove:(e,t,o,n)=>{const s=Zp(e),r=G(s,(e=>et(n.element,e.element)));r.each(Ld)},replaceAt:Qp,replaceBy:(e,t,o,n,s)=>{const r=Zp(e);return $(r,n).bind((o=>Qp(e,t,0,o,s)))},set:(e,t,o,n)=>(t.reuseDom?Yp:qp)(e,n),contents:Zp});const th=Ml({fields:[_s("reuseDom",!0)],name:"replacing",apis:eh}),oh=(e,t)=>{const o=((e,t)=>{const o=Pr(t);return Ml({fields:[ss("enabled")],name:e,active:{events:x(o)}})})(e,t);return{key:e,value:{config:{},me:o,configAsRaw:x({}),initialConfig:{},state:Ta}}},nh=(e,t)=>{t.ignore||(Nl(e.element),t.onFocus(e))};var sh=Object.freeze({__proto__:null,focus:nh,blur:(e,t)=>{t.ignore||Ll(e.element)},isFocused:e=>zl(e.element)}),rh=Object.freeze({__proto__:null,exhibit:(e,t)=>{const o=t.ignore?{}:{attributes:{tabindex:"-1"}};return Ma(o)},events:e=>Pr([jr(cr(),((t,o)=>{nh(t,e),o.stop()}))].concat(e.stopMousedown?[jr(Gs(),((e,t)=>{t.event.prevent()}))]:[]))}),ah=[Ni("onFocus"),ws("stopMousedown",!1),ws("ignore",!1)];const ih=Ml({fields:ah,name:"focusing",active:rh,apis:sh}),lh=(e,t,o,n)=>{const s=o.get();o.set(n),((e,t,o)=>{t.toggleClass.each((t=>{o.get()?ja(e.element,t):$a(e.element,t)}))})(e,t,o),((e,t,o)=>{const n=t.aria;n.update(e,n,o.get())})(e,t,o),s!==n&&t.onToggled(e,n)},ch=(e,t,o)=>{lh(e,t,o,!o.get())},dh=(e,t,o)=>{lh(e,t,o,t.selected)};var uh=Object.freeze({__proto__:null,onLoad:dh,toggle:ch,isOn:(e,t,o)=>o.get(),on:(e,t,o)=>{lh(e,t,o,!0)},off:(e,t,o)=>{lh(e,t,o,!1)},set:lh}),mh=Object.freeze({__proto__:null,exhibit:()=>Ma({}),events:(e,t)=>{const o=(n=e,s=t,r=ch,ta((e=>{r(e,n,s)})));var n,s,r;const a=Ol(e,t,dh);return Pr(q([e.toggleOnExecute?[o]:[],[a]]))}});const gh=(e,t,o)=>{Ot(e.element,"aria-expanded",o)};var ph=[ws("selected",!1),gs("toggleClass"),ws("toggleOnExecute",!0),Ni("onToggled"),Ss("aria",{mode:"none"},Qn("mode",{pressed:[ws("syncWithExpanded",!1),Hi("update",((e,t,o)=>{Ot(e.element,"aria-pressed",o),t.syncWithExpanded&&gh(e,0,o)}))],checked:[Hi("update",((e,t,o)=>{Ot(e.element,"aria-checked",o)}))],expanded:[Hi("update",gh)],selected:[Hi("update",((e,t,o)=>{Ot(e.element,"aria-selected",o)}))],none:[Hi("update",b)]}))];const hh=Ml({fields:ph,name:"toggling",active:mh,apis:uh,state:(!1,{init:()=>{const e=Ms(false);return{get:()=>e.get(),set:t=>e.set(t),clear:()=>e.set(false),readState:()=>e.get()}}})});const fh=()=>{const e=(e,t)=>{t.stop(),Lr(e)};return[jr(or(),e),jr(hr(),e),Xr(Ps()),Xr(Gs())]},bh=e=>Pr(q([e.map((e=>ta(((t,o)=>{e(t),o.stop()})))).toArray(),fh()])),vh="alloy.item-hover",yh="alloy.item-focus",xh="alloy.item-toggled",wh=e=>{(Hl(e.element).isNone()||ih.isFocused(e))&&(ih.isFocused(e)||ih.focus(e),Nr(e,vh,{item:e}))},Sh=e=>{Nr(e,yh,{item:e})},kh=x(vh),Ch=x(yh),Oh=x(xh),_h=e=>e.toggling.map((e=>e.exclusive?"menuitemradio":"menuitemcheckbox")).getOr("menuitem"),Th=[ss("data"),ss("components"),ss("dom"),ws("hasSubmenu",!1),gs("toggling"),ku("itemBehaviours",[hh,ih,$p,yu]),ws("ignoreFocus",!1),ws("domModification",{}),Hi("builder",(e=>({dom:e.dom,domModification:{...e.domModification,attributes:{role:_h(e),...e.domModification.attributes,"aria-haspopup":e.hasSubmenu,...e.hasSubmenu?{"aria-expanded":!1}:{}}},behaviours:Cu(e.itemBehaviours,[e.toggling.fold(hh.revoke,(e=>hh.config((e=>({aria:{mode:"checked"},...ge(e,((e,t)=>"exclusive"!==t)),onToggled:(t,o)=>{p(e.onToggled)&&e.onToggled(t,o),((e,t)=>{Nr(e,xh,{item:e,state:t})})(t,o)}}))(e)))),ih.config({ignore:e.ignoreFocus,stopMousedown:e.ignoreFocus,onFocus:e=>{Sh(e)}}),$p.config({mode:"execution"}),yu.config({store:{mode:"memory",initialValue:e.data}}),oh("item-type-events",[...fh(),jr(Xs(),wh),jr(pr(),ih.focus)])]),components:e.components,eventOrder:e.eventOrder}))),ws("eventOrder",{})],Eh=[ss("dom"),ss("components"),Hi("builder",(e=>({dom:e.dom,components:e.components,events:Pr([Kr(pr())])})))],Ah=x("item-widget"),Mh=x([qu({name:"widget",overrides:e=>({behaviours:El([yu.config({store:{mode:"manual",getValue:t=>e.data,setValue:b}})])})})]),Dh=[ss("uid"),ss("data"),ss("components"),ss("dom"),ws("autofocus",!1),ws("ignoreFocus",!1),ku("widgetBehaviours",[yu,ih,$p]),ws("domModification",{}),pm(Mh()),Hi("builder",(e=>{const t=rm(Ah(),e,Mh()),o=am(Ah(),e,t.internals()),n=t=>im(t,e,"widget").map((e=>($p.focusIn(e),e))),s=(t,o)=>km(o.event.target)?A.none():e.autofocus?(o.setSource(t.element),A.none()):A.none();return{dom:e.dom,components:o,domModification:e.domModification,events:Pr([ta(((e,t)=>{n(e).each((e=>{t.stop()}))})),jr(Xs(),wh),jr(pr(),((t,o)=>{e.autofocus?n(t):ih.focus(t)}))]),behaviours:Cu(e.widgetBehaviours,[yu.config({store:{mode:"memory",initialValue:e.data}}),ih.config({ignore:e.ignoreFocus,onFocus:e=>{Sh(e)}}),$p.config({mode:"special",focusIn:e.autofocus?e=>{n(e)}:Il(),onLeft:s,onRight:s,onEscape:(t,o)=>ih.isFocused(t)||e.autofocus?e.autofocus?(o.setSource(t.element),A.none()):A.none():(ih.focus(t),A.some(!0))})])}}))],Bh=Qn("type",{widget:Dh,item:Th,separator:Eh}),Ih=x([Ku({factory:{sketch:e=>{const t=Jn("menu.spec item",Bh,e);return t.builder(t)}},name:"items",unit:"item",defaults:(e,t)=>ve(t,"uid")?t:{...t,uid:ba("item")},overrides:(e,t)=>({type:t.type,ignoreFocus:e.fakeFocus,domModification:{classes:[e.markers.item]}})})]),Fh=x([ss("value"),ss("items"),ss("dom"),ss("components"),ws("eventOrder",{}),xu("menuBehaviours",[Km,yu,_m,$p]),Ss("movement",{mode:"menu",moveOnTab:!0},Qn("mode",{grid:[Ui(),Hi("config",((e,t)=>({mode:"flatgrid",selector:"."+e.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:e.focusManager})))],matrix:[Hi("config",((e,t)=>({mode:"matrix",selectors:{row:t.rowSelector,cell:"."+e.markers.item},previousSelector:t.previousSelector,focusManager:e.focusManager}))),ss("rowSelector"),ws("previousSelector",A.none)],menu:[ws("moveOnTab",!0),Hi("config",((e,t)=>({mode:"menu",selector:"."+e.markers.item,moveOnTab:t.moveOnTab,focusManager:e.focusManager})))]})),rs("markers",Di()),ws("fakeFocus",!1),ws("focusManager",bg()),Ni("onHighlight"),Ni("onDehighlight")]),Rh=x("alloy.menu-focus"),Nh=Sm({name:"Menu",configFields:Fh(),partFields:Ih(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,markers:e.markers,behaviours:Su(e.menuBehaviours,[Km.config({highlightClass:e.markers.selectedItem,itemClass:e.markers.item,onHighlight:e.onHighlight,onDehighlight:e.onDehighlight}),yu.config({store:{mode:"memory",initialValue:e.value}}),_m.config({find:A.some}),$p.config(e.movement.config(e,e.movement))]),events:Pr([jr(Ch(),((e,t)=>{const o=t.event;e.getSystem().getByDom(o.target).each((o=>{Km.highlight(e,o),t.stop(),Nr(e,Rh(),{menu:e,item:o})}))})),jr(kh(),((e,t)=>{const o=t.event.item;Km.highlight(e,o)})),jr(Oh(),((e,t)=>{const{item:o,state:n}=t.event;n&&"menuitemradio"===Tt(o.element,"role")&&((e,t)=>{const o=Qc(e.element,'[role="menuitemradio"][aria-checked="true"]');H(o,(o=>{et(o,t.element)||e.getSystem().getByDom(o).each((e=>{hh.off(e)}))}))})(e,o)}))]),components:t,eventOrder:e.eventOrder,domModification:{attributes:{role:"menu"}}})}),Lh=(e,t,o,n)=>be(o,n).bind((n=>be(e,n).bind((n=>{const s=Lh(e,t,o,n);return A.some([n].concat(s))})))).getOr([]),zh=e=>"prepared"===e.type?A.some(e.menu):A.none(),Vh=()=>{const e=Ms({}),t=Ms({}),o=Ms({}),n=sc(),s=Ms({}),r=e=>a(e).bind(zh),a=e=>be(t.get(),e),i=t=>be(e.get(),t);return{setMenuBuilt:(e,o)=>{t.set({...t.get(),[e]:{type:"prepared",menu:o}})},setContents:(r,a,i,l)=>{n.set(r),e.set(i),t.set(a),s.set(l);const c=((e,t)=>{const o={};le(e,((e,t)=>{H(e,(e=>{o[e]=t}))}));const n=t,s=de(t,((e,t)=>({k:e,v:t}))),r=ce(s,((e,t)=>[t].concat(Lh(o,n,s,t))));return ce(o,(e=>be(r,e).getOr([e])))})(l,i);o.set(c)},expand:t=>be(e.get(),t).map((e=>{const n=be(o.get(),t).getOr([]);return[e].concat(n)})),refresh:e=>be(o.get(),e),collapse:e=>be(o.get(),e).bind((e=>e.length>1?A.some(e.slice(1)):A.none())),lookupMenu:a,lookupItem:i,otherMenus:e=>{const t=s.get();return J(ae(t),e)},getPrimary:()=>n.get().bind(r),getMenus:()=>t.get(),clear:()=>{e.set({}),t.set({}),o.set({}),n.clear()},isClear:()=>n.get().isNone(),getTriggeringPath:(t,s)=>{const a=U(i(t).toArray(),(e=>r(e).isSome()));return be(o.get(),t).bind((t=>{const o=K(a.concat(t));return(e=>{const t=[];for(let o=0;o<e.length;o++){const n=e[o];if(!n.isSome())return A.none();t.push(n.getOrDie())}return A.some(t)})(Y(o,((t,a)=>((t,o,n)=>r(t).bind((s=>(t=>he(e.get(),((e,o)=>e===t)))(t).bind((e=>o(e).map((e=>({triggeredMenu:s,triggeringItem:e,triggeringPath:n}))))))))(t,s,o.slice(0,a+1)).fold((()=>xe(n.get(),t)?[]:[A.none()]),(e=>[A.some(e)])))))}))}}},Hh=zh,Ph=da("tiered-menu-item-highlight"),Uh=da("tiered-menu-item-dehighlight");var Wh;!function(e){e[e.HighlightMenuAndItem=0]="HighlightMenuAndItem",e[e.HighlightJustMenu=1]="HighlightJustMenu",e[e.HighlightNone=2]="HighlightNone"}(Wh||(Wh={}));const jh=x("collapse-item"),Gh=wm({name:"TieredMenu",configFields:[Vi("onExecute"),Vi("onEscape"),zi("onOpenMenu"),zi("onOpenSubmenu"),Ni("onRepositionMenu"),Ni("onCollapseMenu"),ws("highlightOnOpen",Wh.HighlightMenuAndItem),ds("data",[ss("primary"),ss("menus"),ss("expansions")]),ws("fakeFocus",!1),Ni("onHighlightItem"),Ni("onDehighlightItem"),Ni("onHover"),Ii(),ss("dom"),ws("navigateOnHover",!0),ws("stayInDom",!1),xu("tmenuBehaviours",[$p,Km,_m,th]),ws("eventOrder",{})],apis:{collapseMenu:(e,t)=>{e.collapseMenu(t)},highlightPrimary:(e,t)=>{e.highlightPrimary(t)},repositionMenus:(e,t)=>{e.repositionMenus(t)}},factory:(e,t)=>{const o=sc(),n=Vh(),s=e=>yu.getValue(e).value,r=t=>ce(e.data.menus,((e,t)=>Y(e.items,(e=>"separator"===e.type?[]:[e.data.value])))),a=Km.highlight,i=(t,o)=>{a(t,o),Km.getHighlighted(o).orThunk((()=>Km.getFirst(o))).each((n=>{e.fakeFocus?Km.highlight(o,n):zr(t,n.element,pr())}))},l=(e,t)=>we(V(t,(t=>e.lookupMenu(t).bind((e=>"prepared"===e.type?A.some(e.menu):A.none()))))),c=(t,o,n)=>{const s=l(o,o.otherMenus(n));H(s,(o=>{Xa(o.element,[e.markers.backgroundMenu]),e.stayInDom||th.remove(t,o)}))},d=(t,n)=>{const r=(t=>o.get().getOrThunk((()=>{const n={},r=Qc(t.element,`.${e.markers.item}`),a=U(r,(e=>"true"===Tt(e,"aria-haspopup")));return H(a,(e=>{t.getSystem().getByDom(e).each((e=>{const t=s(e);n[t]=e}))})),o.set(n),n})))(t);le(r,((e,t)=>{const o=R(n,t);Ot(e.element,"aria-expanded",o)}))},u=(t,o,n)=>A.from(n[0]).bind((s=>o.lookupMenu(s).bind((s=>{if("notbuilt"===s.type)return A.none();{const r=s.menu,a=l(o,n.slice(1));return H(a,(t=>{ja(t.element,e.markers.backgroundMenu)})),wt(r.element)||th.append(t,ui(r)),Xa(r.element,[e.markers.backgroundMenu]),i(t,r),c(t,o,n),A.some(r)}}))));let m;!function(e){e[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent"}(m||(m={}));const g=(t,o,r=m.HighlightSubmenu)=>{if(o.hasConfigured(Hm)&&Hm.isDisabled(o))return A.some(o);{const a=s(o);return n.expand(a).bind((s=>(d(t,s),A.from(s[0]).bind((a=>n.lookupMenu(a).bind((i=>{const l=((e,t,o)=>{if("notbuilt"===o.type){const s=e.getSystem().build(o.nbMenu());return n.setMenuBuilt(t,s),s}return o.menu})(t,a,i);return wt(l.element)||th.append(t,ui(l)),e.onOpenSubmenu(t,o,l,K(s)),r===m.HighlightSubmenu?(Km.highlightFirst(l),u(t,n,s)):(Km.dehighlightAll(l),A.some(o))})))))))}},p=(t,o)=>{const r=s(o);return n.collapse(r).bind((s=>(d(t,s),u(t,n,s).map((n=>(e.onCollapseMenu(t,o,n),n))))))},h=t=>(o,n)=>xi(n.getSource(),`.${e.markers.item}`).bind((e=>o.getSystem().getByDom(e).toOptional().bind((e=>t(o,e).map(E))))),f=Pr([jr(Rh(),((e,t)=>{const o=t.event.item;n.lookupItem(s(o)).each((()=>{const o=t.event.menu;Km.highlight(e,o);const r=s(t.event.item);n.refresh(r).each((t=>c(e,n,t)))}))})),ta(((t,o)=>{const n=o.event.target;t.getSystem().getByDom(n).each((o=>{0===s(o).indexOf("collapse-item")&&p(t,o),g(t,o,m.HighlightSubmenu).fold((()=>{e.onExecute(t,o)}),b)}))})),Zr(((t,o)=>{(t=>{const o=((t,o,n)=>ce(n,((n,s)=>{const r=()=>Nh.sketch({...n,value:s,markers:e.markers,fakeFocus:e.fakeFocus,onHighlight:(e,t)=>{Nr(e,Ph,{menuComp:e,itemComp:t})},onDehighlight:(e,t)=>{Nr(e,Uh,{menuComp:e,itemComp:t})},focusManager:e.fakeFocus?vg():bg()});return s===o?{type:"prepared",menu:t.getSystem().build(r())}:{type:"notbuilt",nbMenu:r}})))(t,e.data.primary,e.data.menus),s=r();return n.setContents(e.data.primary,o,e.data.expansions,s),n.getPrimary()})(t).each((o=>{th.append(t,ui(o)),e.onOpenMenu(t,o),e.highlightOnOpen===Wh.HighlightMenuAndItem?i(t,o):e.highlightOnOpen===Wh.HighlightJustMenu&&a(t,o)}))})),jr(Ph,((t,o)=>{e.onHighlightItem(t,o.event.menuComp,o.event.itemComp)})),jr(Uh,((t,o)=>{e.onDehighlightItem(t,o.event.menuComp,o.event.itemComp)})),...e.navigateOnHover?[jr(kh(),((t,o)=>{const r=o.event.item;((e,t)=>{const o=s(t);n.refresh(o).bind((t=>(d(e,t),u(e,n,t))))})(t,r),g(t,r,m.HighlightParent),e.onHover(t,r)}))]:[]]),v=e=>Km.getHighlighted(e).bind(Km.getHighlighted),y={collapseMenu:e=>{v(e).each((t=>{p(e,t)}))},highlightPrimary:e=>{n.getPrimary().each((t=>{i(e,t)}))},repositionMenus:t=>{const o=n.getPrimary().bind((e=>v(t).bind((e=>{const t=s(e),o=fe(n.getMenus()),r=we(V(o,Hh));return n.getTriggeringPath(t,(e=>((e,t,o)=>re(t,(e=>{if(!e.getSystem().isConnected())return A.none();const t=Km.getCandidates(e);return G(t,(e=>s(e)===o))})))(0,r,e)))})).map((t=>({primary:e,triggeringPath:t})))));o.fold((()=>{(e=>A.from(e.components()[0]).filter((e=>"menu"===Tt(e.element,"role"))))(t).each((o=>{e.onRepositionMenu(t,o,[])}))}),(({primary:o,triggeringPath:n})=>{e.onRepositionMenu(t,o,n)}))}};return{uid:e.uid,dom:e.dom,markers:e.markers,behaviours:Su(e.tmenuBehaviours,[$p.config({mode:"special",onRight:h(((e,t)=>km(t.element)?A.none():g(e,t,m.HighlightSubmenu))),onLeft:h(((e,t)=>km(t.element)?A.none():p(e,t))),onEscape:h(((t,o)=>p(t,o).orThunk((()=>e.onEscape(t,o).map((()=>t)))))),focusIn:(e,t)=>{n.getPrimary().each((t=>{zr(e,t.element,pr())}))}}),Km.config({highlightClass:e.markers.selectedMenu,itemClass:e.markers.menu}),_m.config({find:e=>Km.getHighlighted(e)}),th.config({})]),eventOrder:e.eventOrder,apis:y,events:f}},extraApis:{tieredData:(e,t,o)=>({primary:e,menus:t,expansions:o}),singleData:(e,t)=>({primary:e,menus:Bs(e,t),expansions:{}}),collapseItem:e=>({value:da(jh()),meta:{text:e}})}}),$h=wm({name:"InlineView",configFields:[ss("lazySink"),Ni("onShow"),Ni("onHide"),vs("onEscape"),xu("inlineBehaviours",[Qd,yu,Fl]),xs("fireDismissalEventInstead",[ws("event",_r())]),xs("fireRepositionEventInstead",[ws("event",Tr())]),ws("getRelated",A.none),ws("isExtraPart",T),ws("eventOrder",A.none)],factory:(e,t)=>{const o=(t,o,n,s)=>{const r=e.lazySink(t).getOrDie();Qd.openWhileCloaked(t,o,(()=>Td.positionWithinBounds(r,t,n,s()))),yu.setValue(t,A.some({mode:"position",config:n,getBounds:s}))},n=(t,o,n,s)=>{const r=((e,t,o,n,s)=>{const r=()=>e.lazySink(t),a="horizontal"===n.type?{layouts:{onLtr:()=>wl(),onRtl:()=>Sl()}}:{},i=e=>(e=>2===e.length)(e)?a:{};return Gh.sketch({dom:{tag:"div"},data:n.data,markers:n.menu.markers,highlightOnOpen:n.menu.highlightOnOpen,fakeFocus:n.menu.fakeFocus,onEscape:()=>(Qd.close(t),e.onEscape.map((e=>e(t))),A.some(!0)),onExecute:()=>A.some(!0),onOpenMenu:(e,t)=>{Td.positionWithinBounds(r().getOrDie(),t,o,s())},onOpenSubmenu:(e,t,o,n)=>{const s=r().getOrDie();Td.position(s,o,{anchor:{type:"submenu",item:t,...i(n)}})},onRepositionMenu:(e,t,n)=>{const a=r().getOrDie();Td.positionWithinBounds(a,t,o,s()),H(n,(e=>{const t=i(e.triggeringPath);Td.position(a,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem,...t}})}))}})})(e,t,o,n,s);Qd.open(t,r),yu.setValue(t,A.some({mode:"menu",menu:r}))},s=t=>{Qd.isOpen(t)&&yu.getValue(t).each((o=>{switch(o.mode){case"menu":Qd.getState(t).each(Gh.repositionMenus);break;case"position":const n=e.lazySink(t).getOrDie();Td.positionWithinBounds(n,t,o.config,o.getBounds())}}))},r={setContent:(e,t)=>{Qd.setContent(e,t)},showAt:(e,t,n)=>{const s=A.none;o(e,t,n,s)},showWithinBounds:o,showMenuAt:(e,t,o)=>{n(e,t,o,A.none)},showMenuWithinBounds:n,hide:e=>{Qd.isOpen(e)&&(yu.setValue(e,A.none()),Qd.close(e))},getContent:e=>Qd.getState(e),reposition:s,isOpen:Qd.isOpen};return{uid:e.uid,dom:e.dom,behaviours:Su(e.inlineBehaviours,[Qd.config({isPartOf:(t,o,n)=>ki(o,n)||((t,o)=>e.getRelated(t).exists((e=>ki(e,o))))(t,n),getAttachPoint:t=>e.lazySink(t).getOrDie(),onOpen:t=>{e.onShow(t)},onClose:t=>{e.onHide(t)}}),yu.config({store:{mode:"memory",initialValue:A.none()}}),Fl.config({channels:{...su({isExtraPart:t.isExtraPart,...e.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...au({...e.fireRepositionEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({}),doReposition:s})}})]),eventOrder:e.eventOrder,apis:r}},apis:{showAt:(e,t,o,n)=>{e.showAt(t,o,n)},showWithinBounds:(e,t,o,n,s)=>{e.showWithinBounds(t,o,n,s)},showMenuAt:(e,t,o,n)=>{e.showMenuAt(t,o,n)},showMenuWithinBounds:(e,t,o,n,s)=>{e.showMenuWithinBounds(t,o,n,s)},hide:(e,t)=>{e.hide(t)},isOpen:(e,t)=>e.isOpen(t),getContent:(e,t)=>e.getContent(t),setContent:(e,t,o)=>{e.setContent(t,o)},reposition:(e,t)=>{e.reposition(t)}}});var qh=tinymce.util.Tools.resolve("tinymce.util.Delay");const Yh=wm({name:"Button",factory:e=>{const t=bh(e.action),o=e.dom.tag,n=t=>be(e.dom,"attributes").bind((e=>be(e,t)));return{uid:e.uid,dom:e.dom,components:e.components,events:t,behaviours:Cu(e.buttonBehaviours,[ih.config({}),$p.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:"button"===o?{type:n("type").getOr("button"),...n("role").map((e=>({role:e}))).getOr({})}:{role:e.role.getOr(n("role").getOr("button"))}},eventOrder:e.eventOrder}},configFields:[ws("uid",void 0),ss("dom"),ws("components",[]),ku("buttonBehaviours",[ih,$p]),gs("action"),gs("role"),ws("eventOrder",{})]}),Xh=e=>{const t=Re(e),o=ct(t),n=(e=>{const t=void 0!==e.dom.attributes?e.dom.attributes:[];return j(t,((e,t)=>"class"===t.name?e:{...e,[t.name]:t.value}),{})})(t),s=(e=>Array.prototype.slice.call(e.dom.classList,0))(t),r=0===o.length?{}:{innerHtml:oa(t)};return{tag:We(t),classes:s,attributes:n,...r}},Kh=e=>{const t=(e=>void 0!==e.uid)(e)&&ye(e,"uid")?e.uid:ba("memento");return{get:e=>e.getSystem().getByUid(t).getOrDie(),getOpt:e=>e.getSystem().getByUid(t).toOptional(),asSpec:()=>({...e,uid:t})}},{entries:Jh,setPrototypeOf:Zh,isFrozen:Qh,getPrototypeOf:ef,getOwnPropertyDescriptor:tf}=Object;let{freeze:of,seal:nf,create:sf}=Object,{apply:rf,construct:af}="undefined"!=typeof Reflect&&Reflect;rf||(rf=function(e,t,o){return e.apply(t,o)}),of||(of=function(e){return e}),nf||(nf=function(e){return e}),af||(af=function(e,t){return new e(...t)});const lf=xf(Array.prototype.forEach),cf=xf(Array.prototype.pop),df=xf(Array.prototype.push),uf=xf(String.prototype.toLowerCase),mf=xf(String.prototype.toString),gf=xf(String.prototype.match),pf=xf(String.prototype.replace),hf=xf(String.prototype.indexOf),ff=xf(String.prototype.trim),bf=xf(RegExp.prototype.test),vf=(yf=TypeError,function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return af(yf,t)});var yf;function xf(e){return function(t){for(var o=arguments.length,n=new Array(o>1?o-1:0),s=1;s<o;s++)n[s-1]=arguments[s];return rf(e,t,n)}}function wf(e,t,o){var n;o=null!==(n=o)&&void 0!==n?n:uf,Zh&&Zh(e,null);let s=t.length;for(;s--;){let n=t[s];if("string"==typeof n){const e=o(n);e!==n&&(Qh(t)||(t[s]=e),n=e)}e[n]=!0}return e}function Sf(e){const t=sf(null);for(const[o,n]of Jh(e))t[o]=n;return t}function kf(e,t){for(;null!==e;){const o=tf(e,t);if(o){if(o.get)return xf(o.get);if("function"==typeof o.value)return xf(o.value)}e=ef(e)}return function(e){return console.warn("fallback value for",e),null}}const Cf=of(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Of=of(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),_f=of(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Tf=of(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Ef=of(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Af=of(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Mf=of(["#text"]),Df=of(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),Bf=of(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),If=of(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Ff=of(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Rf=nf(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Nf=nf(/<%[\w\W]*|[\w\W]*%>/gm),Lf=nf(/\${[\w\W]*}/gm),zf=nf(/^data-[\-\w.\u00B7-\uFFFF]/),Vf=nf(/^aria-[\-\w]+$/),Hf=nf(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Pf=nf(/^(?:\w+script|data):/i),Uf=nf(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Wf=nf(/^html$/i);var jf=Object.freeze({__proto__:null,MUSTACHE_EXPR:Rf,ERB_EXPR:Nf,TMPLIT_EXPR:Lf,DATA_ATTR:zf,ARIA_ATTR:Vf,IS_ALLOWED_URI:Hf,IS_SCRIPT_OR_DATA:Pf,ATTR_WHITESPACE:Uf,DOCTYPE_NAME:Wf});const Gf=()=>"undefined"==typeof window?null:window;var $f=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Gf();const o=t=>e(t);if(o.version="3.0.5",o.removed=[],!t||!t.document||9!==t.document.nodeType)return o.isSupported=!1,o;const n=t.document,s=n.currentScript;let{document:r}=t;const{DocumentFragment:a,HTMLTemplateElement:i,Node:l,Element:c,NodeFilter:d,NamedNodeMap:u=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:m,DOMParser:g,trustedTypes:p}=t,h=c.prototype,f=kf(h,"cloneNode"),b=kf(h,"nextSibling"),v=kf(h,"childNodes"),y=kf(h,"parentNode");if("function"==typeof i){const e=r.createElement("template");e.content&&e.content.ownerDocument&&(r=e.content.ownerDocument)}let x,w="";const{implementation:S,createNodeIterator:k,createDocumentFragment:C,getElementsByTagName:O}=r,{importNode:_}=n;let T={};o.isSupported="function"==typeof Jh&&"function"==typeof y&&S&&void 0!==S.createHTMLDocument;const{MUSTACHE_EXPR:E,ERB_EXPR:A,TMPLIT_EXPR:M,DATA_ATTR:D,ARIA_ATTR:B,IS_SCRIPT_OR_DATA:I,ATTR_WHITESPACE:F}=jf;let{IS_ALLOWED_URI:R}=jf,N=null;const L=wf({},[...Cf,...Of,..._f,...Ef,...Mf]);let z=null;const V=wf({},[...Df,...Bf,...If,...Ff]);let H=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),P=null,U=null,W=!0,j=!0,G=!1,$=!0,q=!1,Y=!1,X=!1,K=!1,J=!1,Z=!1,Q=!1,ee=!0,te=!1,oe=!0,ne=!1,se={},re=null;const ae=wf({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let ie=null;const le=wf({},["audio","video","img","source","image","track"]);let ce=null;const de=wf({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ue="http://www.w3.org/1998/Math/MathML",me="http://www.w3.org/2000/svg",ge="http://www.w3.org/1999/xhtml";let pe=ge,he=!1,fe=null;const be=wf({},[ue,me,ge],mf);let ve;const ye=["application/xhtml+xml","text/html"];let xe,we=null;const Se=r.createElement("form"),ke=function(e){return e instanceof RegExp||e instanceof Function},Ce=function(e){if(!we||we!==e){if(e&&"object"==typeof e||(e={}),e=Sf(e),ve=ve=-1===ye.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,xe="application/xhtml+xml"===ve?mf:uf,N="ALLOWED_TAGS"in e?wf({},e.ALLOWED_TAGS,xe):L,z="ALLOWED_ATTR"in e?wf({},e.ALLOWED_ATTR,xe):V,fe="ALLOWED_NAMESPACES"in e?wf({},e.ALLOWED_NAMESPACES,mf):be,ce="ADD_URI_SAFE_ATTR"in e?wf(Sf(de),e.ADD_URI_SAFE_ATTR,xe):de,ie="ADD_DATA_URI_TAGS"in e?wf(Sf(le),e.ADD_DATA_URI_TAGS,xe):le,re="FORBID_CONTENTS"in e?wf({},e.FORBID_CONTENTS,xe):ae,P="FORBID_TAGS"in e?wf({},e.FORBID_TAGS,xe):{},U="FORBID_ATTR"in e?wf({},e.FORBID_ATTR,xe):{},se="USE_PROFILES"in e&&e.USE_PROFILES,W=!1!==e.ALLOW_ARIA_ATTR,j=!1!==e.ALLOW_DATA_ATTR,G=e.ALLOW_UNKNOWN_PROTOCOLS||!1,$=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,q=e.SAFE_FOR_TEMPLATES||!1,Y=e.WHOLE_DOCUMENT||!1,J=e.RETURN_DOM||!1,Z=e.RETURN_DOM_FRAGMENT||!1,Q=e.RETURN_TRUSTED_TYPE||!1,K=e.FORCE_BODY||!1,ee=!1!==e.SANITIZE_DOM,te=e.SANITIZE_NAMED_PROPS||!1,oe=!1!==e.KEEP_CONTENT,ne=e.IN_PLACE||!1,R=e.ALLOWED_URI_REGEXP||Hf,pe=e.NAMESPACE||ge,H=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&ke(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(H.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ke(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(H.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(H.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),q&&(j=!1),Z&&(J=!0),se&&(N=wf({},[...Mf]),z=[],!0===se.html&&(wf(N,Cf),wf(z,Df)),!0===se.svg&&(wf(N,Of),wf(z,Bf),wf(z,Ff)),!0===se.svgFilters&&(wf(N,_f),wf(z,Bf),wf(z,Ff)),!0===se.mathMl&&(wf(N,Ef),wf(z,If),wf(z,Ff))),e.ADD_TAGS&&(N===L&&(N=Sf(N)),wf(N,e.ADD_TAGS,xe)),e.ADD_ATTR&&(z===V&&(z=Sf(z)),wf(z,e.ADD_ATTR,xe)),e.ADD_URI_SAFE_ATTR&&wf(ce,e.ADD_URI_SAFE_ATTR,xe),e.FORBID_CONTENTS&&(re===ae&&(re=Sf(re)),wf(re,e.FORBID_CONTENTS,xe)),oe&&(N["#text"]=!0),Y&&wf(N,["html","head","body"]),N.table&&(wf(N,["tbody"]),delete P.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw vf('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw vf('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');x=e.TRUSTED_TYPES_POLICY,w=x.createHTML("")}else void 0===x&&(x=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let o=null;const n="data-tt-policy-suffix";t&&t.hasAttribute(n)&&(o=t.getAttribute(n));const s="dompurify"+(o?"#"+o:"");try{return e.createPolicy(s,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+s+" could not be created."),null}}(p,s)),null!==x&&"string"==typeof w&&(w=x.createHTML(""));of&&of(e),we=e}},Oe=wf({},["mi","mo","mn","ms","mtext"]),_e=wf({},["foreignobject","desc","title","annotation-xml"]),Te=wf({},["title","style","font","a","script"]),Ee=wf({},Of);wf(Ee,_f),wf(Ee,Tf);const Ae=wf({},Ef);wf(Ae,Af);const Me=function(e){df(o.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){e.remove()}},De=function(e,t){try{df(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){df(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!z[e])if(J||Z)try{Me(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},Be=function(e){let t,o;if(K)e="<remove></remove>"+e;else{const t=gf(e,/^[\r\n\t ]+/);o=t&&t[0]}"application/xhtml+xml"===ve&&pe===ge&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const n=x?x.createHTML(e):e;if(pe===ge)try{t=(new g).parseFromString(n,ve)}catch(e){}if(!t||!t.documentElement){t=S.createDocument(pe,"template",null);try{t.documentElement.innerHTML=he?w:n}catch(e){}}const s=t.body||t.documentElement;return e&&o&&s.insertBefore(r.createTextNode(o),s.childNodes[0]||null),pe===ge?O.call(t,Y?"html":"body")[0]:Y?t.documentElement:s},Ie=function(e){return k.call(e.ownerDocument||e,e,d.SHOW_ELEMENT|d.SHOW_COMMENT|d.SHOW_TEXT,null,!1)},Fe=function(e){return"object"==typeof l?e instanceof l:e&&"object"==typeof e&&"number"==typeof e.nodeType&&"string"==typeof e.nodeName},Re=function(e,t,n){T[e]&&lf(T[e],(e=>{e.call(o,t,n,we)}))},Ne=function(e){let t;if(Re("beforeSanitizeElements",e,null),(n=e)instanceof m&&("string"!=typeof n.nodeName||"string"!=typeof n.textContent||"function"!=typeof n.removeChild||!(n.attributes instanceof u)||"function"!=typeof n.removeAttribute||"function"!=typeof n.setAttribute||"string"!=typeof n.namespaceURI||"function"!=typeof n.insertBefore||"function"!=typeof n.hasChildNodes))return Me(e),!0;var n;const s=xe(e.nodeName);if(Re("uponSanitizeElement",e,{tagName:s,allowedTags:N}),e.hasChildNodes()&&!Fe(e.firstElementChild)&&(!Fe(e.content)||!Fe(e.content.firstElementChild))&&bf(/<[/\w]/g,e.innerHTML)&&bf(/<[/\w]/g,e.textContent))return Me(e),!0;if(!N[s]||P[s]){if(!P[s]&&ze(s)){if(H.tagNameCheck instanceof RegExp&&bf(H.tagNameCheck,s))return!1;if(H.tagNameCheck instanceof Function&&H.tagNameCheck(s))return!1}if(oe&&!re[s]){const t=y(e)||e.parentNode,o=v(e)||e.childNodes;if(o&&t)for(let n=o.length-1;n>=0;--n)t.insertBefore(f(o[n],!0),b(e))}return Me(e),!0}return e instanceof c&&!function(e){let t=y(e);t&&t.tagName||(t={namespaceURI:pe,tagName:"template"});const o=uf(e.tagName),n=uf(t.tagName);return!!fe[e.namespaceURI]&&(e.namespaceURI===me?t.namespaceURI===ge?"svg"===o:t.namespaceURI===ue?"svg"===o&&("annotation-xml"===n||Oe[n]):Boolean(Ee[o]):e.namespaceURI===ue?t.namespaceURI===ge?"math"===o:t.namespaceURI===me?"math"===o&&_e[n]:Boolean(Ae[o]):e.namespaceURI===ge?!(t.namespaceURI===me&&!_e[n])&&!(t.namespaceURI===ue&&!Oe[n])&&!Ae[o]&&(Te[o]||!Ee[o]):!("application/xhtml+xml"!==ve||!fe[e.namespaceURI]))}(e)?(Me(e),!0):"noscript"!==s&&"noembed"!==s&&"noframes"!==s||!bf(/<\/no(script|embed|frames)/i,e.innerHTML)?(q&&3===e.nodeType&&(t=e.textContent,t=pf(t,E," "),t=pf(t,A," "),t=pf(t,M," "),e.textContent!==t&&(df(o.removed,{element:e.cloneNode()}),e.textContent=t)),Re("afterSanitizeElements",e,null),!1):(Me(e),!0)},Le=function(e,t,o){if(ee&&("id"===t||"name"===t)&&(o in r||o in Se))return!1;if(j&&!U[t]&&bf(D,t));else if(W&&bf(B,t));else if(!z[t]||U[t]){if(!(ze(e)&&(H.tagNameCheck instanceof RegExp&&bf(H.tagNameCheck,e)||H.tagNameCheck instanceof Function&&H.tagNameCheck(e))&&(H.attributeNameCheck instanceof RegExp&&bf(H.attributeNameCheck,t)||H.attributeNameCheck instanceof Function&&H.attributeNameCheck(t))||"is"===t&&H.allowCustomizedBuiltInElements&&(H.tagNameCheck instanceof RegExp&&bf(H.tagNameCheck,o)||H.tagNameCheck instanceof Function&&H.tagNameCheck(o))))return!1}else if(ce[t]);else if(bf(R,pf(o,F,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==hf(o,"data:")||!ie[e])if(G&&!bf(I,pf(o,F,"")));else if(o)return!1;return!0},ze=function(e){return e.indexOf("-")>0},Ve=function(e){let t,o,n,s;Re("beforeSanitizeAttributes",e,null);const{attributes:r}=e;if(!r)return;const a={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:z};for(s=r.length;s--;){t=r[s];const{name:i,namespaceURI:l}=t;o="value"===i?t.value:ff(t.value);const c=o;if(n=xe(i),a.attrName=n,a.attrValue=o,a.keepAttr=!0,a.forceKeepAttr=void 0,Re("uponSanitizeAttribute",e,a),o=a.attrValue,a.forceKeepAttr)continue;if(!a.keepAttr){De(i,e);continue}if(!$&&bf(/\/>/i,o)){De(i,e);continue}q&&(o=pf(o,E," "),o=pf(o,A," "),o=pf(o,M," "));const d=xe(e.nodeName);if(Le(d,n,o)){if(!te||"id"!==n&&"name"!==n||(De(i,e),o="user-content-"+o),x&&"object"==typeof p&&"function"==typeof p.getAttributeType)if(l);else switch(p.getAttributeType(d,n)){case"TrustedHTML":o=x.createHTML(o);break;case"TrustedScriptURL":o=x.createScriptURL(o)}if(o!==c)try{l?e.setAttributeNS(l,i,o):e.setAttribute(i,o)}catch(t){De(i,e)}}else De(i,e)}Re("afterSanitizeAttributes",e,null)},He=function e(t){let o;const n=Ie(t);for(Re("beforeSanitizeShadowDOM",t,null);o=n.nextNode();)Re("uponSanitizeShadowNode",o,null),Ne(o)||(o.content instanceof a&&e(o.content),Ve(o));Re("afterSanitizeShadowDOM",t,null)};return o.sanitize=function(e){let t,s,r,i,c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(he=!e,he&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Fe(e)){if("function"!=typeof e.toString)throw vf("toString is not a function");if("string"!=typeof(e=e.toString()))throw vf("dirty is not a string, aborting")}if(!o.isSupported)return e;if(X||Ce(c),o.removed=[],"string"==typeof e&&(ne=!1),ne){if(e.nodeName){const t=xe(e.nodeName);if(!N[t]||P[t])throw vf("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof l)t=Be("\x3c!----\x3e"),s=t.ownerDocument.importNode(e,!0),1===s.nodeType&&"BODY"===s.nodeName||"HTML"===s.nodeName?t=s:t.appendChild(s);else{if(!J&&!q&&!Y&&-1===e.indexOf("<"))return x&&Q?x.createHTML(e):e;if(t=Be(e),!t)return J?null:Q?w:""}t&&K&&Me(t.firstChild);const d=Ie(ne?e:t);for(;r=d.nextNode();)Ne(r)||(r.content instanceof a&&He(r.content),Ve(r));if(ne)return e;if(J){if(Z)for(i=C.call(t.ownerDocument);t.firstChild;)i.appendChild(t.firstChild);else i=t;return(z.shadowroot||z.shadowrootmode)&&(i=_.call(n,i,!0)),i}let u=Y?t.outerHTML:t.innerHTML;return Y&&N["!doctype"]&&t.ownerDocument&&t.ownerDocument.doctype&&t.ownerDocument.doctype.name&&bf(Wf,t.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+t.ownerDocument.doctype.name+">\n"+u),q&&(u=pf(u,E," "),u=pf(u,A," "),u=pf(u,M," ")),x&&Q?x.createHTML(u):u},o.setConfig=function(e){Ce(e),X=!0},o.clearConfig=function(){we=null,X=!1},o.isValidAttribute=function(e,t,o){we||Ce({});const n=xe(e),s=xe(t);return Le(n,s,o)},o.addHook=function(e,t){"function"==typeof t&&(T[e]=T[e]||[],df(T[e],t))},o.removeHook=function(e){if(T[e])return cf(T[e])},o.removeHooks=function(e){T[e]&&(T[e]=[])},o.removeAllHooks=function(){T={}},o}();const qf=e=>$f().sanitize(e);var Yf=tinymce.util.Tools.resolve("tinymce.util.I18n");const Xf={indent:!0,outdent:!0,"table-insert-column-after":!0,"table-insert-column-before":!0,"paste-column-after":!0,"paste-column-before":!0,"unordered-list":!0,"list-bull-circle":!0,"list-bull-default":!0,"list-bull-square":!0},Kf="temporary-placeholder",Jf=e=>()=>be(e,Kf).getOr("!not found!"),Zf=(e,t)=>{const o=e.toLowerCase();if(Yf.isRtl()){const e=((e,t)=>Ae(e,t)?e:((e,t)=>e+t)(e,t))(o,"-rtl");return ve(t,e)?e:o}return o},Qf=(e,t)=>be(t,Zf(e,t)),eb=(e,t)=>{const o=t();return Qf(e,o).getOrThunk(Jf(o))},tb=()=>oh("add-focusable",[Zr((e=>{vi(e.element,"svg").each((e=>Ot(e,"focusable","false")))}))]),ob=(e,t,o,n)=>{var s,r;const a=(e=>!!Yf.isRtl()&&ve(Xf,e))(t)?["tox-icon--flip"]:[],i=be(o,Zf(t,o)).or(n).getOrThunk(Jf(o));return{dom:{tag:e.tag,attributes:null!==(s=e.attributes)&&void 0!==s?s:{},classes:e.classes.concat(a),innerHtml:i},behaviours:El([...null!==(r=e.behaviours)&&void 0!==r?r:[],tb()])}},nb=(e,t,o,n=A.none())=>ob(t,e,o(),n),sb={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},rb=wm({name:"Notification",factory:e=>{const t=Kh({dom:Xh(`<p>${qf(e.translationProvider(e.text))}</p>`),behaviours:El([th.config({})])}),o=e=>({dom:{tag:"div",classes:["tox-bar"],styles:{width:`${e}%`}}}),n=e=>({dom:{tag:"div",classes:["tox-text"],innerHtml:`${e}%`}}),s=Kh({dom:{tag:"div",classes:e.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(0)]},n(0)],behaviours:El([th.config({})])}),r={updateProgress:(e,t)=>{e.getSystem().isConnected()&&s.getOpt(e).each((e=>{th.set(e,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(t)]},n(t)])}))},updateText:(e,o)=>{if(e.getSystem().isConnected()){const n=t.get(e);th.set(n,[ai(o)])}}},a=q([e.icon.toArray(),e.level.toArray(),e.level.bind((e=>A.from(sb[e]))).toArray()]),i=Kh(Yh.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[nb("close",{tag:"span",classes:["tox-icon"],attributes:{"aria-label":e.translationProvider("Close")}},e.iconProvider)],action:t=>{e.onAction(t)}})),l=((e,t,o)=>{const n=o(),s=G(e,(e=>ve(n,Zf(e,n))));return ob({tag:"div",classes:["tox-notification__icon"]},s.getOr(Kf),n,A.none())})(a,0,e.iconProvider),c=[l,{dom:{tag:"div",classes:["tox-notification__body"]},components:[t.asSpec()],behaviours:El([th.config({})])}];return{uid:e.uid,dom:{tag:"div",attributes:{role:"alert"},classes:e.level.map((e=>["tox-notification","tox-notification--in",`tox-notification--${e}`])).getOr(["tox-notification","tox-notification--in"])},behaviours:El([ih.config({}),oh("notification-events",[jr(Ks(),(e=>{i.getOpt(e).each(ih.focus)}))])]),components:c.concat(e.progress?[s.asSpec()]:[]).concat([i.asSpec()]),apis:r}},configFields:[gs("level"),ss("progress"),gs("icon"),ss("onAction"),ss("text"),ss("iconProvider"),ss("translationProvider")],apis:{updateProgress:(e,t,o)=>{e.updateProgress(t,o)},updateText:(e,t,o)=>{e.updateText(t,o)}}});var ab,ib,lb=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),cb=tinymce.util.Tools.resolve("tinymce.EditorManager"),db=tinymce.util.Tools.resolve("tinymce.Env");!function(e){e.default="wrap",e.floating="floating",e.sliding="sliding",e.scrolling="scrolling"}(ab||(ab={})),function(e){e.auto="auto",e.top="top",e.bottom="bottom"}(ib||(ib={}));const ub=e=>t=>t.options.get(e),mb=e=>t=>A.from(e(t)),gb=e=>{const t=db.deviceType.isPhone(),o=db.deviceType.isTablet()||t,n=e.options.register,s=e=>r(e)||!1===e,a=e=>r(e)||h(e);n("skin",{processor:e=>r(e)||!1===e,default:"oxide"}),n("skin_url",{processor:"string"}),n("height",{processor:a,default:Math.max(e.getElement().offsetHeight,400)}),n("width",{processor:a,default:lb.DOM.getStyle(e.getElement(),"width")}),n("min_height",{processor:"number",default:100}),n("min_width",{processor:"number"}),n("max_height",{processor:"number"}),n("max_width",{processor:"number"}),n("style_formats",{processor:"object[]"}),n("style_formats_merge",{processor:"boolean",default:!1}),n("style_formats_autohide",{processor:"boolean",default:!1}),n("line_height_formats",{processor:"string",default:"1 1.1 1.2 1.3 1.4 1.5 2"}),n("font_family_formats",{processor:"string",default:"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats"}),n("font_size_formats",{processor:"string",default:"8pt 10pt 12pt 14pt 18pt 24pt 36pt"}),n("font_size_input_default_unit",{processor:"string",default:"pt"}),n("block_formats",{processor:"string",default:"Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre"}),n("content_langs",{processor:"object[]"}),n("removed_menuitems",{processor:"string",default:""}),n("menubar",{processor:e=>r(e)||d(e),default:!t}),n("menu",{processor:"object",default:{}}),n("toolbar",{processor:e=>d(e)||r(e)||l(e)?{value:e,valid:!0}:{valid:!1,message:"Must be a boolean, string or array."},default:!0}),L(9,(e=>{n("toolbar"+(e+1),{processor:"string"})})),n("toolbar_mode",{processor:"string",default:o?"scrolling":"floating"}),n("toolbar_groups",{processor:"object",default:{}}),n("toolbar_location",{processor:"string",default:ib.auto}),n("toolbar_persist",{processor:"boolean",default:!1}),n("toolbar_sticky",{processor:"boolean",default:e.inline}),n("toolbar_sticky_offset",{processor:"number",default:0}),n("fixed_toolbar_container",{processor:"string",default:""}),n("fixed_toolbar_container_target",{processor:"object"}),n("ui_mode",{processor:"string",default:"combined"}),n("file_picker_callback",{processor:"function"}),n("file_picker_validator_handler",{processor:"function"}),n("file_picker_types",{processor:"string"}),n("typeahead_urls",{processor:"boolean",default:!0}),n("anchor_top",{processor:s,default:"#top"}),n("anchor_bottom",{processor:s,default:"#bottom"}),n("draggable_modal",{processor:"boolean",default:!1}),n("statusbar",{processor:"boolean",default:!0}),n("elementpath",{processor:"boolean",default:!0}),n("branding",{processor:"boolean",default:!0}),n("promotion",{processor:"boolean",default:!0}),n("resize",{processor:e=>"both"===e||d(e),default:!db.deviceType.isTouch()}),n("sidebar_show",{processor:"string"}),n("help_accessibility",{processor:"boolean",default:e.hasPlugin("help")}),n("default_font_stack",{processor:"string[]",default:[]})},pb=ub("readonly"),hb=ub("height"),fb=ub("width"),bb=mb(ub("min_width")),vb=mb(ub("min_height")),yb=mb(ub("max_width")),xb=mb(ub("max_height")),wb=mb(ub("style_formats")),Sb=ub("style_formats_merge"),kb=ub("style_formats_autohide"),Cb=ub("content_langs"),Ob=ub("removed_menuitems"),_b=ub("toolbar_mode"),Tb=ub("toolbar_groups"),Eb=ub("toolbar_location"),Ab=ub("fixed_toolbar_container"),Mb=ub("fixed_toolbar_container_target"),Db=ub("toolbar_persist"),Bb=ub("toolbar_sticky_offset"),Ib=ub("menubar"),Fb=ub("toolbar"),Rb=ub("file_picker_callback"),Nb=ub("file_picker_validator_handler"),Lb=ub("font_size_input_default_unit"),zb=ub("file_picker_types"),Vb=ub("typeahead_urls"),Hb=ub("anchor_top"),Pb=ub("anchor_bottom"),Ub=ub("draggable_modal"),Wb=ub("statusbar"),jb=ub("elementpath"),Gb=ub("branding"),$b=ub("resize"),qb=ub("paste_as_text"),Yb=ub("sidebar_show"),Xb=ub("promotion"),Kb=ub("help_accessibility"),Jb=ub("default_font_stack"),Zb=e=>!1===e.options.get("skin"),Qb=e=>!1!==e.options.get("menubar"),ev=e=>{const t=e.options.get("skin_url");if(Zb(e))return t;if(t)return e.documentBaseURI.toAbsolute(t);{const t=e.options.get("skin");return cb.baseURL+"/skins/ui/"+t}},tv=e=>A.from(e.options.get("skin_url")),ov=e=>e.options.get("line_height_formats").split(" "),nv=e=>{const t=Fb(e),o=r(t),n=l(t)&&t.length>0;return!rv(e)&&(n||o||!0===t)},sv=e=>{const t=L(9,(t=>e.options.get("toolbar"+(t+1)))),o=U(t,r);return Ce(o.length>0,o)},rv=e=>sv(e).fold((()=>{const t=Fb(e);return f(t,r)&&t.length>0}),E),av=e=>Eb(e)===ib.bottom,iv=e=>{var t;if(!e.inline)return A.none();const o=null!==(t=Ab(e))&&void 0!==t?t:"";if(o.length>0)return yi(St(),o);const n=Mb(e);return g(n)?A.some(ze(n)):A.none()},lv=e=>e.inline&&iv(e).isSome(),cv=e=>iv(e).getOrThunk((()=>vt(bt(ze(e.getElement()))))),dv=e=>e.inline&&!Qb(e)&&!nv(e)&&!rv(e),uv=e=>(e.options.get("toolbar_sticky")||e.inline)&&!lv(e)&&!dv(e),mv=e=>!lv(e)&&"split"===e.options.get("ui_mode"),gv=e=>{const t=e.options.get("menu");return ce(t,(e=>({...e,items:e.items})))};var pv=Object.freeze({__proto__:null,get ToolbarMode(){return ab},get ToolbarLocation(){return ib},register:gb,getSkinUrl:ev,getSkinUrlOption:tv,isReadOnly:pb,isSkinDisabled:Zb,getHeightOption:hb,getWidthOption:fb,getMinWidthOption:bb,getMinHeightOption:vb,getMaxWidthOption:yb,getMaxHeightOption:xb,getUserStyleFormats:wb,shouldMergeStyleFormats:Sb,shouldAutoHideStyleFormats:kb,getLineHeightFormats:ov,getContentLanguages:Cb,getRemovedMenuItems:Ob,isMenubarEnabled:Qb,isMultipleToolbars:rv,isToolbarEnabled:nv,isToolbarPersist:Db,getMultipleToolbarsOption:sv,getUiContainer:cv,useFixedContainer:lv,isSplitUiMode:mv,getToolbarMode:_b,isDraggableModal:Ub,isDistractionFree:dv,isStickyToolbar:uv,getStickyToolbarOffset:Bb,getToolbarLocation:Eb,isToolbarLocationBottom:av,getToolbarGroups:Tb,getMenus:gv,getMenubar:Ib,getToolbar:Fb,getFilePickerCallback:Rb,getFilePickerTypes:zb,useTypeaheadUrls:Vb,getAnchorTop:Hb,getAnchorBottom:Pb,getFilePickerValidatorHandler:Nb,getFontSizeInputDefaultUnit:Lb,useStatusBar:Wb,useElementPath:jb,promotionEnabled:Xb,useBranding:Gb,getResize:$b,getPasteAsText:qb,getSidebarShow:Yb,useHelpAccessibility:Kb,getDefaultFontStack:Jb});var hv;!function(e){e[e.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",e[e.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX"}(hv||(hv={}));var fv=hv;const bv="tox-menu-nav__js",vv="tox-collection__item",yv="tox-swatch",xv={normal:bv,color:yv},wv="tox-collection__item--enabled",Sv="tox-collection__item-icon",kv="tox-collection__item-label",Cv="tox-collection__item-caret",Ov="tox-collection__item--active",_v="tox-collection__item-container",Tv="tox-collection__item-container--row",Ev=e=>be(xv,e).getOr(bv),Av=e=>"color"===e?"tox-swatches":"tox-menu",Mv=e=>({backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:Av(e),tieredMenu:"tox-tiered-menu"}),Dv=e=>{const t=Mv(e);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:Ev(e)}},Bv=(e,t,o)=>{const n=Mv(o);return{tag:"div",classes:q([[n.menu,`tox-menu-${t}-column`],e?[n.hasIcons]:[]])}},Iv=[Nh.parts.items({})],Fv=(e,t,o)=>{const n=Mv(o);return{dom:{tag:"div",classes:q([[n.tieredMenu]])},markers:Dv(o)}},Rv=x([gs("data"),ws("inputAttributes",{}),ws("inputStyles",{}),ws("tag","input"),ws("inputClasses",[]),Ni("onSetValue"),ws("styles",{}),ws("eventOrder",{}),xu("inputBehaviours",[yu,ih]),ws("selectOnFocus",!0)]),Nv=e=>El([ih.config({onFocus:e.selectOnFocus?e=>{const t=e.element,o=Ja(t);t.dom.setSelectionRange(0,o.length)}:b})]),Lv=e=>({...Nv(e),...Su(e.inputBehaviours,[yu.config({store:{mode:"manual",...e.data.map((e=>({initialValue:e}))).getOr({}),getValue:e=>Ja(e.element),setValue:(e,t)=>{Ja(e.element)!==t&&Za(e.element,t)}},onSetValue:e.onSetValue})])}),zv=e=>({tag:e.tag,attributes:{type:"text",...e.inputAttributes},styles:e.inputStyles,classes:e.inputClasses}),Vv=wm({name:"Input",configFields:Rv(),factory:(e,t)=>({uid:e.uid,dom:zv(e),components:[],behaviours:Lv(e),eventOrder:e.eventOrder})}),Hv=da("refetch-trigger-event"),Pv=da("redirect-menu-item-interaction"),Uv="tox-menu__searcher",Wv=e=>yi(e.element,`.${Uv}`).bind((t=>e.getSystem().getByDom(t).toOptional())),jv=Wv,Gv=e=>({fetchPattern:yu.getValue(e),selectionStart:e.element.dom.selectionStart,selectionEnd:e.element.dom.selectionEnd}),$v=e=>{const t=(e,t)=>(t.cut(),A.none()),o=(e,t)=>{const o={interactionEvent:t.event,eventType:t.event.raw.type};return Nr(e,Pv,o),A.some(!0)},n="searcher-events";return{dom:{tag:"div",classes:[vv]},components:[Vv.sketch({inputClasses:[Uv,"tox-textfield"],inputAttributes:{...e.placeholder.map((t=>({placeholder:e.i18n(t)}))).getOr({}),type:"search","aria-autocomplete":"list"},inputBehaviours:El([oh(n,[jr(er(),(e=>{Rr(e,Hv)})),jr(Zs(),((e,t)=>{"Escape"===t.event.raw.key&&t.stop()}))]),$p.config({mode:"special",onLeft:t,onRight:t,onSpace:t,onEnter:o,onEscape:o,onUp:o,onDown:o})]),eventOrder:{keydown:[n,$p.name()]}})]}},qv="tox-collection--results__js",Yv=e=>{var t;return e.dom?{...e,dom:{...e.dom,attributes:{...null!==(t=e.dom.attributes)&&void 0!==t?t:{},id:da("aria-item-search-result-id"),"aria-selected":"false"}}}:e},Xv=(e,t)=>o=>{const n=z(o,t);return V(n,(t=>({dom:e,components:t})))},Kv=(e,t)=>{const o=[];let n=[];return H(e,((e,s)=>{t(e,s)?(n.length>0&&o.push(n),n=[],(ve(e.dom,"innerHtml")||e.components&&e.components.length>0)&&n.push(e)):n.push(e)})),n.length>0&&o.push(n),V(o,(e=>({dom:{tag:"div",classes:["tox-collection__group"]},components:e})))},Jv=(e,t,o)=>Nh.parts.items({preprocess:n=>{const s=V(n,o);return"auto"!==e&&e>1?Xv({tag:"div",classes:["tox-collection__group"]},e)(s):Kv(s,((e,o)=>"separator"===t[o].type))}}),Zv=(e,t,o=!0)=>({dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[Jv(e,t,w)]}),Qv=e=>N(e,(e=>"icon"in e&&void 0!==e.icon)),ey=e=>(console.error(Zn(e)),console.log(e),A.none()),ty=(e,t,o,n,s)=>{const r=(a=o,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[Nh.parts.items({preprocess:e=>Kv(e,((e,t)=>"separator"===a[t].type))})]});var a;return{value:e,dom:r.dom,components:r.components,items:o}},oy=(e,t,o,n,s)=>{if("color"===s.menuType){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[Nh.parts.items({preprocess:"auto"!==e?Xv({tag:"div",classes:["tox-swatches__row"]},e):w})]}]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType&&"auto"===n){const t=Zv(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType||"searchable"===s.menuType){const t="searchable"!==s.menuType?Zv(n,o):"search-with-field"===s.searchMode.searchMode?((e,t,o)=>{const n=da("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[$v({i18n:Yf.translate,placeholder:o.placeholder}),{dom:{tag:"div",classes:[...1===e?["tox-collection--list"]:["tox-collection--grid"],qv],attributes:{id:n}},components:[Jv(e,t,Yv)]}]}})(n,o,s.searchMode):((e,t,o=!0)=>{const n=da("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection",qv].concat(1===e?["tox-collection--list"]:["tox-collection--grid"]),attributes:{id:n}},components:[Jv(e,t,Yv)]}})(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("listpreview"===s.menuType&&"auto"!==n){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[Nh.parts.items({preprocess:Xv({tag:"div",classes:["tox-collection__group"]},e)})]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}return{value:e,dom:Bv(t,n,s.menuType),components:Iv,items:o}},ny=is("type"),sy=is("name"),ry=is("label"),ay=is("text"),iy=is("title"),ly=is("icon"),cy=is("value"),dy=cs("fetch"),uy=cs("getSubmenuItems"),my=cs("onAction"),gy=cs("onItemAction"),py=Ts("onSetup",(()=>b)),hy=fs("name"),fy=fs("text"),by=fs("icon"),vy=fs("tooltip"),yy=fs("label"),xy=fs("shortcut"),wy=vs("select"),Sy=_s("active",!1),ky=_s("borderless",!1),Cy=_s("enabled",!0),Oy=_s("primary",!1),_y=e=>ws("columns",e),Ty=ws("meta",{}),Ey=Ts("onAction",b),Ay=e=>Cs("type",e),My=e=>ts("name","name",xn((()=>da(`${e}-name`))),Pn),Dy=In([ny,fy]),By=In([Ay("autocompleteitem"),Sy,Cy,Ty,cy,fy,by]),Iy=[Cy,vy,by,fy,py],Fy=In([ny,my,xy].concat(Iy)),Ry=e=>Xn("toolbarbutton",Fy,e),Ny=[Sy].concat(Iy),Ly=In(Ny.concat([ny,my,xy])),zy=e=>Xn("ToggleButton",Ly,e),Vy=[Ts("predicate",T),Os("scope","node",["node","editor"]),Os("position","selection",["node","selection","line"])],Hy=Iy.concat([Ay("contextformbutton"),Oy,my,os("original",w)]),Py=Ny.concat([Ay("contextformbutton"),Oy,my,os("original",w)]),Uy=Iy.concat([Ay("contextformbutton")]),Wy=Ny.concat([Ay("contextformtogglebutton")]),jy=Qn("type",{contextformbutton:Hy,contextformtogglebutton:Py}),Gy=In([Ay("contextform"),Ts("initValue",x("")),yy,ms("commands",jy),ps("launch",Qn("type",{contextformbutton:Uy,contextformtogglebutton:Wy}))].concat(Vy)),$y=In([Ay("contexttoolbar"),is("items")].concat(Vy)),qy=[ny,is("src"),fs("alt"),Es("classes",[],Pn)],Yy=In(qy),Xy=[ny,ay,hy,Es("classes",["tox-collection__item-label"],Pn)],Ky=In(Xy),Jy=Mn((()=>$n("type",{cardimage:Yy,cardtext:Ky,cardcontainer:Zy}))),Zy=In([ny,Cs("direction","horizontal"),Cs("align","left"),Cs("valign","middle"),ms("items",Jy)]),Qy=[Cy,fy,xy,("menuitem",ts("value","value",xn((()=>da("menuitem-value"))),zn())),Ty];const ex=In([ny,yy,ms("items",Jy),py,Ey].concat(Qy)),tx=In([ny,Sy,by].concat(Qy)),ox=[ny,is("fancytype"),Ey],nx=[ws("initData",{})].concat(ox),sx=[vs("select"),As("initData",{},[_s("allowCustomColors",!0),Cs("storageKey","default"),ys("colors",zn())])].concat(ox),rx=Qn("fancytype",{inserttable:nx,colorswatch:sx}),ax=In([ny,py,Ey,by].concat(Qy)),ix=In([ny,uy,py,by].concat(Qy)),lx=In([ny,by,Sy,py,my].concat(Qy)),cx=(e,t,o)=>{const n=Qc(e.element,"."+o);if(n.length>0){const e=$(n,(e=>{const o=e.dom.getBoundingClientRect().top,s=n[0].dom.getBoundingClientRect().top;return Math.abs(o-s)>t})).getOr(n.length);return A.some({numColumns:e,numRows:Math.ceil(n.length/e)})}return A.none()},dx=e=>((e,t)=>El([oh(e,t)]))(da("unnamed-events"),e),ux=da("tooltip.exclusive"),mx=da("tooltip.show"),gx=da("tooltip.hide"),px=da("tooltip.immediateHide"),hx=da("tooltip.immediateShow"),fx=(e,t,o)=>{e.getSystem().broadcastOn([ux],{})};var bx=Object.freeze({__proto__:null,hideAllExclusive:fx,setComponents:(e,t,o,n)=>{o.getTooltip().each((e=>{e.getSystem().isConnected()&&th.set(e,n)}))}}),vx=Object.freeze({__proto__:null,events:(e,t)=>{const o=o=>{t.getTooltip().each((n=>{n.getSystem().isConnected()&&(Ld(n),e.onHide(o,n),t.clearTooltip())})),t.clearTimer()},n=o=>{if(!t.isShowing()){fx(o);const n=e.lazySink(o).getOrDie(),s=o.getSystem().build({dom:e.tooltipDom,components:e.tooltipComponents,events:Pr("normal"===e.mode?[jr(Xs(),(e=>{Rr(o,mx)})),jr(qs(),(e=>{Rr(o,gx)}))]:[]),behaviours:El([th.config({})])});t.setTooltip(s),Fd(n,s),e.onShow(o,s),Td.position(n,s,{anchor:e.anchor(o)})}},s=o=>{t.getTooltip().each((t=>{const n=e.lazySink(o).getOrDie();Td.position(n,t,{anchor:e.anchor(o)})}))};return Pr(q([[jr(mx,(o=>{t.resetTimer((()=>{n(o)}),e.delayForShow())})),jr(gx,(n=>{t.resetTimer((()=>{o(n)}),e.delayForHide())})),jr(hx,(e=>{t.resetTimer((()=>{n(e)}),0)})),jr(px,(e=>{t.resetTimer((()=>{o(e)}),0)})),jr(mr(),((e,t)=>{const n=t;n.universal||R(n.channels,ux)&&o(e)})),Qr((e=>{o(e)}))],(()=>{switch(e.mode){case"normal":return[jr(Ks(),(e=>{Rr(e,hx)})),jr(dr(),(e=>{Rr(e,px)})),jr(Xs(),(e=>{Rr(e,mx)})),jr(qs(),(e=>{Rr(e,gx)}))];case"follow-highlight":return[jr(Ir(),((e,t)=>{Rr(e,mx)})),jr(Fr(),(e=>{Rr(e,gx)}))];case"children-normal":return[jr(Ks(),((o,n)=>{Hl(o.element).each((r=>{Je(n.event.target,"[data-mce-tooltip]")&&t.getTooltip().fold((()=>{Rr(o,hx)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),jr(dr(),(e=>{Hl(e.element).fold((()=>{Rr(e,px)}),b)})),jr(Xs(),(o=>{yi(o.element,"[data-mce-tooltip]:hover").each((n=>{t.getTooltip().fold((()=>{Rr(o,mx)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),jr(qs(),(e=>{yi(e.element,"[data-mce-tooltip]:hover").fold((()=>{Rr(e,gx)}),b)}))];default:return[jr(Ks(),((o,n)=>{Hl(o.element).each((r=>{Je(n.event.target,"[data-mce-tooltip]")&&t.getTooltip().fold((()=>{Rr(o,hx)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),jr(dr(),(e=>{Hl(e.element).fold((()=>{Rr(e,px)}),b)}))]}})()]))}}),yx=[ss("lazySink"),ss("tooltipDom"),ws("exclusive",!0),ws("tooltipComponents",[]),Ts("delayForShow",x(300)),Ts("delayForHide",x(300)),Os("mode","normal",["normal","follow-highlight","children-keyboard-focus","children-normal"]),ws("anchor",(e=>({type:"hotspot",hotspot:e,layouts:{onLtr:x([pl,gl,cl,ul,dl,ml]),onRtl:x([pl,gl,cl,ul,dl,ml])},bubble:vc(0,-2,{})}))),Ni("onHide"),Ni("onShow")],xx=Object.freeze({__proto__:null,init:()=>{const e=sc(),t=sc(),o=()=>{e.on(clearTimeout)},n=x("not-implemented");return Ea({getTooltip:t.get,isShowing:t.isSet,setTooltip:t.set,clearTooltip:t.clear,clearTimer:o,resetTimer:(t,n)=>{o(),e.set(setTimeout(t,n))},readState:n})}});const wx=Ml({fields:yx,name:"tooltipping",active:vx,state:xx,apis:bx}),Sx="silver.readonly",kx=In([("readonly",rs("readonly",Un))]);const Cx=(e,t)=>{const o=e.mainUi.outerContainer.element,n=[e.mainUi.mothership,...e.uiMotherships];t&&H(n,(e=>{e.broadcastOn([eu()],{target:o})})),H(n,(e=>{e.broadcastOn([Sx],{readonly:t})}))},Ox=(e,t)=>{e.on("init",(()=>{e.mode.isReadOnly()&&Cx(t,!0)})),e.on("SwitchMode",(()=>Cx(t,e.mode.isReadOnly()))),pb(e)&&e.mode.set("readonly")},_x=()=>Fl.config({channels:{[Sx]:{schema:kx,onReceive:(e,t)=>{Hm.set(e,t.readonly)}}}}),Tx=e=>Hm.config({disabled:e}),Ex=e=>Hm.config({disabled:e,disableClass:"tox-tbtn--disabled"}),Ax=e=>Hm.config({disabled:e,disableClass:"tox-tbtn--disabled",useNative:!1}),Mx=(e,t)=>{const o=e.getApi(t);return e=>{e(o)}},Dx=(e,t)=>Zr((o=>{Mx(e,o)((o=>{const n=e.onSetup(o);p(n)&&t.set(n)}))})),Bx=(e,t)=>Qr((o=>Mx(e,o)(t.get()))),Ix=(e,t)=>ta(((o,n)=>{Mx(e,o)(e.onAction),e.triggersSubmenu||t!==fv.CLOSE_ON_EXECUTE||(o.getSystem().isConnected()&&Rr(o,br()),n.stop())})),Fx={[gr()]:["disabling","alloy.base.behaviour","toggling","item-events"]},Rx=we,Nx=(e,t,o,n)=>{const s=Ms(b);return{type:"item",dom:t.dom,components:Rx(t.optComponents),data:e.data,eventOrder:Fx,hasSubmenu:e.triggersSubmenu,itemBehaviours:El([oh("item-events",[Ix(e,o),Dx(e,s),Bx(e,s)]),(r=()=>!e.enabled||n.isDisabled(),Hm.config({disabled:r,disableClass:"tox-collection__item--state-disabled"})),_x(),th.config({})].concat(e.itemBehaviours))};var r},Lx=e=>({value:e.value,meta:{text:e.text.getOr(""),...e.meta}}),zx=e=>{const t=db.os.isMacOS()||db.os.isiOS(),o=t?{alt:"\u2325",ctrl:"\u2303",shift:"\u21e7",meta:"\u2318",access:"\u2303\u2325"}:{meta:"Ctrl",access:"Shift+Alt"},n=e.split("+"),s=V(n,(e=>{const t=e.toLowerCase().trim();return ve(o,t)?o[t]:e}));return t?s.join(""):s.join("+")},Vx=(e,t,o=[Sv])=>nb(e,{tag:"div",classes:o},t),Hx=e=>({dom:{tag:"div",classes:[kv]},components:[ai(Yf.translate(e))]}),Px=(e,t)=>({dom:{tag:"div",classes:t,innerHtml:e}}),Ux=(e,t)=>({dom:{tag:"div",classes:[kv]},components:[{dom:{tag:e.tag,styles:e.styles},components:[ai(Yf.translate(t))]}]}),Wx=e=>({dom:{tag:"div",classes:["tox-collection__item-accessory"]},components:[ai(zx(e))]}),jx=e=>Vx("checkmark",e,["tox-collection__item-checkmark"]),Gx=e=>{const t=e.map((e=>({attributes:{id:da("menu-item"),"aria-label":Yf.translate(e)}}))).getOr({});return{tag:"div",classes:[bv,vv],...t}},$x=(e,t,o,n=A.none())=>"color"===e.presets?((e,t,o)=>{const n=e.value,s=e.iconContent.map((e=>((e,t,o)=>{const n=t();return Qf(e,n).or(o).getOrThunk(Jf(n))})(e,t.icons,o))),r=e.ariaLabel.map((e=>({"aria-label":t.translate(e),"data-mce-name":e}))).getOr({});return{dom:(()=>{const e=yv,t=s.getOr(""),o={tag:"div",attributes:r,classes:[e]};return"custom"===n?{...o,tag:"button",classes:[...o.classes,"tox-swatches__picker-btn"],innerHtml:t}:"remove"===n?{...o,classes:[...o.classes,"tox-swatch--remove"],innerHtml:t}:g(n)?{...o,attributes:{...o.attributes,"data-mce-color":n},styles:{"background-color":n},innerHtml:t}:o})(),optComponents:[]}})(e,t,n):((e,t,o,n)=>{const s={tag:"div",classes:[Sv]},r=o?e.iconContent.map((e=>nb(e,s,t.icons,n))).orThunk((()=>A.some({dom:s}))):A.none(),a=e.checkMark,i=A.from(e.meta).fold((()=>Hx),(e=>ve(e,"style")?k(Ux,e.style):Hx)),l=e.htmlContent.fold((()=>e.textContent.map(i)),(e=>A.some(Px(e,[kv]))));return{dom:Gx(e.ariaLabel),optComponents:[r,l,e.shortcutContent.map(Wx),a,e.caret]}})(e,t,o,n),qx=(e,t,o)=>be(e,"tooltipWorker").map((e=>[wx.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:e=>({type:"submenu",item:e,overrides:{maxHeightFunction:pc}}),mode:"follow-highlight",onShow:(t,o)=>{e((e=>{wx.setComponents(t,[ii({element:ze(e)})])}))}})])).getOrThunk((()=>o.map((e=>[wx.config({...t.providers.tooltips.getConfig({tooltipText:e}),mode:"follow-highlight"})])).getOr([]))),Yx=(e,t)=>{const o=(e=>lb.DOM.encode(e))(Yf.translate(e));if(t.length>0){const e=new RegExp((e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))(t),"gi");return o.replace(e,(e=>`<span class="tox-autocompleter-highlight">${e}</span>`))}return o},Xx=(e,t)=>V(e,(e=>{switch(e.type){case"cardcontainer":return((e,t)=>{const o="vertical"===e.direction?"tox-collection__item-container--column":Tv,n="left"===e.align?"tox-collection__item-container--align-left":"tox-collection__item-container--align-right";return{dom:{tag:"div",classes:[_v,o,n,(()=>{switch(e.valign){case"top":return"tox-collection__item-container--valign-top";case"middle":return"tox-collection__item-container--valign-middle";case"bottom":return"tox-collection__item-container--valign-bottom"}})()]},components:t}})(e,Xx(e.items,t));case"cardimage":return((e,t,o)=>({dom:{tag:"img",classes:t,attributes:{src:e,alt:o.getOr("")}}}))(e.src,e.classes,e.alt);case"cardtext":const o=e.name.exists((e=>R(t.cardText.highlightOn,e))),n=o?A.from(t.cardText.matchText).getOr(""):"";return Px(Yx(e.text,n),e.classes)}})),Kx=em(Ah(),Mh()),Jx=e=>({value:tw(e)}),Zx=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,Qx=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,ew=e=>Zx.test(e)||Qx.test(e),tw=e=>_e(e,"#").toUpperCase(),ow=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},nw=e=>{const t=ow(e.red)+ow(e.green)+ow(e.blue);return Jx(t)},sw=Math.min,rw=Math.max,aw=Math.round,iw=/^\s*rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)\s*$/i,lw=/^\s*rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d?(?:\.\d+)?)\s*\)\s*$/i,cw=(e,t,o,n)=>({red:e,green:t,blue:o,alpha:n}),dw=e=>{const t=parseInt(e,10);return t.toString()===e&&t>=0&&t<=255},uw=e=>{let t,o,n;const s=(e.hue||0)%360;let r=e.saturation/100,a=e.value/100;if(r=rw(0,sw(r,1)),a=rw(0,sw(a,1)),0===r)return t=o=n=aw(255*a),cw(t,o,n,1);const i=s/60,l=a*r,c=l*(1-Math.abs(i%2-1)),d=a-l;switch(Math.floor(i)){case 0:t=l,o=c,n=0;break;case 1:t=c,o=l,n=0;break;case 2:t=0,o=l,n=c;break;case 3:t=0,o=c,n=l;break;case 4:t=c,o=0,n=l;break;case 5:t=l,o=0,n=c;break;default:t=o=n=0}return t=aw(255*(t+d)),o=aw(255*(o+d)),n=aw(255*(n+d)),cw(t,o,n,1)},mw=e=>{const t=(e=>{const t=(e=>{const t=e.value.replace(Zx,((e,t,o,n)=>t+t+o+o+n+n));return{value:t}})(e),o=Qx.exec(t.value);return null===o?["FFFFFF","FF","FF","FF"]:o})(e),o=parseInt(t[1],16),n=parseInt(t[2],16),s=parseInt(t[3],16);return cw(o,n,s,1)},gw=(e,t,o,n)=>{const s=parseInt(e,10),r=parseInt(t,10),a=parseInt(o,10),i=parseFloat(n);return cw(s,r,a,i)},pw=e=>{if("transparent"===e)return A.some(cw(0,0,0,0));const t=iw.exec(e);if(null!==t)return A.some(gw(t[1],t[2],t[3],"1"));const o=lw.exec(e);return null!==o?A.some(gw(o[1],o[2],o[3],o[4])):A.none()},hw=e=>`rgba(${e.red},${e.green},${e.blue},${e.alpha})`,fw=cw(255,0,0,1),bw=(e,t)=>{e.dispatch("ResizeContent",t)},vw=(e,t)=>{e.dispatch("TextColorChange",t)},yw=(e,t)=>e.dispatch("ResolveName",{name:t.nodeName.toLowerCase(),target:t}),xw=(e,t)=>()=>{e(),t()},ww=e=>kw(e,"NodeChange",(t=>{t.setEnabled(e.selection.isEditable())})),Sw=(e,t)=>o=>{const n=ww(e)(o),s=((e,t)=>o=>{const n=nc(),s=()=>{o.setActive(e.formatter.match(t));const s=e.formatter.formatChanged(t,o.setActive);n.set(s)};return e.initialized?s():e.once("init",s),()=>{e.off("init",s),n.clear()}})(e,t)(o);return()=>{n(),s()}},kw=(e,t,o)=>n=>{const s=()=>o(n),r=()=>{o(n),e.on(t,s)};return e.initialized?r():e.once("init",r),()=>{e.off("init",r),e.off(t,s)}},Cw=e=>t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("mceToggleFormat",!1,t.format)}))},Ow=(e,t)=>()=>e.execCommand(t);var _w=tinymce.util.Tools.resolve("tinymce.util.LocalStorage");const Tw={},Ew=e=>be(Tw,e).getOrThunk((()=>{const t=`tinymce-custom-colors-${e}`,o=_w.getItem(t);if(m(o)){const e=_w.getItem("tinymce-custom-colors");_w.setItem(t,g(e)?e:"[]")}const n=((e,t=10)=>{const o=_w.getItem(e),n=r(o)?JSON.parse(o):[],s=t-(a=n).length<0?a.slice(0,t):a;var a;const i=e=>{s.splice(e,1)};return{add:o=>{F(s,o).each(i),s.unshift(o),s.length>t&&s.pop(),_w.setItem(e,JSON.stringify(s))},state:()=>s.slice(0)}})(t,10);return Tw[e]=n,n})),Aw=(e,t)=>{Ew(e).add(t)},Mw=(e,t,o)=>({hue:e,saturation:t,value:o}),Dw=e=>{let t=0,o=0,n=0;const s=e.red/255,r=e.green/255,a=e.blue/255,i=Math.min(s,Math.min(r,a)),l=Math.max(s,Math.max(r,a));return i===l?(n=i,Mw(0,0,100*n)):(t=s===i?3:a===i?1:5,t=60*(t-(s===i?r-a:a===i?s-r:a-s)/(l-i)),o=(l-i)/l,n=l,Mw(Math.round(t),Math.round(100*o),Math.round(100*n)))},Bw=e=>nw(uw(e)),Iw=e=>{return(t=e,ew(t)?A.some({value:tw(t)}):A.none()).orThunk((()=>pw(e).map(nw))).getOrThunk((()=>{const t=document.createElement("canvas");t.height=1,t.width=1;const o=t.getContext("2d");o.clearRect(0,0,t.width,t.height),o.fillStyle="#FFFFFF",o.fillStyle=e,o.fillRect(0,0,1,1);const n=o.getImageData(0,0,1,1).data,s=n[0],r=n[1],a=n[2],i=n[3];return nw(cw(s,r,a,i))}));var t},Fw="forecolor",Rw="hilitecolor",Nw=e=>{const t=[];for(let o=0;o<e.length;o+=2)t.push({text:e[o+1],value:"#"+Iw(e[o]).value,icon:"checkmark",type:"choiceitem"});return t},Lw=e=>t=>t.options.get(e),zw="#000000",Vw=(e,t)=>t===Fw&&e.options.isSet("color_map_foreground")?Lw("color_map_foreground")(e):t===Rw&&e.options.isSet("color_map_background")?Lw("color_map_background")(e):Lw("color_map")(e),Hw=(e,t="default")=>Math.max(5,Math.ceil(Math.sqrt(Vw(e,t).length))),Pw=(e,t)=>{const o=Lw("color_cols")(e),n=Hw(e,t);return o===Hw(e)?n:o},Uw=(e,t="default")=>Math.round(t===Fw?Lw("color_cols_foreground")(e):t===Rw?Lw("color_cols_background")(e):Lw("color_cols")(e)),Ww=Lw("custom_colors"),jw=Lw("color_default_foreground"),Gw=Lw("color_default_background"),$w=(e,t)=>{const o=ze(e.selection.getStart()),n="hilitecolor"===t?Ns(o,(e=>{if($e(e)){const t=Nt(e,"background-color");return Ce(pw(t).exists((e=>0!==e.alpha)),t)}return A.none()})).getOr("rgba(0, 0, 0, 0)"):Nt(o,"color");return pw(n).map((e=>"#"+nw(e).value))},qw=e=>{const t="choiceitem",o={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return e?[o,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[o]},Yw=(e,t,o,n)=>{"custom"===o?nS(e)((o=>{o.each((o=>{Aw(t,o),e.execCommand("mceApplyTextcolor",t,o),n(o)}))}),$w(e,t).getOr(zw)):"remove"===o?(n(""),e.execCommand("mceRemoveTextcolor",t)):(n(o),e.execCommand("mceApplyTextcolor",t,o))},Xw=(e,t,o)=>e.concat((e=>V(Ew(e).state(),(e=>({type:"choiceitem",text:e,icon:"checkmark",value:e}))))(t).concat(qw(o))),Kw=(e,t,o)=>n=>{n(Xw(e,t,o))},Jw=(e,t,o)=>{const n="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color";e.setIconFill(n,o)},Zw=(e,t)=>{e.setTooltip(t)},Qw=(e,t)=>o=>{const n=$w(e,t);return xe(n,o.toUpperCase())},eS=(e,t,o)=>{if(Be(o))return"forecolor"===t?"Text color":"Background color";const n="forecolor"===t?"Text color {0}":"Background color {0}",s=Xw(Vw(e,t),t,!1),r=G(s,(e=>e.value===o)).getOr({text:""}).text;return e.translate([n,e.translate(r)])},tS=(e,t,o,n)=>{e.ui.registry.addSplitButton(t,{tooltip:eS(e,o,n.get()),presets:"color",icon:"forecolor"===t?"text-color":"highlight-bg-color",select:Qw(e,o),columns:Uw(e,o),fetch:Kw(Vw(e,o),o,Ww(e)),onAction:t=>{Yw(e,o,n.get(),b)},onItemAction:(s,r)=>{Yw(e,o,r,(o=>{n.set(o),vw(e,{name:t,color:o})}))},onSetup:s=>{Jw(s,t,n.get());const r=n=>{n.name===t&&(Jw(s,n.name,n.color),Zw(s,eS(e,o,n.color)))};return e.on("TextColorChange",r),xw(ww(e)(s),(()=>{e.off("TextColorChange",r)}))}})},oS=(e,t,o,n,s)=>{e.ui.registry.addNestedMenuItem(t,{text:n,icon:"forecolor"===t?"text-color":"highlight-bg-color",onSetup:n=>(Zw(n,eS(e,o,s.get())),Jw(n,t,s.get()),ww(e)(n)),getSubmenuItems:()=>[{type:"fancymenuitem",fancytype:"colorswatch",select:Qw(e,o),initData:{storageKey:o},onAction:n=>{Yw(e,o,n.value,(o=>{s.set(o),vw(e,{name:t,color:o})}))}}]})},nS=e=>(t,o)=>{let n=!1;const s={colorpicker:o};e.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s,onAction:(e,t)=>{"hex-valid"===t.name&&(n=t.value)},onSubmit:o=>{const s=o.getData().colorpicker;n?(t(A.from(s)),o.close()):e.windowManager.alert(e.translate(["Invalid hex color code: {0}",s]))},onClose:b,onCancel:()=>{t(A.none())}})},sS=(e,t,o,n,s,r,a,i)=>{const l=Qv(t),c=rS(t,o,n,"color"!==s?"normal":"color",r,a,i);return oy(e,l,c,n,{menuType:s})},rS=(e,t,o,n,s,r,a)=>we(V(e,(i=>{return"choiceitem"===i.type?(l=i,Xn("choicemenuitem",tx,l)).fold(ey,(i=>A.some(((e,t,o,n,s,r,a,i=!0)=>{const l=$x({presets:o,textContent:t?e.text:A.none(),htmlContent:A.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:t?e.shortcut:A.none(),checkMark:t?A.some(jx(a.icons)):A.none(),caret:A.none(),value:e.value},a,i),c=e.text.filter(x(!t)).map((e=>wx.config(a.tooltips.getConfig({tooltipText:a.translate(e)}))));return vn(Nx({data:Lx(e),enabled:e.enabled,getApi:e=>({setActive:t=>{hh.set(e,t)},isActive:()=>hh.isOn(e),isEnabled:()=>!Hm.isDisabled(e),setEnabled:t=>Hm.set(e,!t)}),onAction:t=>n(e.value),onSetup:e=>(e.setActive(s),b),triggersSubmenu:!1,itemBehaviours:[...c.toArray()]},l,r,a),{toggling:{toggleClass:wv,toggleOnExecute:!1,selected:e.active,exclusive:!0}})})(i,1===o,n,t,r(i.value),s,a,Qv(e))))):A.none();var l}))),aS=(e,t)=>{const o=Dv(t);return 1===e?{mode:"menu",moveOnTab:!0}:"auto"===e?{mode:"grid",selector:"."+o.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===t?"tox-swatches__row":"tox-collection__group"),previousSelector:e=>"color"===t?yi(e.element,"[aria-checked=true]"):A.none()}},iS=da("cell-over"),lS=da("cell-execute"),cS=(e,t,o)=>{const n=o=>Nr(o,lS,{row:e,col:t}),s=(e,t)=>{t.stop(),n(e)};return di({dom:{tag:"div",attributes:{role:"button","aria-label":o}},behaviours:El([oh("insert-table-picker-cell",[jr(Xs(),ih.focus),jr(gr(),n),jr(or(),s),jr(hr(),s)]),hh.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),ih.config({onFocus:o=>Nr(o,iS,{row:e,col:t})})])})},dS=e=>Y(e,(e=>V(e,ui))),uS=(e,t)=>ai(`${t}x${e}`),mS={inserttable:(e,t)=>{const o=(e=>(t,o)=>e.shared.providers.translate(["{0} columns, {1} rows",o,t]))(t),n=((e,t,o)=>{const n=[];for(let t=0;t<10;t++){const o=[];for(let n=0;n<10;n++){const s=e(t+1,n+1);o.push(cS(t,n,s))}n.push(o)}return n})(o),s=uS(0,0),r=Kh({dom:{tag:"span",classes:["tox-insert-table-picker__label"]},components:[s],behaviours:El([th.config({})])});return{type:"widget",data:{value:da("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Kx.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:dS(n).concat(r.asSpec()),behaviours:El([oh("insert-table-picker",[Zr((e=>{th.set(r.get(e),[s])})),Yr(iS,((e,t,o)=>{const{row:s,col:a}=o.event;((e,t,o,n,s)=>{for(let n=0;n<10;n++)for(let s=0;s<10;s++)hh.set(e[n][s],n<=t&&s<=o)})(n,s,a),th.set(r.get(e),[uS(s+1,a+1)])})),Yr(lS,((t,o,n)=>{const{row:s,col:r}=n.event;e.onAction({numRows:s+1,numColumns:r+1}),Rr(t,br())}))]),$p.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:(e,t)=>{const o=((e,t)=>{const o=e.initData.allowCustomColors&&t.colorinput.hasCustomColors();return e.initData.colors.fold((()=>Xw(t.colorinput.getColors(e.initData.storageKey),e.initData.storageKey,o)),(e=>e.concat(qw(o))))})(e,t),n=t.colorinput.getColorCols(e.initData.storageKey),s="color",r={...sS(da("menu-value"),o,(t=>{e.onAction({value:t})}),n,s,fv.CLOSE_ON_EXECUTE,e.select.getOr(T),t.shared.providers),markers:Dv(s),movement:aS(n,s)};return{type:"widget",data:{value:da("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Kx.widget(Nh.sketch(r))]}}},gS=e=>({type:"separator",dom:{tag:"div",classes:[vv,"tox-collection__group-heading"]},components:e.text.map(ai).toArray()});var pS=Object.freeze({__proto__:null,getCoupled:(e,t,o,n)=>o.getOrCreate(e,t,n),getExistingCoupled:(e,t,o,n)=>o.getExisting(e,t,n)}),hS=[rs("others",Yn(an.value,zn()))],fS=Object.freeze({__proto__:null,init:()=>{const e={},t=(t,o)=>{if(0===ae(t.others).length)throw new Error("Cannot find any known coupled components");return be(e,o)},o=x({});return Ea({readState:o,getExisting:(e,o,n)=>t(o,n).orThunk((()=>(be(o.others,n).getOrDie("No information found for coupled component: "+n),A.none()))),getOrCreate:(o,n,s)=>t(n,s).getOrThunk((()=>{const t=be(n.others,s).getOrDie("No information found for coupled component: "+s)(o),r=o.getSystem().build(t);return e[s]=r,r}))})}});const bS=Ml({fields:hS,name:"coupling",apis:pS,state:fS}),vS=e=>{let t=A.none(),o=[];const n=e=>{s()?r(e):o.push(e)},s=()=>t.isSome(),r=e=>{t.each((t=>{setTimeout((()=>{e(t)}),0)}))};return e((e=>{s()||(t=A.some(e),H(o,r),o=[])})),{get:n,map:e=>vS((t=>{n((o=>{t(e(o))}))})),isReady:s}},yS={nu:vS,pure:e=>vS((t=>{t(e)}))},xS=e=>{setTimeout((()=>{throw e}),0)},wS=e=>{const t=t=>{e().then(t,xS)};return{map:t=>wS((()=>e().then(t))),bind:t=>wS((()=>e().then((e=>t(e).toPromise())))),anonBind:t=>wS((()=>e().then((()=>t.toPromise())))),toLazy:()=>yS.nu(t),toCached:()=>{let t=null;return wS((()=>(null===t&&(t=e()),t)))},toPromise:e,get:t}},SS=e=>wS((()=>new Promise(e))),kS=e=>wS((()=>Promise.resolve(e))),CS=x("sink"),OS=x(Xu({name:CS(),overrides:x({dom:{tag:"div"},behaviours:El([Td.config({useFixed:E})]),events:Pr([Xr(Zs()),Xr(Gs()),Xr(or())])})})),_S=(e,t)=>{const o=e.getHotspot(t).getOr(t),n="hotspot",s=e.getAnchorOverrides();return e.layouts.fold((()=>({type:n,hotspot:o,overrides:s})),(e=>({type:n,hotspot:o,overrides:s,layouts:e})))},TS=(e,t,o,n,s,r,a)=>{const i=((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>(0,e.fetch)(o).map(t))(e,t,n),l=MS(n,e);return i.map((e=>e.bind((e=>A.from(Gh.sketch({...r.menu(),uid:ba(""),data:e,highlightOnOpen:a,onOpenMenu:(e,t)=>{const n=l().getOrDie();Td.position(n,t,{anchor:o}),Qd.decloak(s)},onOpenSubmenu:(e,t,o)=>{const n=l().getOrDie();Td.position(n,o,{anchor:{type:"submenu",item:t}}),Qd.decloak(s)},onRepositionMenu:(e,t,n)=>{const s=l().getOrDie();Td.position(s,t,{anchor:o}),H(n,(e=>{Td.position(s,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem}})}))},onEscape:()=>(ih.focus(n),Qd.close(s),A.some(!0))}))))))})(e,t,_S(e,o),o,n,s,a);return i.map((e=>(e.fold((()=>{Qd.isOpen(n)&&Qd.close(n)}),(e=>{Qd.cloak(n),Qd.open(n,e),r(n)})),n)))},ES=(e,t,o,n,s,r,a)=>(Qd.close(n),kS(n)),AS=(e,t,o,n,s,r)=>{const a=bS.getCoupled(o,"sandbox");return(Qd.isOpen(a)?ES:TS)(e,t,o,a,n,s,r)},MS=(e,t)=>e.getSystem().getByUid(t.uid+"-"+CS()).map((e=>()=>an.value(e))).getOrThunk((()=>t.lazySink.fold((()=>()=>an.error(new Error("No internal sink is specified, nor could an external sink be found"))),(t=>()=>t(e))))),DS=e=>{Qd.getState(e).each((e=>{Gh.repositionMenus(e)}))},BS=(e,t,o)=>{const n=Si(),s=MS(t,e);return{dom:{tag:"div",classes:e.sandboxClasses,attributes:{id:n.id,role:"listbox"}},behaviours:Cu(e.sandboxBehaviours,[yu.config({store:{mode:"memory",initialValue:t}}),Qd.config({onOpen:(s,r)=>{const a=_S(e,t);n.link(t.element),e.matchWidth&&((e,t,o)=>{const n=_m.getCurrent(t).getOr(t),s=Qt(e.element);o?It(n.element,"min-width",s+"px"):((e,t)=>{Zt.set(e,t)})(n.element,s)})(a.hotspot,r,e.useMinWidth),e.onOpen(a,s,r),void 0!==o&&void 0!==o.onOpen&&o.onOpen(s,r)},onClose:(e,r)=>{n.unlink(t.element),s().getOr(r).element.dom.dispatchEvent(new window.FocusEvent("focusout")),void 0!==o&&void 0!==o.onClose&&o.onClose(e,r)},isPartOf:(e,o,n)=>ki(o,n)||ki(t,n),getAttachPoint:()=>s().getOrDie()}),_m.config({find:e=>Qd.getState(e).bind((e=>_m.getCurrent(e)))}),Fl.config({channels:{...su({isExtraPart:T}),...au({doReposition:DS})}})])}},IS=e=>{const t=bS.getCoupled(e,"sandbox");DS(t)},FS=()=>[ws("sandboxClasses",[]),ku("sandboxBehaviours",[_m,Fl,Qd,yu])],RS=x([ss("dom"),ss("fetch"),Ni("onOpen"),Li("onExecute"),ws("getHotspot",A.some),ws("getAnchorOverrides",x({})),_c(),xu("dropdownBehaviours",[hh,bS,$p,ih]),ss("toggleClass"),ws("eventOrder",{}),gs("lazySink"),ws("matchWidth",!1),ws("useMinWidth",!1),gs("role")].concat(FS())),NS=x([Yu({schema:[Ii(),ws("fakeFocus",!1)],name:"menu",defaults:e=>({onExecute:e.onExecute})}),OS()]),LS=Sm({name:"Dropdown",configFields:RS(),partFields:NS(),factory:(e,t,o,n)=>{const s=e=>{Qd.getState(e).each((e=>{Gh.highlightPrimary(e)}))},r=(t,o,s)=>AS(e,w,t,n,o,s),a={expand:e=>{hh.isOn(e)||r(e,b,Wh.HighlightNone).get(b)},open:e=>{hh.isOn(e)||r(e,b,Wh.HighlightMenuAndItem).get(b)},refetch:t=>bS.getExistingCoupled(t,"sandbox").fold((()=>r(t,b,Wh.HighlightMenuAndItem).map(b)),(o=>TS(e,w,t,o,n,b,Wh.HighlightMenuAndItem).map(b))),isOpen:hh.isOn,close:e=>{hh.isOn(e)&&r(e,b,Wh.HighlightMenuAndItem).get(b)},repositionMenus:e=>{hh.isOn(e)&&IS(e)}},i=(e,t)=>(Lr(e),A.some(!0));return{uid:e.uid,dom:e.dom,components:t,behaviours:Su(e.dropdownBehaviours,[hh.config({toggleClass:e.toggleClass,aria:{mode:"expanded"}}),bS.config({others:{sandbox:t=>BS(e,t,{onOpen:()=>hh.on(t),onClose:()=>hh.off(t)})}}),$p.config({mode:"special",onSpace:i,onEnter:i,onDown:(e,t)=>{if(LS.isOpen(e)){const t=bS.getCoupled(e,"sandbox");s(t)}else LS.open(e);return A.some(!0)},onEscape:(e,t)=>LS.isOpen(e)?(LS.close(e),A.some(!0)):A.none()}),ih.config({})]),events:bh(A.some((e=>{r(e,s,Wh.HighlightMenuAndItem).get(b)}))),eventOrder:{...e.eventOrder,[gr()]:["disabling","toggling","alloy.base.behaviour"]},apis:a,domModification:{attributes:{"aria-haspopup":"true",...e.role.fold((()=>({})),(e=>({role:e}))),..."button"===e.dom.tag?{type:("type",be(e.dom,"attributes").bind((e=>be(e,"type")))).getOr("button")}:{}}}}},apis:{open:(e,t)=>e.open(t),refetch:(e,t)=>e.refetch(t),expand:(e,t)=>e.expand(t),close:(e,t)=>e.close(t),isOpen:(e,t)=>e.isOpen(t),repositionMenus:(e,t)=>e.repositionMenus(t)}}),zS=(e,t,o)=>{jv(e).each((e=>{var n;((e,t)=>{Et(t.element,"id").each((t=>Ot(e.element,"aria-activedescendant",t)))})(e,o),(qa((n=t).element,qv)?A.some(n.element):yi(n.element,"."+qv)).each((t=>{Et(t,"id").each((t=>Ot(e.element,"aria-controls",t)))}))})),Ot(o.element,"aria-selected","true")},VS=(e,t,o)=>{Ot(o.element,"aria-selected","false")},HS=e=>bS.getExistingCoupled(e,"sandbox").bind(Wv).map(Gv).map((e=>e.fetchPattern)).getOr("");var PS;!function(e){e[e.ContentFocus=0]="ContentFocus",e[e.UiFocus=1]="UiFocus"}(PS||(PS={}));const US=(e,t,o,n,s)=>{const r=o.shared.providers,a=e=>s?{...e,shortcut:A.none(),icon:e.text.isSome()?A.none():e.icon}:e;switch(e.type){case"menuitem":return(i=e,Xn("menuitem",ax,i)).fold(ey,(e=>A.some(((e,t,o,n=!0)=>{const s=$x({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,caret:A.none(),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return Nx({data:Lx(e),getApi:e=>({isEnabled:()=>!Hm.isDisabled(e),setEnabled:t=>Hm.set(e,!t)}),enabled:e.enabled,onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o)})(a(e),t,r,n))));case"nestedmenuitem":return(e=>Xn("nestedmenuitem",ix,e))(e).fold(ey,(e=>A.some(((e,t,o,n=!0,s=!1)=>{const r=s?(a=o.icons,Vx("chevron-down",a,[Cv])):(e=>Vx("chevron-right",e,[Cv]))(o.icons);var a;const i=$x({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,caret:A.some(r),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return Nx({data:Lx(e),getApi:e=>({isEnabled:()=>!Hm.isDisabled(e),setEnabled:t=>Hm.set(e,!t),setIconFill:(t,o)=>{yi(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{Ot(e,"fill",o)}))},setTooltip:t=>{const n=o.translate(t);Ot(e.element,"aria-label",n)}}),enabled:e.enabled,onAction:b,onSetup:e.onSetup,triggersSubmenu:!0,itemBehaviours:[]},i,t,o)})(a(e),t,r,n,s))));case"togglemenuitem":return(e=>Xn("togglemenuitem",lx,e))(e).fold(ey,(e=>A.some(((e,t,o,n=!0)=>{const s=$x({iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,checkMark:A.some(jx(o.icons)),caret:A.none(),shortcutContent:e.shortcut,presets:"normal",meta:e.meta},o,n);return vn(Nx({data:Lx(e),enabled:e.enabled,getApi:e=>({setActive:t=>{hh.set(e,t)},isActive:()=>hh.isOn(e),isEnabled:()=>!Hm.isDisabled(e),setEnabled:t=>Hm.set(e,!t)}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o),{toggling:{toggleClass:wv,toggleOnExecute:!1,selected:e.active}})})(a(e),t,r,n))));case"separator":return(e=>Xn("separatormenuitem",Dy,e))(e).fold(ey,(e=>A.some(gS(e))));case"fancymenuitem":return(e=>Xn("fancymenuitem",rx,e))(e).fold(ey,(e=>((e,t)=>be(mS,e.fancytype).map((o=>o(e,t))))(e,o)));default:return console.error("Unknown item in general menu",e),A.none()}var i},WS=(e,t,o,n,s,r,a)=>{const i=1===n,l=!i||Qv(e);return we(V(e,(e=>{switch(e.type){case"separator":return(n=e,Xn("Autocompleter.Separator",Dy,n)).fold(ey,(e=>A.some(gS(e))));case"cardmenuitem":return(e=>Xn("cardmenuitem",ex,e))(e).fold(ey,(e=>A.some(((e,t,o,n)=>{const s={dom:Gx(e.label),optComponents:[A.some({dom:{tag:"div",classes:[_v,Tv]},components:Xx(e.items,n)})]};return Nx({data:Lx({text:A.none(),...e}),enabled:e.enabled,getApi:e=>({isEnabled:()=>!Hm.isDisabled(e),setEnabled:t=>{Hm.set(e,!t),H(Qc(e.element,"*"),(o=>{e.getSystem().getByDom(o).each((e=>{e.hasConfigured(Hm)&&Hm.set(e,!t)}))}))}}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:A.from(n.itemBehaviours).getOr([])},s,t,o.providers)})({...e,onAction:t=>{e.onAction(t),o(e.value,e.meta)}},s,r,{itemBehaviours:qx(e.meta,r,A.none()),cardText:{matchText:t,highlightOn:a}}))));default:return(e=>Xn("Autocompleter.Item",By,e))(e).fold(ey,(e=>A.some(((e,t,o,n,s,r,a,i=!0)=>{const l=$x({presets:n,textContent:A.none(),htmlContent:o?e.text.map((e=>Yx(e,t))):A.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:A.none(),checkMark:A.none(),caret:A.none(),value:e.value},a.providers,i,e.icon),c=e.text.filter((e=>!o&&""!==e));return Nx({data:Lx(e),enabled:e.enabled,getApi:x({}),onAction:t=>s(e.value,e.meta),onSetup:x(b),triggersSubmenu:!1,itemBehaviours:qx(e,a,c)},l,r,a.providers)})(e,t,i,"normal",o,s,r,l))))}var n})))},jS=(e,t,o,n,s,r)=>{const a=Qv(t),i=we(V(t,(e=>{const t=e=>US(e,o,n,(e=>s?!ve(e,"text"):a)(e),s);return"nestedmenuitem"===e.type&&e.getSubmenuItems().length<=0?t({...e,enabled:!1}):t(e)}))),l=(e=>"no-search"===e.searchMode?{menuType:"normal"}:{menuType:"searchable",searchMode:e})(r);return(s?ty:oy)(e,a,i,1,l)},GS=e=>Gh.singleData(e.value,e),$S=e=>Pc(ze(e.startContainer),e.startOffset,ze(e.endContainer),e.endOffset),qS=(e,t)=>{const o=da("autocompleter"),n=Ms(!1),s=Ms(!1),r=sc(),a=di($h.sketch({dom:{tag:"div",classes:["tox-autocompleter"],attributes:{id:o}},components:[],fireDismissalEventInstead:{},inlineBehaviours:El([oh("dismissAutocompleter",[jr(_r(),(()=>u())),jr(Ir(),((t,o)=>{Et(o.event.target,"id").each((t=>Ot(ze(e.getBody()),"aria-activedescendant",t)))}))])]),lazySink:t.getSink})),i=()=>$h.isOpen(a),l=s.get,c=()=>{if(i()){$h.hide(a),e.dom.remove(o,!1);const t=ze(e.getBody());Et(t,"aria-owns").filter((e=>e===o)).each((()=>{Mt(t,"aria-owns"),Mt(t,"aria-activedescendant")}))}},d=()=>$h.getContent(a).bind((e=>te(e.components(),0))),u=()=>e.execCommand("mceAutocompleterClose"),m=s=>{const i=(o=>{const s=re(o,(e=>A.from(e.columns))).getOr(1);return Y(o,(o=>{const a=o.items;return WS(a,o.matchText,((t,s)=>{const a={hide:()=>u(),reload:t=>{c(),e.execCommand("mceAutocompleterReload",!1,{fetchOptions:t})}};e.execCommand("mceAutocompleterRefreshActiveRange"),r.get().each((e=>{n.set(!0),o.onAction(a,e,t,s),n.set(!1)}))}),s,fv.BUBBLE_TO_SANDBOX,t,o.highlightOn)}))})(s);i.length>0?(((t,o)=>{const n=re(t,(e=>A.from(e.columns))).getOr(1);$h.showMenuAt(a,{anchor:{type:"selection",getSelection:()=>r.get().map($S),root:ze(e.getBody())}},((e,t,o,n)=>{const s=aS(t,n),r=Dv(n);return{data:GS({...e,movement:s,menuBehaviours:dx("auto"!==t?[]:[Zr(((e,t)=>{cx(e,4,r.item).each((({numColumns:t,numRows:o})=>{$p.setGridSize(e,o,t)}))}))])}),menu:{markers:Dv(n),fakeFocus:o===PS.ContentFocus}}})(oy("autocompleter-value",!0,o,n,{menuType:"normal"}),n,PS.ContentFocus,"normal")),d().each(Km.highlightFirst)})(s,i),Ot(ze(e.getBody()),"aria-owns",o),e.inline||g()):c()},g=()=>{e.dom.get(o)&&e.dom.remove(o,!1);const t=e.getDoc().documentElement,n=e.selection.getNode(),s=(e=>sa(e,!0))(a.element);Ft(s,{border:"0",clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:"0",position:"absolute",width:"1px",top:`${n.offsetTop}px`,left:`${n.offsetLeft}px`}),e.dom.add(t,s.dom),yi(s,'[role="menu"]').each((e=>{Pt(e,"position"),Pt(e,"max-height")}))};e.on("AutocompleterStart",(({lookupData:e})=>{s.set(!0),n.set(!1),m(e)})),e.on("AutocompleterUpdate",(({lookupData:e})=>m(e))),e.on("AutocompleterUpdateActiveRange",(({range:e})=>r.set(e))),e.on("AutocompleterEnd",(()=>{c(),s.set(!1),n.set(!1),r.clear()}));((e,t)=>{const o=(e,t)=>{Nr(e,Zs(),{raw:t})},n=()=>e.getMenu().bind(Km.getHighlighted);t.on("keydown",(t=>{const s=t.which;e.isActive()&&(e.isMenuOpen()?13===s?(n().each(Lr),t.preventDefault()):40===s?(n().fold((()=>{e.getMenu().each(Km.highlightFirst)}),(e=>{o(e,t)})),t.preventDefault(),t.stopImmediatePropagation()):37!==s&&38!==s&&39!==s||n().each((e=>{o(e,t),t.preventDefault(),t.stopImmediatePropagation()})):13!==s&&38!==s&&40!==s||e.cancelIfNecessary())})),t.on("NodeChange",(()=>{!e.isActive()||e.isProcessingAction()||t.queryCommandState("mceAutoCompleterInRange")||e.cancelIfNecessary()}))})({cancelIfNecessary:u,isMenuOpen:i,isActive:l,isProcessingAction:n.get,getMenu:d},e)},YS=["visible","hidden","clip"],XS=e=>Me(e).length>0&&!R(YS,e),KS=e=>{if(Ge(e)){const t=Nt(e,"overflow-x"),o=Nt(e,"overflow-y");return XS(t)||XS(o)}return!1},JS=(e,t)=>mv(e)?(e=>{const t=Zc(e,KS),o=0===t.length?yt(e).map(xt).map((e=>Zc(e,KS))).getOr([]):t;return oe(o).map((e=>({element:e,others:o.slice(1)})))})(t):A.none(),ZS=e=>{const t=[...V(e.others,Qo),on()];return((e,t)=>j(t,((e,t)=>tn(e,t)),e))(Qo(e.element),t)},QS=(e,t,o)=>xi(e,t,o).isSome(),ek=(e,t)=>{let o=null;return{cancel:()=>{null!==o&&(clearTimeout(o),o=null)},schedule:(...n)=>{o=setTimeout((()=>{e.apply(null,n),o=null}),t)}}},tk=e=>{const t=e.raw;return void 0===t.touches||1!==t.touches.length?A.none():A.some(t.touches[0])},ok=(e,t)=>{const o={stopBackspace:!0,...t},n=(e=>{const t=sc(),o=Ms(!1),n=ek((t=>{e.triggerEvent(fr(),t),o.set(!0)}),400),s=Is([{key:Ps(),value:e=>(tk(e).each((s=>{n.cancel();const r={x:s.clientX,y:s.clientY,target:e.target};n.schedule(e),o.set(!1),t.set(r)})),A.none())},{key:Us(),value:e=>(n.cancel(),tk(e).each((e=>{t.on((o=>{((e,t)=>{const o=Math.abs(e.clientX-t.x),n=Math.abs(e.clientY-t.y);return o>5||n>5})(e,o)&&t.clear()}))})),A.none())},{key:Ws(),value:s=>(n.cancel(),t.get().filter((e=>et(e.target,s.target))).map((t=>o.get()?(s.prevent(),!1):e.triggerEvent(hr(),s))))}]);return{fireIfReady:(e,t)=>be(s,t).bind((t=>t(e)))}})(o),s=V(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),(t=>ac(e,t,(e=>{n.fireIfReady(e,t).each((t=>{t&&e.kill()})),o.triggerEvent(t,e)&&e.kill()})))),r=sc(),a=ac(e,"paste",(e=>{n.fireIfReady(e,"paste").each((t=>{t&&e.kill()})),o.triggerEvent("paste",e)&&e.kill(),r.set(setTimeout((()=>{o.triggerEvent(ur(),e)}),0))})),i=ac(e,"keydown",(e=>{o.triggerEvent("keydown",e)?e.kill():o.stopBackspace&&(e=>e.raw.which===Jm[0]&&!R(["input","textarea"],We(e.target))&&!QS(e.target,'[contenteditable="true"]'))(e)&&e.prevent()})),l=ac(e,"focusin",(e=>{o.triggerEvent("focusin",e)&&e.kill()})),c=sc(),d=ac(e,"focusout",(e=>{o.triggerEvent("focusout",e)&&e.kill(),c.set(setTimeout((()=>{o.triggerEvent(dr(),e)}),0))}));return{unbind:()=>{H(s,(e=>{e.unbind()})),i.unbind(),l.unbind(),d.unbind(),a.unbind(),r.on(clearTimeout),c.on(clearTimeout)}}},nk=(e,t)=>{const o=be(e,"target").getOr(t);return Ms(o)},sk=Ds([{stopped:[]},{resume:["element"]},{complete:[]}]),rk=(e,t,o,n,s,r)=>{const a=e(t,n),i=((e,t)=>{const o=Ms(!1),n=Ms(!1);return{stop:()=>{o.set(!0)},cut:()=>{n.set(!0)},isStopped:o.get,isCut:n.get,event:e,setSource:t.set,getSource:t.get}})(o,s);return a.fold((()=>(r.logEventNoHandlers(t,n),sk.complete())),(e=>{const o=e.descHandler;return Da(o)(i),i.isStopped()?(r.logEventStopped(t,e.element,o.purpose),sk.stopped()):i.isCut()?(r.logEventCut(t,e.element,o.purpose),sk.complete()):at(e.element).fold((()=>(r.logNoParent(t,e.element,o.purpose),sk.complete())),(n=>(r.logEventResponse(t,e.element,o.purpose),sk.resume(n))))}))},ak=(e,t,o,n,s,r)=>rk(e,t,o,n,s,r).fold(E,(n=>ak(e,t,o,n,s,r)),T),ik=(e,t,o,n,s)=>{const r=nk(o,n);return ak(e,t,o,n,r,s)},lk=()=>{const e=(()=>{const e={};return{registerId:(t,o,n)=>{le(n,((n,s)=>{const r=void 0!==e[s]?e[s]:{};r[o]=((e,t)=>({cHandler:k.apply(void 0,[e.handler].concat(t)),purpose:e.purpose}))(n,t),e[s]=r}))},unregisterId:t=>{le(e,((e,o)=>{ve(e,t)&&delete e[t]}))},filterByType:t=>be(e,t).map((e=>pe(e,((e,t)=>((e,t)=>({id:e,descHandler:t}))(t,e))))).getOr([]),find:(t,o,n)=>be(e,o).bind((e=>Ns(n,(t=>((e,t)=>fa(t).bind((t=>be(e,t))).map((e=>((e,t)=>({element:e,descHandler:t}))(t,e))))(e,t)),t)))}})(),t={},o=o=>{fa(o.element).each((o=>{delete t[o],e.unregisterId(o)}))};return{find:(t,o,n)=>e.find(t,o,n),filter:t=>e.filterByType(t),register:n=>{const s=(e=>{const t=e.element;return fa(t).getOrThunk((()=>((e,t)=>{const o=da(ga+"uid-");return ha(t,o),o})(0,e.element)))})(n);ye(t,s)&&((e,n)=>{const s=t[n];if(s!==e)throw new Error('The tagId "'+n+'" is already used by: '+ra(s.element)+"\nCannot use it for: "+ra(e.element)+"\nThe conflicting element is"+(wt(s.element)?" ":" not ")+"already in the DOM");o(e)})(n,s);const r=[n];e.registerId(r,s,n.events),t[s]=n},unregister:o,getById:e=>be(t,e)}},ck=wm({name:"Container",factory:e=>{const{attributes:t,...o}=e.dom;return{uid:e.uid,dom:{tag:"div",attributes:{role:"presentation",...t},...o},components:e.components,behaviours:wu(e.containerBehaviours),events:e.events,domModification:e.domModification,eventOrder:e.eventOrder}},configFields:[ws("components",[]),xu("containerBehaviours",[]),ws("events",{}),ws("domModification",{}),ws("eventOrder",{})]}),dk=e=>{const t=t=>at(e.element).fold(E,(e=>et(t,e))),o=lk(),n=(e,n)=>o.find(t,e,n),s=ok(e.element,{triggerEvent:(e,t)=>Ti(e,t.target,(o=>((e,t,o,n)=>ik(e,t,o,o.target,n))(n,e,t,o)))}),r={debugInfo:x("real"),triggerEvent:(e,t,o)=>{Ti(e,t,(s=>ik(n,e,o,t,s)))},triggerFocus:(e,t)=>{fa(e).fold((()=>{Nl(e)}),(o=>{Ti(cr(),e,(o=>(((e,t,o,n,s)=>{const r=nk(o,n);rk(e,t,o,n,r,s)})(n,cr(),{originator:t,kill:b,prevent:b,target:e},e,o),!1)))}))},triggerEscape:(e,t)=>{r.triggerEvent("keydown",e.element,t.event)},getByUid:e=>p(e),getByDom:e=>h(e),build:di,buildOrPatch:ci,addToGui:e=>{l(e)},removeFromGui:e=>{c(e)},addToWorld:e=>{a(e)},removeFromWorld:e=>{i(e)},broadcast:e=>{u(e)},broadcastOn:(e,t)=>{m(e,t)},broadcastEvent:(e,t)=>{g(e,t)},isConnected:E},a=e=>{e.connect(r),qe(e.element)||(o.register(e),H(e.components(),a),r.triggerEvent(yr(),e.element,{target:e.element}))},i=e=>{qe(e.element)||(H(e.components(),i),o.unregister(e)),e.disconnect()},l=t=>{Fd(e,t)},c=e=>{Ld(e)},d=e=>{const t=o.filter(mr());H(t,(t=>{const o=t.descHandler;Da(o)(e)}))},u=e=>{d({universal:!0,data:e})},m=(e,t)=>{d({universal:!1,channels:e,data:t})},g=(e,t)=>((e,t,o)=>{const n=(e=>{const t=Ms(!1);return{stop:()=>{t.set(!0)},cut:b,isStopped:t.get,isCut:T,event:e,setSource:O("Cannot set source of a broadcasted event"),getSource:O("Cannot get source of a broadcasted event")}})(t);return H(e,(e=>{const t=e.descHandler;Da(t)(n)})),n.isStopped()})(o.filter(e),t),p=e=>o.getById(e).fold((()=>an.error(new Error('Could not find component with uid: "'+e+'" in system.'))),an.value),h=e=>{const t=fa(e).getOr("not found");return p(t)};return a(e),{root:e,element:e.element,destroy:()=>{s.unbind(),Wo(e.element)},add:l,remove:c,getByUid:p,getByDom:h,addToWorld:a,removeFromWorld:i,broadcast:u,broadcastOn:m,broadcastEvent:g}},uk=x([ws("prefix","form-field"),xu("fieldBehaviours",[_m,yu])]),mk=x([Xu({schema:[ss("dom")],name:"label"}),Xu({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[ss("text")],name:"aria-descriptor"}),qu({factory:{sketch:e=>{const t=((e,t)=>{const o={};return le(e,((e,n)=>{R(t,n)||(o[n]=e)})),o})(e,["factory"]);return e.factory.sketch(t)}},schema:[ss("factory")],name:"field"})]),gk=Sm({name:"FormField",configFields:uk(),partFields:mk(),factory:(e,t,o,n)=>{const s=Su(e.fieldBehaviours,[_m.config({find:t=>im(t,e,"field")}),yu.config({store:{mode:"manual",getValue:e=>_m.getCurrent(e).bind(yu.getValue),setValue:(e,t)=>{_m.getCurrent(e).each((e=>{yu.setValue(e,t)}))}}})]),r=Pr([Zr(((t,o)=>{const n=cm(t,e,["label","field","aria-descriptor"]);n.field().each((t=>{const o=da(e.prefix);n.label().each((e=>{Ot(e.element,"for",o),Ot(t.element,"id",o)})),n["aria-descriptor"]().each((o=>{const n=da(e.prefix);Ot(o.element,"id",n),Ot(t.element,"aria-describedby",n)}))}))}))]),a={getField:t=>im(t,e,"field"),getLabel:t=>im(t,e,"label")};return{uid:e.uid,dom:e.dom,components:t,behaviours:s,events:r,apis:a}},apis:{getField:(e,t)=>e.getField(t),getLabel:(e,t)=>e.getLabel(t)}});var pk=Object.freeze({__proto__:null,exhibit:(e,t)=>Ma({attributes:Is([{key:t.tabAttr,value:"true"}])})}),hk=[ws("tabAttr","data-alloy-tabstop")];const fk=Ml({fields:hk,name:"tabstopping",active:pk});var bk=tinymce.util.Tools.resolve("tinymce.html.Entities");const vk=(e,t,o,n)=>{const s=yk(e,t,o,n);return gk.sketch(s)},yk=(e,t,o,n)=>({dom:xk(o),components:e.toArray().concat([t]),fieldBehaviours:El(n)}),xk=e=>({tag:"div",classes:["tox-form__group"].concat(e)}),wk=(e,t)=>gk.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[ai(t.translate(e))]}),Sk=da("form-component-change"),kk=da("form-close"),Ck=da("form-cancel"),Ok=da("form-action"),_k=da("form-submit"),Tk=da("form-block"),Ek=da("form-unblock"),Ak=da("form-tabchange"),Mk=da("form-resize"),Dk=(e,t,o)=>{const n=e.label.map((e=>wk(e,t))),s=t.icons(),r=e=>(t,o)=>{xi(o.event.target,"[data-collection-item-value]").each((n=>{e(t,o,n,Tt(n,"data-collection-item-value"))}))},a=r(((o,n,s,r)=>{n.stop(),t.isDisabled()||Nr(o,Ok,{name:e.name,value:r})})),i=[jr(Xs(),r(((e,t,o)=>{Nl(o)}))),jr(or(),a),jr(hr(),a),jr(Ks(),r(((e,t,o)=>{yi(e.element,"."+Ov).each((e=>{$a(e,Ov)})),ja(o,Ov)}))),jr(Js(),r((e=>{yi(e.element,"."+Ov).each((e=>{$a(e,Ov),Ll(e)}))}))),ta(r(((t,o,n,s)=>{Nr(t,Ok,{name:e.name,value:s})})))],l=(e,t)=>V(Qc(e.element,".tox-collection__item"),t),c=gk.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==e.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:w},behaviours:El([Hm.config({disabled:t.isDisabled,onDisabled:e=>{l(e,(e=>{ja(e,"tox-collection__item--state-disabled"),Ot(e,"aria-disabled",!0)}))},onEnabled:e=>{l(e,(e=>{$a(e,"tox-collection__item--state-disabled"),Mt(e,"aria-disabled")}))}}),_x(),th.config({}),wx.config({...t.tooltips.getConfig({tooltipText:"",onShow:e=>{yi(e.element,"."+Ov+"[data-mce-tooltip]").each((o=>{Et(o,"data-mce-tooltip").each((o=>{wx.setComponents(e,t.tooltips.getComponents({tooltipText:o}))}))}))}}),mode:"children-keyboard-focus",anchor:e=>({type:"node",node:yi(e.element,"."+Ov).orThunk((()=>Qe(".tox-collection__item"))),root:e.element,layouts:{onLtr:x([pl,gl,cl,ul,dl,ml]),onRtl:x([pl,gl,cl,ul,dl,ml])},bubble:vc(0,-2,{})})}),yu.config({store:{mode:"memory",initialValue:o.getOr([])},onSetValue:(o,n)=>{((o,n)=>{const r=V(n,(o=>{const n=Yf.translate(o.text),r=1===e.columns?`<div class="tox-collection__item-label">${n}</div>`:"",a=`<div class="tox-collection__item-icon">${(e=>{var t;return null!==(t=s[e])&&void 0!==t?t:e})(o.icon)}</div>`,i={_:" "," - ":" ","-":" "},l=n.replace(/\_| \- |\-/g,(e=>i[e]));return`<div data-mce-tooltip="${l}" class="tox-collection__item${t.isDisabled()?" tox-collection__item--state-disabled":""}" tabindex="-1" data-collection-item-value="${bk.encodeAllRaw(o.value)}" aria-label="${l}">${a}${r}</div>`})),a="auto"!==e.columns&&e.columns>1?z(r,e.columns):[r],i=V(a,(e=>`<div class="tox-collection__group">${e.join("")}</div>`));na(o.element,i.join(""))})(o,n),"auto"===e.columns&&cx(o,5,"tox-collection__item").each((({numRows:e,numColumns:t})=>{$p.setGridSize(o,e,t)})),Rr(o,Mk)}}),fk.config({}),$p.config((d=e.columns,"normal",1===d?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===d?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:".tox-collection__group",cell:`.${vv}`}})),oh("collection-events",i)]),eventOrder:{[gr()]:["disabling","alloy.base.behaviour","collection-events"],[Ks()]:["collection-events","tooltipping"]}});var d;return vk(n,c,["tox-form__group--collection"],[])},Bk=["input","textarea"],Ik=e=>{const t=We(e);return R(Bk,t)},Fk=(e,t)=>{const o=t.getRoot(e).getOr(e.element);$a(o,t.invalidClass),t.notify.each((t=>{Ik(e.element)&&Ot(e.element,"aria-invalid",!1),t.getContainer(e).each((e=>{na(e,t.validHtml)})),t.onValid(e)}))},Rk=(e,t,o,n)=>{const s=t.getRoot(e).getOr(e.element);ja(s,t.invalidClass),t.notify.each((t=>{Ik(e.element)&&Ot(e.element,"aria-invalid",!0),t.getContainer(e).each((e=>{na(e,n)})),t.onInvalid(e,n)}))},Nk=(e,t,o)=>t.validator.fold((()=>kS(an.value(!0))),(t=>t.validate(e))),Lk=(e,t,o)=>(t.notify.each((t=>{t.onValidate(e)})),Nk(e,t).map((o=>e.getSystem().isConnected()?o.fold((o=>(Rk(e,t,0,o),an.error(o))),(o=>(Fk(e,t),an.value(o)))):an.error("No longer in system"))));var zk=Object.freeze({__proto__:null,markValid:Fk,markInvalid:Rk,query:Nk,run:Lk,isInvalid:(e,t)=>{const o=t.getRoot(e).getOr(e.element);return qa(o,t.invalidClass)}}),Vk=Object.freeze({__proto__:null,events:(e,t)=>e.validator.map((t=>Pr([jr(t.onEvent,(t=>{Lk(t,e).get(w)}))].concat(t.validateOnLoad?[Zr((t=>{Lk(t,e).get(b)}))]:[])))).getOr({})}),Hk=[ss("invalidClass"),ws("getRoot",A.none),xs("notify",[ws("aria","alert"),ws("getContainer",A.none),ws("validHtml",""),Ni("onValid"),Ni("onInvalid"),Ni("onValidate")]),xs("validator",[ss("validate"),ws("onEvent","input"),ws("validateOnLoad",!0)])];const Pk=Ml({fields:Hk,name:"invalidating",active:Vk,apis:zk,extra:{validation:e=>t=>{const o=yu.getValue(t);return kS(e(o))}}}),Uk=Ml({fields:[],name:"unselecting",active:Object.freeze({__proto__:null,events:()=>Pr([Ur(ar(),E)]),exhibit:()=>Ma({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})})}),Wk=da("color-input-change"),jk=da("color-swatch-change"),Gk=da("color-picker-cancel"),$k=Xu({schema:[ss("dom")],name:"label"}),qk=e=>Xu({name:e+"-edge",overrides:t=>t.model.manager.edgeActions[e].fold((()=>({})),(e=>({events:Pr([Gr(Ps(),((t,o,n)=>e(t,n)),[t]),Gr(Gs(),((t,o,n)=>e(t,n)),[t]),Gr($s(),((t,o,n)=>{n.mouseIsDown.get()&&e(t,n)}),[t])])})))}),Yk=qk("top-left"),Xk=qk("top"),Kk=qk("top-right"),Jk=qk("right"),Zk=qk("bottom-right"),Qk=qk("bottom"),eC=qk("bottom-left"),tC=qk("left"),oC=qu({name:"thumb",defaults:x({dom:{styles:{position:"absolute"}}}),overrides:e=>({events:Pr([qr(Ps(),e,"spectrum"),qr(Us(),e,"spectrum"),qr(Ws(),e,"spectrum"),qr(Gs(),e,"spectrum"),qr($s(),e,"spectrum"),qr(Ys(),e,"spectrum")])})}),nC=e=>mg(e.event);var sC=[$k,tC,Jk,Xk,Qk,Yk,Kk,eC,Zk,oC,qu({schema:[os("mouseIsDown",(()=>Ms(!1)))],name:"spectrum",overrides:e=>{const t=e.model.manager,o=(o,n)=>t.getValueFromEvent(n).map((n=>t.setValueFrom(o,e,n)));return{behaviours:El([$p.config({mode:"special",onLeft:(o,n)=>t.onLeft(o,e,nC(n)),onRight:(o,n)=>t.onRight(o,e,nC(n)),onUp:(o,n)=>t.onUp(o,e,nC(n)),onDown:(o,n)=>t.onDown(o,e,nC(n))}),fk.config({}),ih.config({})]),events:Pr([jr(Ps(),o),jr(Us(),o),jr(Gs(),o),jr($s(),((t,n)=>{e.mouseIsDown.get()&&o(t,n)}))])}}})];const rC=x("slider.change.value"),aC=e=>{const t=e.event.raw;if((e=>-1!==e.type.indexOf("touch"))(t)){const e=t;return void 0!==e.touches&&1===e.touches.length?A.some(e.touches[0]).map((e=>Yt(e.clientX,e.clientY))):A.none()}{const e=t;return void 0!==e.clientX?A.some(e).map((e=>Yt(e.clientX,e.clientY))):A.none()}},iC=e=>e.model.minX,lC=e=>e.model.minY,cC=e=>e.model.minX-1,dC=e=>e.model.minY-1,uC=e=>e.model.maxX,mC=e=>e.model.maxY,gC=e=>e.model.maxX+1,pC=e=>e.model.maxY+1,hC=(e,t,o)=>t(e)-o(e),fC=e=>hC(e,uC,iC),bC=e=>hC(e,mC,lC),vC=e=>fC(e)/2,yC=e=>bC(e)/2,xC=(e,t)=>t?e.stepSize*e.speedMultiplier:e.stepSize,wC=e=>e.snapToGrid,SC=e=>e.snapStart,kC=e=>e.rounded,CC=(e,t)=>void 0!==e[t+"-edge"],OC=e=>CC(e,"left"),_C=e=>CC(e,"right"),TC=e=>CC(e,"top"),EC=e=>CC(e,"bottom"),AC=e=>e.model.value.get(),MC=(e,t)=>({x:e,y:t}),DC=(e,t)=>{Nr(e,rC(),{value:t})},BC=(e,t,o,n)=>e<t?e:e>o?o:e===t?t-1:Math.max(t,e-n),IC=(e,t,o,n)=>e>o?e:e<t?t:e===o?o+1:Math.min(o,e+n),FC=(e,t,o)=>Math.max(t,Math.min(o,e)),RC=e=>{const{min:t,max:o,range:n,value:s,step:r,snap:a,snapStart:i,rounded:l,hasMinEdge:c,hasMaxEdge:d,minBound:u,maxBound:m,screenRange:g}=e,p=c?t-1:t,h=d?o+1:o;if(s<u)return p;if(s>m)return h;{const e=((e,t,o)=>Math.min(o,Math.max(e,t))-t)(s,u,m),c=FC(e/g*n+t,p,h);return a&&c>=t&&c<=o?((e,t,o,n,s)=>s.fold((()=>{const s=e-t,r=Math.round(s/n)*n;return FC(t+r,t-1,o+1)}),(t=>{const s=(e-t)%n,r=Math.round(s/n),a=Math.floor((e-t)/n),i=Math.floor((o-t)/n),l=t+Math.min(i,a+r)*n;return Math.max(t,l)})))(c,t,o,r,i):l?Math.round(c):c}},NC=e=>{const{min:t,max:o,range:n,value:s,hasMinEdge:r,hasMaxEdge:a,maxBound:i,maxOffset:l,centerMinEdge:c,centerMaxEdge:d}=e;return s<t?r?0:c:s>o?a?i:d:(s-t)/n*l},LC="top",zC="right",VC="bottom",HC="left",PC=e=>e.element.dom.getBoundingClientRect(),UC=(e,t)=>e[t],WC=e=>{const t=PC(e);return UC(t,HC)},jC=e=>{const t=PC(e);return UC(t,zC)},GC=e=>{const t=PC(e);return UC(t,LC)},$C=e=>{const t=PC(e);return UC(t,VC)},qC=e=>{const t=PC(e);return UC(t,"width")},YC=e=>{const t=PC(e);return UC(t,"height")},XC=(e,t,o)=>(e+t)/2-o,KC=(e,t)=>{const o=PC(e),n=PC(t),s=UC(o,HC),r=UC(o,zC),a=UC(n,HC);return XC(s,r,a)},JC=(e,t)=>{const o=PC(e),n=PC(t),s=UC(o,LC),r=UC(o,VC),a=UC(n,LC);return XC(s,r,a)},ZC=(e,t)=>{Nr(e,rC(),{value:t})},QC=(e,t,o)=>{const n={min:iC(t),max:uC(t),range:fC(t),value:o,step:xC(t),snap:wC(t),snapStart:SC(t),rounded:kC(t),hasMinEdge:OC(t),hasMaxEdge:_C(t),minBound:WC(e),maxBound:jC(e),screenRange:qC(e)};return RC(n)},eO=e=>(t,o,n)=>((e,t,o,n)=>{const s=(e>0?IC:BC)(AC(o),iC(o),uC(o),xC(o,n));return ZC(t,s),A.some(s)})(e,t,o,n).map(E),tO=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=qC(e),a=n.bind((t=>A.some(KC(t,e)))).getOr(0),i=s.bind((t=>A.some(KC(t,e)))).getOr(r),l={min:iC(t),max:uC(t),range:fC(t),value:o,hasMinEdge:OC(t),hasMaxEdge:_C(t),minBound:WC(e),minOffset:0,maxBound:jC(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return NC(l)})(t,r,o,n,s);return WC(t)-WC(e)+a},oO=eO(-1),nO=eO(1),sO=A.none,rO=A.none,aO={"top-left":A.none(),top:A.none(),"top-right":A.none(),right:A.some(((e,t)=>{DC(e,gC(t))})),"bottom-right":A.none(),bottom:A.none(),"bottom-left":A.none(),left:A.some(((e,t)=>{DC(e,cC(t))}))};var iO=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=QC(e,t,o);return ZC(e,n),n},setToMin:(e,t)=>{const o=iC(t);ZC(e,o)},setToMax:(e,t)=>{const o=uC(t);ZC(e,o)},findValueOfOffset:QC,getValueFromEvent:e=>aC(e).map((e=>e.left)),findPositionOfValue:tO,setPositionFromValue:(e,t,o,n)=>{const s=AC(o),r=tO(e,n.getSpectrum(e),s,n.getLeftEdge(e),n.getRightEdge(e),o),a=Qt(t.element)/2;It(t.element,"left",r-a+"px")},onLeft:oO,onRight:nO,onUp:sO,onDown:rO,edgeActions:aO});const lO=(e,t)=>{Nr(e,rC(),{value:t})},cO=(e,t,o)=>{const n={min:lC(t),max:mC(t),range:bC(t),value:o,step:xC(t),snap:wC(t),snapStart:SC(t),rounded:kC(t),hasMinEdge:TC(t),hasMaxEdge:EC(t),minBound:GC(e),maxBound:$C(e),screenRange:YC(e)};return RC(n)},dO=e=>(t,o,n)=>((e,t,o,n)=>{const s=(e>0?IC:BC)(AC(o),lC(o),mC(o),xC(o,n));return lO(t,s),A.some(s)})(e,t,o,n).map(E),uO=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=YC(e),a=n.bind((t=>A.some(JC(t,e)))).getOr(0),i=s.bind((t=>A.some(JC(t,e)))).getOr(r),l={min:lC(t),max:mC(t),range:bC(t),value:o,hasMinEdge:TC(t),hasMaxEdge:EC(t),minBound:GC(e),minOffset:0,maxBound:$C(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return NC(l)})(t,r,o,n,s);return GC(t)-GC(e)+a},mO=A.none,gO=A.none,pO=dO(-1),hO=dO(1),fO={"top-left":A.none(),top:A.some(((e,t)=>{DC(e,dC(t))})),"top-right":A.none(),right:A.none(),"bottom-right":A.none(),bottom:A.some(((e,t)=>{DC(e,pC(t))})),"bottom-left":A.none(),left:A.none()};var bO=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=cO(e,t,o);return lO(e,n),n},setToMin:(e,t)=>{const o=lC(t);lO(e,o)},setToMax:(e,t)=>{const o=mC(t);lO(e,o)},findValueOfOffset:cO,getValueFromEvent:e=>aC(e).map((e=>e.top)),findPositionOfValue:uO,setPositionFromValue:(e,t,o,n)=>{const s=AC(o),r=uO(e,n.getSpectrum(e),s,n.getTopEdge(e),n.getBottomEdge(e),o),a=Gt(t.element)/2;It(t.element,"top",r-a+"px")},onLeft:mO,onRight:gO,onUp:pO,onDown:hO,edgeActions:fO});const vO=(e,t)=>{Nr(e,rC(),{value:t})},yO=(e,t)=>({x:e,y:t}),xO=(e,t)=>(o,n,s)=>((e,t,o,n,s)=>{const r=e>0?IC:BC,a=t?AC(n).x:r(AC(n).x,iC(n),uC(n),xC(n,s)),i=t?r(AC(n).y,lC(n),mC(n),xC(n,s)):AC(n).y;return vO(o,yO(a,i)),A.some(a)})(e,t,o,n,s).map(E),wO=xO(-1,!1),SO=xO(1,!1),kO=xO(-1,!0),CO=xO(1,!0),OO={"top-left":A.some(((e,t)=>{DC(e,MC(cC(t),dC(t)))})),top:A.some(((e,t)=>{DC(e,MC(vC(t),dC(t)))})),"top-right":A.some(((e,t)=>{DC(e,MC(gC(t),dC(t)))})),right:A.some(((e,t)=>{DC(e,MC(gC(t),yC(t)))})),"bottom-right":A.some(((e,t)=>{DC(e,MC(gC(t),pC(t)))})),bottom:A.some(((e,t)=>{DC(e,MC(vC(t),pC(t)))})),"bottom-left":A.some(((e,t)=>{DC(e,MC(cC(t),pC(t)))})),left:A.some(((e,t)=>{DC(e,MC(cC(t),yC(t)))}))};var _O=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=QC(e,t,o.left),s=cO(e,t,o.top),r=yO(n,s);return vO(e,r),r},setToMin:(e,t)=>{const o=iC(t),n=lC(t);vO(e,yO(o,n))},setToMax:(e,t)=>{const o=uC(t),n=mC(t);vO(e,yO(o,n))},getValueFromEvent:e=>aC(e),setPositionFromValue:(e,t,o,n)=>{const s=AC(o),r=tO(e,n.getSpectrum(e),s.x,n.getLeftEdge(e),n.getRightEdge(e),o),a=uO(e,n.getSpectrum(e),s.y,n.getTopEdge(e),n.getBottomEdge(e),o),i=Qt(t.element)/2,l=Gt(t.element)/2;It(t.element,"left",r-i+"px"),It(t.element,"top",a-l+"px")},onLeft:wO,onRight:SO,onUp:kO,onDown:CO,edgeActions:OO});const TO=Sm({name:"Slider",configFields:[ws("stepSize",1),ws("speedMultiplier",10),ws("onChange",b),ws("onChoose",b),ws("onInit",b),ws("onDragStart",b),ws("onDragEnd",b),ws("snapToGrid",!1),ws("rounded",!0),gs("snapStart"),rs("model",Qn("mode",{x:[ws("minX",0),ws("maxX",100),os("value",(e=>Ms(e.mode.minX))),ss("getInitialValue"),Hi("manager",iO)],y:[ws("minY",0),ws("maxY",100),os("value",(e=>Ms(e.mode.minY))),ss("getInitialValue"),Hi("manager",bO)],xy:[ws("minX",0),ws("maxX",100),ws("minY",0),ws("maxY",100),os("value",(e=>Ms({x:e.mode.minX,y:e.mode.minY}))),ss("getInitialValue"),Hi("manager",_O)]})),xu("sliderBehaviours",[$p,yu]),os("mouseIsDown",(()=>Ms(!1)))],partFields:sC,factory:(e,t,o,n)=>{const s=t=>lm(t,e,"thumb"),r=t=>lm(t,e,"spectrum"),a=t=>im(t,e,"left-edge"),i=t=>im(t,e,"right-edge"),l=t=>im(t,e,"top-edge"),c=t=>im(t,e,"bottom-edge"),d=e.model,u=d.manager,m=(t,o)=>{u.setPositionFromValue(t,o,e,{getLeftEdge:a,getRightEdge:i,getTopEdge:l,getBottomEdge:c,getSpectrum:r})},g=(e,t)=>{d.value.set(t);const o=s(e);m(e,o)},p=t=>{const o=e.mouseIsDown.get();e.mouseIsDown.set(!1),o&&im(t,e,"thumb").each((o=>{const n=d.value.get();e.onChoose(t,o,n)}))},h=(t,o)=>{o.stop(),e.mouseIsDown.set(!0),e.onDragStart(t,s(t))},f=(t,o)=>{o.stop(),e.onDragEnd(t,s(t)),p(t)},b=t=>{im(t,e,"spectrum").map($p.focusIn)};return{uid:e.uid,dom:e.dom,components:t,behaviours:Su(e.sliderBehaviours,[$p.config({mode:"special",focusIn:b}),yu.config({store:{mode:"manual",getValue:e=>d.value.get(),setValue:g}}),Fl.config({channels:{[ou()]:{onReceive:p}}})]),events:Pr([jr(rC(),((t,o)=>{((t,o)=>{g(t,o);const n=s(t);e.onChange(t,n,o),A.some(!0)})(t,o.event.value)})),Zr(((t,o)=>{const n=d.getInitialValue();d.value.set(n);const a=s(t);m(t,a);const i=r(t);e.onInit(t,a,i,d.value.get())})),jr(Ps(),h),jr(Ws(),f),jr(Gs(),((e,t)=>{b(e),h(e,t)})),jr(Ys(),f)]),apis:{resetToMin:t=>{u.setToMin(t,e)},resetToMax:t=>{u.setToMax(t,e)},setValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{setValue:(e,t,o)=>{e.setValue(t,o)},resetToMin:(e,t)=>{e.resetToMin(t)},resetToMax:(e,t)=>{e.resetToMax(t)},refresh:(e,t)=>{e.refresh(t)}}}),EO=da("rgb-hex-update"),AO=da("slider-update"),MO=da("palette-update"),DO="form",BO=[xu("formBehaviours",[yu])],IO=e=>"<alloy.field."+e+">",FO=(e,t)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Su(e.formBehaviours,[yu.config({store:{mode:"manual",getValue:t=>{const o=dm(t,e);return ce(o,((e,t)=>e().bind((e=>{return o=_m.getCurrent(e),n=new Error(`Cannot find a current component to extract the value from for form part '${t}': `+ra(e.element)),o.fold((()=>an.error(n)),an.value);var o,n})).map(yu.getValue)))},setValue:(t,o)=>{le(o,((o,n)=>{im(t,e,n).each((e=>{_m.getCurrent(e).each((e=>{yu.setValue(e,o)}))}))}))}}})]),apis:{getField:(t,o)=>im(t,e,o).bind(_m.getCurrent)}}),RO={getField:_a(((e,t,o)=>e.getField(t,o))),sketch:e=>{const t=(()=>{const e=[];return{field:(t,o)=>(e.push(t),om(DO,IO(t),o)),record:x(e)}})(),o=e(t),n=t.record(),s=V(n,(e=>qu({name:e,pname:IO(e)})));return bm(DO,BO,s,FO,o)}},NO=da("valid-input"),LO=da("invalid-input"),zO=da("validating-input"),VO="colorcustom.rgb.",HO=(e,t,o,n)=>{const s=(o,n)=>Pk.config({invalidClass:t("invalid"),notify:{onValidate:e=>{Nr(e,zO,{type:o})},onValid:e=>{Nr(e,NO,{type:o,value:yu.getValue(e)})},onInvalid:e=>{Nr(e,LO,{type:o,value:yu.getValue(e)})}},validator:{validate:t=>{const o=yu.getValue(t),s=n(o)?an.value(!0):an.error(e("aria.input.invalid"));return kS(s)},validateOnLoad:!1}}),r=(o,n,r,a,i)=>{const l=e(VO+"range"),c=gk.parts.label({dom:{tag:"label",attributes:{"aria-label":a}},components:[ai(r)]}),d=gk.parts.field({data:i,factory:Vv,inputAttributes:{type:"text",..."hex"===n?{"aria-live":"polite"}:{}},inputClasses:[t("textfield")],inputBehaviours:El([s(n,o),fk.config({})]),onSetValue:e=>{Pk.isInvalid(e)&&Pk.run(e).get(b)}}),u=[c,d],m="hex"!==n?[gk.parts["aria-descriptor"]({text:l})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:u.concat(m)}},a=(e,t)=>{const o=t.red,n=t.green,s=t.blue;yu.setValue(e,{red:o,green:n,blue:s})},i=Kh({dom:{tag:"div",classes:[t("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),l=(e,t)=>{i.getOpt(e).each((e=>{It(e.element,"background-color","#"+t.value)}))},c=wm({factory:()=>{const s={red:Ms(A.some(255)),green:Ms(A.some(255)),blue:Ms(A.some(255)),hex:Ms(A.some("ffffff"))},c=e=>s[e].get(),d=(e,t)=>{s[e].set(t)},u=e=>{const t=e.red,o=e.green,n=e.blue;d("red",A.some(t)),d("green",A.some(o)),d("blue",A.some(n))},m=(e,t)=>{const o=t.event;"hex"!==o.type?d(o.type,A.none()):n(e)},g=(e,t)=>{const n=t.event;(e=>"hex"===e.type)(n)?((e,t)=>{o(e);const n=Jx(t);d("hex",A.some(n.value));const s=mw(n);a(e,s),u(s),Nr(e,EO,{hex:n}),l(e,n)})(e,n.value):((e,t,o)=>{const n=parseInt(o,10);d(t,A.some(n)),c("red").bind((e=>c("green").bind((t=>c("blue").map((o=>cw(e,t,o,1))))))).each((t=>{const o=((e,t)=>{const o=nw(t);return RO.getField(e,"hex").each((t=>{ih.isFocused(t)||yu.setValue(e,{hex:o.value})})),o})(e,t);Nr(e,EO,{hex:o}),l(e,o)}))})(e,n.type,n.value)},p=t=>({label:e(VO+t+".label"),description:e(VO+t+".description")}),h=p("red"),f=p("green"),b=p("blue"),v=p("hex");return vn(RO.sketch((o=>({dom:{tag:"form",classes:[t("rgb-form")],attributes:{"aria-label":e("aria.color.picker")}},components:[o.field("red",gk.sketch(r(dw,"red",h.label,h.description,255))),o.field("green",gk.sketch(r(dw,"green",f.label,f.description,255))),o.field("blue",gk.sketch(r(dw,"blue",b.label,b.description,255))),o.field("hex",gk.sketch(r(ew,"hex",v.label,v.description,"ffffff"))),i.asSpec()],formBehaviours:El([Pk.config({invalidClass:t("form-invalid")}),oh("rgb-form-events",[jr(NO,g),jr(LO,m),jr(zO,m)])])}))),{apis:{updateHex:(e,t)=>{yu.setValue(e,{hex:t.value}),((e,t)=>{const o=mw(t);a(e,o),u(o)})(e,t),l(e,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:(e,t,o)=>{e.updateHex(t,o)}},extraApis:{}});return c},PO=(e,t)=>{const o=wm({name:"ColourPicker",configFields:[ss("dom"),ws("onValidHex",b),ws("onInvalidHex",b)],factory:o=>{const n=HO(e,t,o.onValidHex,o.onInvalidHex),s=((e,t)=>{const o=TO.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[t("sv-palette-spectrum")]}}),n=TO.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette-thumb")],innerHtml:`<div class=${t("sv-palette-inner-thumb")} role="presentation"></div>`}}),s=(e,t)=>{const{width:o,height:n}=e,s=e.getContext("2d");if(null===s)return;s.fillStyle=t,s.fillRect(0,0,o,n);const r=s.createLinearGradient(0,0,o,0);r.addColorStop(0,"rgba(255,255,255,1)"),r.addColorStop(1,"rgba(255,255,255,0)"),s.fillStyle=r,s.fillRect(0,0,o,n);const a=s.createLinearGradient(0,0,0,n);a.addColorStop(0,"rgba(0,0,0,0)"),a.addColorStop(1,"rgba(0,0,0,1)"),s.fillStyle=a,s.fillRect(0,0,o,n)};return wm({factory:r=>{const a=x({x:0,y:0}),i=El([_m.config({find:A.some}),ih.config({})]);return TO.sketch({dom:{tag:"div",attributes:{role:"slider","aria-valuetext":e(["Saturation {0}%, Brightness {1}%",0,0])},classes:[t("sv-palette")]},model:{mode:"xy",getInitialValue:a},rounded:!1,components:[o,n],onChange:(t,o,n)=>{h(n)||Ot(t.element,"aria-valuetext",e(["Saturation {0}%, Brightness {1}%",Math.floor(n.x),Math.floor(100-n.y)])),Nr(t,MO,{value:n})},onInit:(e,t,o,n)=>{s(o.element.dom,hw(fw))},sliderBehaviours:i})},name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:(e,t,o)=>{((e,t)=>{const o=e.components()[0].element.dom,n=Mw(t,100,100),r=uw(n);s(o,hw(r))})(t,o)},setThumb:(t,o,n)=>{((t,o)=>{const n=Dw(mw(o));TO.setValue(t,{x:n.saturation,y:100-n.value}),Ot(t.element,"aria-valuetext",e(["Saturation {0}%, Brightness {1}%",n.saturation,n.value]))})(o,n)}},extraApis:{}})})(e,t),r={paletteRgba:Ms(fw),paletteHue:Ms(0)},a=Kh(((e,t)=>{const o=TO.parts.spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),n=TO.parts.thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return TO.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"slider","aria-valuemin":0,"aria-valuemax":360,"aria-valuenow":120}},rounded:!1,model:{mode:"y",getInitialValue:x(0)},components:[o,n],sliderBehaviours:El([ih.config({})]),onChange:(e,t,o)=>{Ot(e.element,"aria-valuenow",Math.floor(360-3.6*o)),Nr(e,AO,{value:o})}})})(0,t)),i=Kh(s.sketch({})),l=Kh(n.sketch({})),c=(e,t,o)=>{i.getOpt(e).each((e=>{s.setHue(e,o)}))},d=(e,t)=>{l.getOpt(e).each((e=>{n.updateHex(e,t)}))},u=(e,t,o)=>{a.getOpt(e).each((e=>{TO.setValue(e,(e=>100-e/360*100)(o))}))},m=(e,t)=>{i.getOpt(e).each((e=>{s.setThumb(e,t)}))},g=(e,t,o,n)=>{((e,t)=>{const o=mw(e);r.paletteRgba.set(o),r.paletteHue.set(t)})(t,o),H(n,(n=>{n(e,t,o)}))};return{uid:o.uid,dom:o.dom,components:[i.asSpec(),a.asSpec(),l.asSpec()],behaviours:El([oh("colour-picker-events",[jr(EO,(()=>{const e=[c,u,m];return(t,o)=>{const n=o.event.hex,s=(e=>Dw(mw(e)))(n);g(t,n,s.hue,e)}})()),jr(MO,(()=>{const e=[d];return(t,o)=>{const n=o.event.value,s=r.paletteHue.get(),a=Mw(s,n.x,100-n.y),i=Bw(a);g(t,i,s,e)}})()),jr(AO,(()=>{const e=[c,d];return(t,o)=>{const n=(e=>(100-e)/100*360)(o.event.value),s=r.paletteRgba.get(),a=Dw(s),i=Mw(n,a.saturation,a.value),l=Bw(i);g(t,l,n,e)}})())]),_m.config({find:e=>l.getOpt(e)}),$p.config({mode:"acyclic"})])}}});return o},UO=()=>_m.config({find:A.some}),WO=e=>_m.config({find:t=>dt(t.element,e).bind((e=>t.getSystem().getByDom(e).toOptional()))}),jO=In([ws("preprocess",w),ws("postprocess",w)]),GO=(e,t)=>{const o=Jn("RepresentingConfigs.memento processors",jO,t);return yu.config({store:{mode:"manual",getValue:t=>{const n=e.get(t),s=yu.getValue(n);return o.postprocess(s)},setValue:(t,n)=>{const s=o.preprocess(n),r=e.get(t);yu.setValue(r,s)}}})},$O=(e,t,o)=>yu.config({store:{mode:"manual",...e.map((e=>({initialValue:e}))).getOr({}),getValue:t,setValue:o}}),qO=(e,t,o)=>$O(e,(e=>t(e.element)),((e,t)=>o(e.element,t))),YO=e=>yu.config({store:{mode:"memory",initialValue:e}}),XO={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"};var KO=tinymce.util.Tools.resolve("tinymce.Resource");const JO=e=>ve(e,"init");var ZO=tinymce.util.Tools.resolve("tinymce.util.Tools");const QO=(e,t)=>{let o=null;const n=()=>{c(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...s)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,s)}),t)}}},e_=da("alloy-fake-before-tabstop"),t_=da("alloy-fake-after-tabstop"),o_=e=>({dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:e},behaviours:El([ih.config({ignore:!0}),fk.config({})])}),n_=(e,t)=>({dom:{tag:"div",classes:["tox-navobj",...e.getOr([])]},components:[o_([e_]),t,o_([t_])],behaviours:El([WO(1)])}),s_=(e,t)=>{Nr(e,Zs(),{raw:{which:9,shiftKey:t}})},r_=(e,t)=>{const o=t.element;qa(o,e_)?s_(e,!0):qa(o,t_)&&s_(e,!1)},a_=e=>QS(e,["."+e_,"."+t_].join(","),T),i_=da("update-dialog"),l_=da("update-title"),c_=da("update-body"),d_=da("update-footer"),u_=da("body-send-message"),m_=da("dialog-focus-shifted"),g_=Io().browser,p_=g_.isSafari(),h_=g_.isFirefox(),f_=p_||h_,b_=g_.isChromium(),v_=({scrollTop:e,scrollHeight:t,clientHeight:o})=>Math.ceil(e)+o>=t,y_=(e,t)=>e.scrollTo(0,"bottom"===t?99999999:t),x_=(e,t,o)=>{const n=e.dom;A.from(n.contentDocument).fold(o,(e=>{let o=0;const s=((e,t)=>{const o=e.body;return A.from(!/^<!DOCTYPE (html|HTML)/.test(t)&&(!b_&&!p_||g(o)&&(0!==o.scrollTop||Math.abs(o.scrollHeight-o.clientHeight)>1))?o:e.documentElement)})(e,t).map((e=>(o=e.scrollTop,e))).forall(v_),r=()=>{const e=n.contentWindow;g(e)&&(s?y_(e,"bottom"):!s&&f_&&0!==o&&y_(e,o))};p_&&n.addEventListener("load",r,{once:!0}),e.open(),e.write(t),e.close(),p_||r()}))},w_=Ce(f_,p_?500:200).map((e=>((e,t)=>{let o=null,n=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null,n=null)},throttle:(...s)=>{n=s,c(o)&&(o=setTimeout((()=>{const t=n;o=null,n=null,e.apply(null,t)}),t))}}})(x_,e))),S_=da("toolbar.button.execute"),k_=da("common-button-display-events"),C_={[gr()]:["disabling","alloy.base.behaviour","toggling","toolbar-button-events","tooltipping"],[Cr()]:["toolbar-button-events",k_],[Or()]:["toolbar-button-events","dropdown-events","tooltipping"],[Gs()]:["focusing","alloy.base.behaviour",k_]},O_=e=>It(e.element,"width",Nt(e.element,"width")),__=(e,t,o)=>nb(e,{tag:"span",classes:["tox-icon","tox-tbtn__icon-wrap"],behaviours:o},t),T_=(e,t)=>__(e,t,[]),E_=(e,t)=>__(e,t,[th.config({})]),A_=(e,t,o)=>({dom:{tag:"span",classes:[`${t}__select-label`]},components:[ai(o.translate(e))],behaviours:El([th.config({})])}),M_=da("update-menu-text"),D_=da("update-menu-icon"),B_=(e,t,o,n)=>{const s=Ms(b),r=e.text.map((e=>Kh(A_(e,t,o.providers)))),a=e.icon.map((e=>Kh(E_(e,o.providers.icons)))),i=(e,t)=>{const o=yu.getValue(e);return ih.focus(o),Nr(o,"keydown",{raw:t.event.raw}),LS.close(o),A.some(!0)},l=e.role.fold((()=>({})),(e=>({role:e}))),c=e.ariaLabel.fold((()=>({})),(e=>({"aria-label":o.providers.translate(e)}))),d=nb("chevron-down",{tag:"div",classes:[`${t}__select-chevron`]},o.providers.icons),u=da("common-button-display-events"),m="dropdown-events",p=Kh(LS.sketch({...e.uid?{uid:e.uid}:{},...l,dom:{tag:"button",classes:[t,`${t}--select`].concat(V(e.classes,(e=>`${t}--${e}`))),attributes:{...c,...g(n)?{"data-mce-name":n}:{}}},components:Rx([a.map((e=>e.asSpec())),r.map((e=>e.asSpec())),A.some(d)]),matchWidth:!0,useMinWidth:!0,onOpen:(t,o,n)=>{e.searchable&&(e=>{jv(e).each((e=>ih.focus(e)))})(n)},dropdownBehaviours:El([...e.dropdownBehaviours,Tx((()=>e.disabled||o.providers.isDisabled())),_x(),Uk.config({}),th.config({}),...e.tooltip.map((e=>wx.config(o.providers.tooltips.getConfig({tooltipText:o.providers.translate(e)})))).toArray(),oh(m,[Dx(e,s),Bx(e,s)]),oh(u,[Zr(((e,t)=>O_(e)))]),oh("menubutton-update-display-text",[jr(M_,((e,t)=>{r.bind((t=>t.getOpt(e))).each((e=>{th.set(e,[ai(o.providers.translate(t.event.text))])}))})),jr(D_,((e,t)=>{a.bind((t=>t.getOpt(e))).each((e=>{th.set(e,[E_(t.event.icon,o.providers.icons)])}))}))])]),eventOrder:vn(C_,{[Gs()]:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"],[Cr()]:["toolbar-button-events",wx.name(),m,u]}),sandboxBehaviours:El([$p.config({mode:"special",onLeft:i,onRight:i}),oh("dropdown-sandbox-events",[jr(Hv,((e,t)=>{(e=>{const t=yu.getValue(e),o=Wv(e).map(Gv);LS.refetch(t).get((()=>{const e=bS.getCoupled(t,"sandbox");o.each((t=>Wv(e).each((e=>((e,t)=>{yu.setValue(e,t.fetchPattern),e.element.dom.selectionStart=t.selectionStart,e.element.dom.selectionEnd=t.selectionEnd})(e,t)))))}))})(e),t.stop()})),jr(Pv,((e,t)=>{((e,t)=>{(e=>Qd.getState(e).bind(Km.getHighlighted).bind(Km.getHighlighted))(e).each((o=>{((e,t,o,n)=>{const s={...n,target:t};e.getSystem().triggerEvent(o,t,s)})(e,o.element,t.event.eventType,t.event.interactionEvent)}))})(e,t),t.stop()}))])]),lazySink:o.getSink,toggleClass:`${t}--active`,parts:{menu:{...Fv(0,e.columns,e.presets),fakeFocus:e.searchable,onHighlightItem:zS,onCollapseMenu:(e,t,o)=>{Km.getHighlighted(o).each((t=>{zS(e,o,t)}))},onDehighlightItem:VS}},getAnchorOverrides:()=>({maxHeightFunction:(e,t)=>{gc()(e,t-10)}}),fetch:t=>SS(k(e.fetch,t))}));return p.asSpec()},I_=e=>"separator"===e.type,F_={type:"separator"},R_=(e,t)=>{const o=((e,t)=>{const o=j(e,((e,o)=>(e=>r(e))(o)?""===o?e:"|"===o?e.length>0&&!I_(e[e.length-1])?e.concat([F_]):e:ve(t,o.toLowerCase())?e.concat([t[o.toLowerCase()]]):e:e.concat([o])),[]);return o.length>0&&I_(o[o.length-1])&&o.pop(),o})(r(e)?e.split(" "):e,t);return W(o,((e,o)=>{if((e=>ve(e,"getSubmenuItems"))(o)){const n=(e=>{const t=be(e,"value").getOrThunk((()=>da("generated-menu-item")));return vn({value:t},e)})(o),s=((e,t)=>{const o=e.getSubmenuItems(),n=R_(o,t);return{item:e,menus:vn(n.menus,{[e.value]:n.items}),expansions:vn(n.expansions,{[e.value]:e.value})}})(n,t);return{menus:vn(e.menus,s.menus),items:[s.item,...e.items],expansions:vn(e.expansions,s.expansions)}}return{...e,items:[o,...e.items]}}),{menus:{},expansions:{},items:[]})},N_=(e,t,o,n)=>{const s=da("primary-menu"),r=R_(e,o.shared.providers.menuItems());if(0===r.items.length)return A.none();const a=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-field",placeholder:e.placeholder}))))(n),i=jS(s,r.items,t,o,n.isHorizontalMenu,a),l=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-results"}))))(n),c=ce(r.menus,((e,n)=>jS(n,e,t,o,!1,l))),d=vn(c,Bs(s,i));return A.from(Gh.tieredData(s,d,r.expansions))},L_=e=>!ve(e,"items"),z_="data-value",V_=(e,t,o,n)=>V(o,(o=>L_(o)?{type:"togglemenuitem",text:o.text,value:o.value,active:o.value===n,onAction:()=>{yu.setValue(e,o.value),Nr(e,Sk,{name:t}),ih.focus(e)}}:{type:"nestedmenuitem",text:o.text,getSubmenuItems:()=>V_(e,t,o.items,n)})),H_=(e,t)=>re(e,(e=>L_(e)?Ce(e.value===t,e):H_(e.items,t))),P_=wm({name:"HtmlSelect",configFields:[ss("options"),xu("selectBehaviours",[ih,yu]),ws("selectClasses",[]),ws("selectAttributes",{}),gs("data")],factory:(e,t)=>{const o=V(e.options,(e=>({dom:{tag:"option",value:e.value,innerHtml:e.text}}))),n=e.data.map((e=>Bs("initialValue",e))).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:o,behaviours:Su(e.selectBehaviours,[ih.config({}),yu.config({store:{mode:"manual",getValue:e=>Ja(e.element),setValue:(t,o)=>{const n=oe(e.options);G(e.options,(e=>e.value===o)).isSome()?Za(t.element,o):-1===t.element.dom.selectedIndex&&""===o&&n.each((e=>Za(t.element,e.value)))},...n}})])}}}),U_=x([ws("field1Name","field1"),ws("field2Name","field2"),zi("onLockedChange"),Fi(["lockClass"]),ws("locked",!1),ku("coupledFieldBehaviours",[_m,yu])]),W_=(e,t)=>qu({factory:gk,name:e,overrides:e=>({fieldBehaviours:El([oh("coupled-input-behaviour",[jr(er(),(o=>{((e,t,o)=>im(e,t,o).bind(_m.getCurrent))(o,e,t).each((t=>{im(o,e,"lock").each((n=>{hh.isOn(n)&&e.onLockedChange(o,t,n)}))}))}))])])})}),j_=x([W_("field1","field2"),W_("field2","field1"),qu({factory:Yh,schema:[ss("dom")],name:"lock",overrides:e=>({buttonBehaviours:El([hh.config({selected:e.locked,toggleClass:e.markers.lockClass,aria:{mode:"pressed"}})])})})]),G_=Sm({name:"FormCoupledInputs",configFields:U_(),partFields:j_(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Cu(e.coupledFieldBehaviours,[_m.config({find:A.some}),yu.config({store:{mode:"manual",getValue:t=>{const o=mm(t,e,["field1","field2"]);return{[e.field1Name]:yu.getValue(o.field1()),[e.field2Name]:yu.getValue(o.field2())}},setValue:(t,o)=>{const n=mm(t,e,["field1","field2"]);ye(o,e.field1Name)&&yu.setValue(n.field1(),o[e.field1Name]),ye(o,e.field2Name)&&yu.setValue(n.field2(),o[e.field2Name])}}})]),apis:{getField1:t=>im(t,e,"field1"),getField2:t=>im(t,e,"field2"),getLock:t=>im(t,e,"lock")}}),apis:{getField1:(e,t)=>e.getField1(t),getField2:(e,t)=>e.getField2(t),getLock:(e,t)=>e.getLock(t)}}),$_=e=>{const t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(e);if(null!==t){const e=parseFloat(t[1]),o=t[2];return an.value({value:e,unit:o})}return an.error(e)},q_=(e,t)=>{const o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,in:1},n=e=>ve(o,e);return e.unit===t?A.some(e.value):n(e.unit)&&n(t)?o[e.unit]===o[t]?A.some(e.value):A.some(e.value/o[e.unit]*o[t]):A.none()},Y_=e=>A.none(),X_=(e,t)=>{const o=e.label.map((e=>wk(e,t))),n=[Hm.config({disabled:()=>e.disabled||t.isDisabled()}),_x(),$p.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:e=>(Rr(e,_k),A.some(!0))}),oh("textfield-change",[jr(er(),((t,o)=>{Nr(t,Sk,{name:e.name})})),jr(ur(),((t,o)=>{Nr(t,Sk,{name:e.name})}))]),fk.config({})],s=e.validation.map((e=>Pk.config({getRoot:e=>it(e.element),invalidClass:"tox-invalid",validator:{validate:t=>{const o=yu.getValue(t),n=e.validator(o);return kS(!0===n?an.value(o):an.error(n))},validateOnLoad:e.validateOnLoad}}))).toArray(),r={...e.placeholder.fold(x({}),(e=>({placeholder:t.translate(e)}))),...e.inputMode.fold(x({}),(e=>({inputmode:e})))},a=gk.parts.field({tag:!0===e.multiline?"textarea":"input",...e.data.map((e=>({data:e}))).getOr({}),inputAttributes:r,inputClasses:[e.classname],inputBehaviours:El(q([n,s])),selectOnFocus:!1,factory:Vv}),i=e.multiline?{dom:{tag:"div",classes:["tox-textarea-wrap"]},components:[a]}:a,l=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),c=[Hm.config({disabled:()=>e.disabled||t.isDisabled(),onDisabled:e=>{gk.getField(e).each(Hm.disable)},onEnabled:e=>{gk.getField(e).each(Hm.enable)}}),_x()];return vk(o,i,l,c)},K_=(e,t)=>t.getAnimationRoot.fold((()=>e.element),(t=>t(e))),J_=e=>e.dimension.property,Z_=(e,t)=>e.dimension.getDimension(t),Q_=(e,t)=>{const o=K_(e,t);Xa(o,[t.shrinkingClass,t.growingClass])},eT=(e,t)=>{$a(e.element,t.openClass),ja(e.element,t.closedClass),It(e.element,J_(t),"0px"),Ut(e.element)},tT=(e,t)=>{$a(e.element,t.closedClass),ja(e.element,t.openClass),Pt(e.element,J_(t))},oT=(e,t,o,n)=>{o.setCollapsed(),It(e.element,J_(t),Z_(t,e.element)),Q_(e,t),eT(e,t),t.onStartShrink(e),t.onShrunk(e)},nT=(e,t,o,n)=>{const s=n.getOrThunk((()=>Z_(t,e.element)));o.setCollapsed(),It(e.element,J_(t),s),Ut(e.element);const r=K_(e,t);$a(r,t.growingClass),ja(r,t.shrinkingClass),eT(e,t),t.onStartShrink(e)},sT=(e,t,o)=>{const n=Z_(t,e.element);("0px"===n?oT:nT)(e,t,o,A.some(n))},rT=(e,t,o)=>{const n=K_(e,t),s=qa(n,t.shrinkingClass),r=Z_(t,e.element);tT(e,t);const a=Z_(t,e.element);(s?()=>{It(e.element,J_(t),r),Ut(e.element)}:()=>{eT(e,t)})(),$a(n,t.shrinkingClass),ja(n,t.growingClass),tT(e,t),It(e.element,J_(t),a),o.setExpanded(),t.onStartGrow(e)},aT=(e,t,o)=>{const n=K_(e,t);return!0===qa(n,t.growingClass)},iT=(e,t,o)=>{const n=K_(e,t);return!0===qa(n,t.shrinkingClass)};var lT=Object.freeze({__proto__:null,refresh:(e,t,o)=>{if(o.isExpanded()){Pt(e.element,J_(t));const o=Z_(t,e.element);It(e.element,J_(t),o)}},grow:(e,t,o)=>{o.isExpanded()||rT(e,t,o)},shrink:(e,t,o)=>{o.isExpanded()&&sT(e,t,o)},immediateShrink:(e,t,o)=>{o.isExpanded()&&oT(e,t,o)},hasGrown:(e,t,o)=>o.isExpanded(),hasShrunk:(e,t,o)=>o.isCollapsed(),isGrowing:aT,isShrinking:iT,isTransitioning:(e,t,o)=>aT(e,t)||iT(e,t),toggleGrow:(e,t,o)=>{(o.isExpanded()?sT:rT)(e,t,o)},disableTransitions:Q_,immediateGrow:(e,t,o)=>{o.isExpanded()||(tT(e,t),It(e.element,J_(t),Z_(t,e.element)),Q_(e,t),o.setExpanded(),t.onStartGrow(e),t.onGrown(e))}}),cT=Object.freeze({__proto__:null,exhibit:(e,t,o)=>{const n=t.expanded;return Ma(n?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:Bs(t.dimension.property,"0px")})},events:(e,t)=>Pr([Jr(sr(),((o,n)=>{n.event.raw.propertyName===e.dimension.property&&(Q_(o,e),t.isExpanded()&&Pt(o.element,e.dimension.property),(t.isExpanded()?e.onGrown:e.onShrunk)(o))}))])}),dT=[ss("closedClass"),ss("openClass"),ss("shrinkingClass"),ss("growingClass"),gs("getAnimationRoot"),Ni("onShrunk"),Ni("onStartShrink"),Ni("onGrown"),Ni("onStartGrow"),ws("expanded",!1),rs("dimension",Qn("property",{width:[Hi("property","width"),Hi("getDimension",(e=>Qt(e)+"px"))],height:[Hi("property","height"),Hi("getDimension",(e=>Gt(e)+"px"))]}))];const uT=Ml({fields:dT,name:"sliding",active:cT,apis:lT,state:Object.freeze({__proto__:null,init:e=>{const t=Ms(e.expanded);return Ea({isExpanded:()=>!0===t.get(),isCollapsed:()=>!1===t.get(),setCollapsed:k(t.set,!1),setExpanded:k(t.set,!0),readState:()=>"expanded: "+t.get()})}})}),mT=e=>({isEnabled:()=>!Hm.isDisabled(e),setEnabled:t=>Hm.set(e,!t),setActive:t=>{const o=e.element;t?(ja(o,"tox-tbtn--enabled"),Ot(o,"aria-pressed",!0)):($a(o,"tox-tbtn--enabled"),Mt(o,"aria-pressed"))},isActive:()=>qa(e.element,"tox-tbtn--enabled"),setText:t=>{Nr(e,M_,{text:t})},setIcon:t=>Nr(e,D_,{icon:t})}),gT=(e,t,o,n,s=!0,r)=>B_({text:e.text,icon:e.icon,tooltip:e.tooltip,ariaLabel:e.tooltip,searchable:e.search.isSome(),role:n,fetch:(t,n)=>{const s={pattern:e.search.isSome()?HS(t):""};e.fetch((t=>{n(N_(t,fv.CLOSE_ON_EXECUTE,o,{isHorizontalMenu:!1,search:e.search}))}),s,mT(t))},onSetup:e.onSetup,getApi:mT,columns:1,presets:"normal",classes:[],dropdownBehaviours:[...s?[fk.config({})]:[]]},t,o.shared,r),pT=(e,t,o)=>{const n=e=>n=>{const s=!n.isActive();n.setActive(s),e.storage.set(s),o.shared.getSink().each((o=>{t().getOpt(o).each((t=>{Nl(t.element),Nr(t,Ok,{name:e.name,value:e.storage.get()})}))}))},s=e=>t=>{t.setActive(e.storage.get())};return t=>{t(V(e,(e=>{const t=e.text.fold((()=>({})),(e=>({text:e})));return{type:e.type,active:!1,...t,onAction:n(e),onSetup:s(e)}})))}},hT=e=>({dom:{tag:"span",classes:["tox-tree__label"],attributes:{"aria-label":e}},components:[ai(e)]}),fT=da("leaf-label-event-id"),bT=({leaf:e,onLeafAction:t,visible:o,treeId:n,selectedId:s,backstage:r})=>{const a=e.menu.map((e=>gT(e,"tox-mbtn",r,A.none(),o))),i=[hT(e.title)];return a.each((e=>i.push(e))),Yh.sketch({dom:{tag:"div",classes:["tox-tree--leaf__label","tox-trbtn"].concat(o?["tox-tree--leaf__label--visible"]:[])},components:i,role:"treeitem",action:o=>{t(e.id),o.getSystem().broadcastOn([`update-active-item-${n}`],{value:e.id})},eventOrder:{[Zs()]:[fT,"keying"]},buttonBehaviours:El([...o?[fk.config({})]:[],hh.config({toggleClass:"tox-trbtn--enabled",toggleOnExecute:!1,aria:{mode:"selected"}}),Fl.config({channels:{[`update-active-item-${n}`]:{onReceive:(t,o)=>{(o.value===e.id?hh.on:hh.off)(t)}}}}),oh(fT,[Zr(((t,o)=>{s.each((o=>{(o===e.id?hh.on:hh.off)(t)}))})),jr(Zs(),((e,t)=>{const o="ArrowLeft"===t.event.raw.code,n="ArrowRight"===t.event.raw.code;o?(bi(e.element,".tox-tree--directory").each((t=>{e.getSystem().getByDom(t).each((e=>{vi(t,".tox-tree--directory__label").each((t=>{e.getSystem().getByDom(t).each(ih.focus)}))}))})),t.stop()):n&&t.stop()}))])])})},vT=da("directory-label-event-id"),yT=({directory:e,visible:t,noChildren:o,backstage:n})=>{const s=e.menu.map((e=>gT(e,"tox-mbtn",n,A.none()))),r=[{dom:{tag:"div",classes:["tox-chevron"]},components:[(a="chevron-right",i=n.shared.providers.icons,((e,t,o)=>nb(e,{tag:"span",classes:["tox-tree__icon-wrap","tox-icon"],behaviours:[]},t))(a,i))]},hT(e.title)];var a,i;s.each((e=>{r.push(e)}));const l=t=>{bi(t.element,".tox-tree--directory").each((o=>{t.getSystem().getByDom(o).each((o=>{const n=!hh.isOn(o);hh.toggle(o),Nr(t,"expand-tree-node",{expanded:n,node:e.id})}))}))};return Yh.sketch({dom:{tag:"div",classes:["tox-tree--directory__label","tox-trbtn"].concat(t?["tox-tree--directory__label--visible"]:[])},components:r,action:l,eventOrder:{[Zs()]:[vT,"keying"]},buttonBehaviours:El([...t?[fk.config({})]:[],oh(vT,[jr(Zs(),((e,t)=>{const n="ArrowRight"===t.event.raw.code,s="ArrowLeft"===t.event.raw.code;n&&o&&t.stop(),(n||s)&&bi(e.element,".tox-tree--directory").each((o=>{e.getSystem().getByDom(o).each((o=>{!hh.isOn(o)&&n||hh.isOn(o)&&s?(l(e),t.stop()):s&&!hh.isOn(o)&&(bi(o.element,".tox-tree--directory").each((e=>{vi(e,".tox-tree--directory__label").each((e=>{o.getSystem().getByDom(e).each(ih.focus)}))})),t.stop())}))}))}))])])})},xT=({children:e,onLeafAction:t,visible:o,treeId:n,expandedIds:s,selectedId:r,backstage:a})=>({dom:{tag:"div",classes:["tox-tree--directory__children"]},components:e.map((e=>"leaf"===e.type?bT({leaf:e,selectedId:r,onLeafAction:t,visible:o,treeId:n,backstage:a}):ST({directory:e,expandedIds:s,selectedId:r,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:a}))),behaviours:El([uT.config({dimension:{property:"height"},closedClass:"tox-tree--directory__children--closed",openClass:"tox-tree--directory__children--open",growingClass:"tox-tree--directory__children--growing",shrinkingClass:"tox-tree--directory__children--shrinking",expanded:o}),th.config({})])}),wT=da("directory-event-id"),ST=({directory:e,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:s,expandedIds:r,selectedId:a})=>{const{children:i}=e,l=Ms(r),c=r.includes(e.id);return{dom:{tag:"div",classes:["tox-tree--directory"],attributes:{role:"treeitem"}},components:[yT({directory:e,visible:o,noChildren:0===e.children.length,backstage:s}),xT({children:i,expandedIds:r,selectedId:a,onLeafAction:t,visible:c,treeId:n,backstage:s})],behaviours:El([oh(wT,[Zr(((e,t)=>{hh.set(e,c)})),jr("expand-tree-node",((e,t)=>{const{expanded:o,node:n}=t.event;l.set(o?[...l.get(),n]:l.get().filter((e=>e!==n)))}))]),hh.config({...e.children.length>0?{aria:{mode:"expanded"}}:{},toggleClass:"tox-tree--directory--expanded",onToggled:(e,o)=>{const r=e.components()[1],c=(d=o,i.map((e=>"leaf"===e.type?bT({leaf:e,selectedId:a,onLeafAction:t,visible:d,treeId:n,backstage:s}):ST({directory:e,expandedIds:l.get(),selectedId:a,onLeafAction:t,labelTabstopping:d,treeId:n,backstage:s}))));var d;o?uT.grow(r):uT.shrink(r),th.set(r,c)}})])}},kT=da("tree-event-id");var CT=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.stream.streams.setup(e,t);return Pr([jr(e.event,o),Qr((()=>t.cancel()))].concat(e.cancelEvent.map((e=>[jr(e,(()=>t.cancel()))])).getOr([])))}});const OT=e=>{const t=Ms(null);return Ea({readState:()=>({timer:null!==t.get()?"set":"unset"}),setTimer:e=>{t.set(e)},cancel:()=>{const e=t.get();null!==e&&e.cancel()}})};var _T=Object.freeze({__proto__:null,throttle:OT,init:e=>e.stream.streams.state(e)}),TT=[rs("stream",Qn("mode",{throttle:[ss("delay"),ws("stopEvent",!0),Hi("streams",{setup:(e,t)=>{const o=e.stream,n=QO(e.onStream,o.delay);return t.setTimer(n),(e,t)=>{n.throttle(e,t),o.stopEvent&&t.stop()}},state:OT})]})),ws("event","input"),gs("cancelEvent"),zi("onStream")];const ET=Ml({fields:TT,name:"streaming",active:CT,state:_T}),AT=(e,t,o)=>{const n=yu.getValue(o);yu.setValue(t,n),DT(t)},MT=(e,t)=>{const o=e.element,n=Ja(o),s=o.dom;"number"!==Tt(o,"type")&&t(s,n)},DT=e=>{MT(e,((e,t)=>e.setSelectionRange(t.length,t.length)))},BT=x("alloy.typeahead.itemexecute"),IT=x([gs("lazySink"),ss("fetch"),ws("minChars",5),ws("responseTime",1e3),Ni("onOpen"),ws("getHotspot",A.some),ws("getAnchorOverrides",x({})),ws("layouts",A.none()),ws("eventOrder",{}),As("model",{},[ws("getDisplayText",(e=>void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.value)),ws("selectsOver",!0),ws("populateFromBrowse",!0)]),Ni("onSetValue"),Li("onExecute"),Ni("onItemExecute"),ws("inputClasses",[]),ws("inputAttributes",{}),ws("inputStyles",{}),ws("matchWidth",!0),ws("useMinWidth",!1),ws("dismissOnBlur",!0),Fi(["openClass"]),gs("initialData"),xu("typeaheadBehaviours",[ih,yu,ET,$p,hh,bS]),os("lazyTypeaheadComp",(()=>Ms(A.none))),os("previewing",(()=>Ms(!0)))].concat(Rv()).concat(FS())),FT=x([Yu({schema:[Ii()],name:"menu",overrides:e=>({fakeFocus:!0,onHighlightItem:(t,o,n)=>{e.previewing.get()?e.lazyTypeaheadComp.get().each((t=>{((e,t,o)=>{if(e.selectsOver){const n=yu.getValue(t),s=e.getDisplayText(n),r=yu.getValue(o);return 0===e.getDisplayText(r).indexOf(s)?A.some((()=>{AT(0,t,o),((e,t)=>{MT(e,((e,o)=>e.setSelectionRange(t,o.length)))})(t,s.length)})):A.none()}return A.none()})(e.model,t,n).fold((()=>{e.model.selectsOver?(Km.dehighlight(o,n),e.previewing.set(!0)):e.previewing.set(!1)}),(t=>{t(),e.previewing.set(!1)}))})):e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&AT(e.model,t,n),Et(n.element,"id").each((e=>Ot(t.element,"aria-activedescendant",e)))}))},onExecute:(t,o)=>e.lazyTypeaheadComp.get().map((e=>(Nr(e,BT(),{item:o}),!0))),onHover:(t,o)=>{e.previewing.set(!1),e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&AT(e.model,t,o)}))}})})]),RT=Sm({name:"Typeahead",configFields:IT(),partFields:FT(),factory:(e,t,o,n)=>{const s=(t,o,s)=>{e.previewing.set(!1);const r=bS.getCoupled(t,"sandbox");if(Qd.isOpen(r))_m.getCurrent(r).each((e=>{Km.getHighlighted(e).fold((()=>{s(e)}),(()=>{Hr(r,e.element,"keydown",o)}))}));else{const o=e=>{_m.getCurrent(e).each(s)};TS(e,a(t),t,r,n,o,Wh.HighlightMenuAndItem).get(b)}},r=Nv(e),a=e=>t=>t.map((t=>{const o=fe(t.menus),n=Y(o,(e=>U(e.items,(e=>"item"===e.type))));return yu.getState(e).update(V(n,(e=>e.data))),t})),i=e=>_m.getCurrent(e),l="typeaheadevents",c=[ih.config({}),yu.config({onSetValue:e.onSetValue,store:{mode:"dataset",getDataKey:e=>Ja(e.element),getFallbackEntry:e=>({value:e,meta:{}}),setValue:(t,o)=>{Za(t.element,e.model.getDisplayText(o))},...e.initialData.map((e=>Bs("initialValue",e))).getOr({})}}),ET.config({stream:{mode:"throttle",delay:e.responseTime,stopEvent:!1},onStream:(t,o)=>{const s=bS.getCoupled(t,"sandbox");if(ih.isFocused(t)&&Ja(t.element).length>=e.minChars){const o=i(s).bind((e=>Km.getHighlighted(e).map(yu.getValue)));e.previewing.set(!0);const r=t=>{i(s).each((t=>{o.fold((()=>{e.model.selectsOver&&Km.highlightFirst(t)}),(e=>{Km.highlightBy(t,(t=>yu.getValue(t).value===e.value)),Km.getHighlighted(t).orThunk((()=>(Km.highlightFirst(t),A.none())))}))}))};TS(e,a(t),t,s,n,r,Wh.HighlightJustMenu).get(b)}},cancelEvent:vr()}),$p.config({mode:"special",onDown:(e,t)=>(s(e,t,Km.highlightFirst),A.some(!0)),onEscape:e=>{const t=bS.getCoupled(e,"sandbox");return Qd.isOpen(t)?(Qd.close(t),A.some(!0)):A.none()},onUp:(e,t)=>(s(e,t,Km.highlightLast),A.some(!0)),onEnter:t=>{const o=bS.getCoupled(t,"sandbox"),n=Qd.isOpen(o);if(n&&!e.previewing.get())return i(o).bind((e=>Km.getHighlighted(e))).map((e=>(Nr(t,BT(),{item:e}),!0)));{const s=yu.getValue(t);return Rr(t,vr()),e.onExecute(o,t,s),n&&Qd.close(o),A.some(!0)}}}),hh.config({toggleClass:e.markers.openClass,aria:{mode:"expanded"}}),bS.config({others:{sandbox:t=>BS(e,t,{onOpen:()=>hh.on(t),onClose:()=>{e.lazyTypeaheadComp.get().each((e=>Mt(e.element,"aria-activedescendant"))),hh.off(t)}})}}),oh(l,[Zr((t=>{e.lazyTypeaheadComp.set(A.some(t))})),Qr((t=>{e.lazyTypeaheadComp.set(A.none())})),ta((t=>{const o=b;AS(e,a(t),t,n,o,Wh.HighlightMenuAndItem).get(b)})),jr(BT(),((t,o)=>{const n=bS.getCoupled(t,"sandbox");AT(e.model,t,o.event.item),Rr(t,vr()),e.onItemExecute(t,n,o.event.item,yu.getValue(t)),Qd.close(n),DT(t)}))].concat(e.dismissOnBlur?[jr(dr(),(e=>{const t=bS.getCoupled(e,"sandbox");Hl(t.element).isNone()&&Qd.close(t)}))]:[]))],d={[Or()]:[yu.name(),ET.name(),l],...e.eventOrder};return{uid:e.uid,dom:zv(vn(e,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:{...r,...Su(e.typeaheadBehaviours,c)},eventOrder:d}}}),NT=e=>({...e,toCached:()=>NT(e.toCached()),bindFuture:t=>NT(e.bind((e=>e.fold((e=>kS(an.error(e))),(e=>t(e)))))),bindResult:t=>NT(e.map((e=>e.bind(t)))),mapResult:t=>NT(e.map((e=>e.map(t)))),mapError:t=>NT(e.map((e=>e.mapError(t)))),foldResult:(t,o)=>e.map((e=>e.fold(t,o))),withTimeout:(t,o)=>NT(SS((n=>{let s=!1;const r=setTimeout((()=>{s=!0,n(an.error(o()))}),t);e.get((e=>{s||(clearTimeout(r),n(e))}))})))}),LT=e=>NT(SS(e)),zT=(e,t,o=[],n,s,r,a)=>{const i=t.fold((()=>({})),(e=>({action:e}))),l={buttonBehaviours:El([Tx((()=>!e.enabled||a.isDisabled())),_x(),fk.config({}),...r.map((e=>wx.config(a.tooltips.getConfig({tooltipText:a.translate(e)})))).toArray(),oh("button press",[Wr("click"),Wr("mousedown")])].concat(o)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]},...i},c=vn(l,{dom:n});return vn(c,{components:s})},VT=(e,t,o,n=[],s)=>{const r={tag:"button",classes:["tox-tbtn"],attributes:{...e.tooltip.map((e=>({"aria-label":o.translate(e)}))).getOr({}),"data-mce-name":s}},a=e.icon.map((e=>T_(e,o.icons))),i=Rx([a]);return zT(e,t,n,r,i,e.tooltip,o)},HT=e=>{switch(e){case"primary":return["tox-button"];case"toolbar":return["tox-tbtn"];default:return["tox-button","tox-button--secondary"]}},PT=(e,t,o,n=[],s=[])=>{const r=o.translate(e.text),a=e.icon.map((e=>T_(e,o.icons))),i=[a.getOrThunk((()=>ai(r)))],l=e.buttonType.getOr(e.primary||e.borderless?"primary":"secondary"),c={tag:"button",classes:[...HT(l),...a.isSome()?["tox-button--icon"]:[],...e.borderless?["tox-button--naked"]:[],...s],attributes:{"aria-label":r,"data-mce-name":e.text}},d=e.icon.map(x(r));return zT(e,t,n,c,i,d,o)},UT=(e,t,o,n=[],s=[])=>{const r=PT(e,A.some(t),o,n,s);return Yh.sketch(r)},WT=(e,t)=>o=>{"custom"===t?Nr(o,Ok,{name:e,value:{}}):"submit"===t?Rr(o,_k):"cancel"===t?Rr(o,Ck):console.error("Unknown button type: ",t)},jT=(e,t,o)=>{if(((e,t)=>"menu"===t)(0,t)){const t=()=>r,n=e,s={...e,type:"menubutton",search:A.none(),onSetup:t=>(t.setEnabled(e.enabled),b),fetch:pT(n.items,t,o)},r=Kh(gT(s,"tox-tbtn",o,A.none(),!0,e.text.or(e.tooltip).getOrUndefined()));return r.asSpec()}if(((e,t)=>"custom"===t||"cancel"===t||"submit"===t)(0,t)){const n=WT(e.name,t),s={...e,borderless:!1};return UT(s,n,o.shared.providers,[])}if(((e,t)=>"togglebutton"===t)(0,t))return((e,t,o)=>{var n,s;const r=e.icon.map((e=>E_(e,t.icons))).map(Kh),a=e.buttonType.getOr(e.primary?"primary":"secondary"),i={...e,name:null!==(n=e.name)&&void 0!==n?n:"",primary:"primary"===a,tooltip:e.tooltip,enabled:null!==(s=e.enabled)&&void 0!==s&&s,borderless:!1},l=i.tooltip.or(e.text).map((e=>({"aria-label":t.translate(e)}))).getOr({}),c=HT(null!=a?a:"secondary"),d=e.icon.isSome()&&e.text.isSome(),u={tag:"button",classes:[...c.concat(e.icon.isSome()?["tox-button--icon"]:[]),...e.active?["tox-button--enabled"]:[],...d?["tox-button--icon-and-text"]:[]],attributes:{...l,...g(o)?{"data-mce-name":o}:{}}},m=t.translate(e.text.getOr("")),p=ai(m),h=[...Rx([r.map((e=>e.asSpec()))]),...e.text.isSome()?[p]:[]],f=zT(i,A.some((o=>{Nr(o,Ok,{name:e.name,value:{setIcon:e=>{r.map((n=>n.getOpt(o).each((o=>{th.set(o,[E_(e,t.icons)])}))))}}})})),[],u,h,e.tooltip,t);return Yh.sketch(f)})(e,o.shared.providers,e.text.or(e.tooltip).getOrUndefined());throw console.error("Unknown footer button type: ",t),new Error("Unknown footer button type")},GT={type:"separator"},$T=e=>({type:"menuitem",value:e.url,text:e.title,meta:{attach:e.attach},onAction:b}),qT=(e,t)=>({type:"menuitem",value:t,text:e,meta:{attach:void 0},onAction:b}),YT=(e,t)=>(e=>V(e,$T))(((e,t)=>U(t,(t=>t.type===e)))(e,t)),XT=e=>YT("header",e.targets),KT=e=>YT("anchor",e.targets),JT=e=>A.from(e.anchorTop).map((e=>qT("<top>",e))).toArray(),ZT=e=>A.from(e.anchorBottom).map((e=>qT("<bottom>",e))).toArray(),QT=(e,t)=>{const o=e.toLowerCase();return U(t,(e=>{var t;const n=void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.text,s=null!==(t=e.value)&&void 0!==t?t:"";return Te(n.toLowerCase(),o)||Te(s.toLowerCase(),o)}))},eE=da("aria-invalid"),tE=(e,t)=>{e.dom.checked=t},oE=e=>e.dom.checked,nE=e=>(t,o,n,s)=>be(o,"name").fold((()=>e(o,s,A.none())),(r=>t.field(r,e(o,s,be(n,r))))),sE={bar:nE(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:V(e.items,t.interpreter)}))(e,t.shared))),collection:nE(((e,t,o)=>Dk(e,t.shared.providers,o))),alertbanner:nE(((e,t)=>((e,t)=>{const o=eb(e.icon,t.icons);return ck.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in",`tox-notification--${e.level}`]},components:[{dom:{tag:"div",classes:["tox-notification__icon"],innerHtml:e.url?void 0:o},components:e.url?[Yh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:o,attributes:{title:t.translate(e.iconTooltip)}},action:t=>Nr(t,Ok,{name:"alert-banner",value:e.url}),buttonBehaviours:El([tb()])})]:void 0},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:t.translate(e.text)}}]})})(e,t.shared.providers))),input:nE(((e,t,o)=>((e,t,o)=>X_({name:e.name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:!e.enabled,classname:"tox-textfield",validation:A.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),textarea:nE(((e,t,o)=>((e,t,o)=>X_({name:e.name,multiline:!0,label:e.label,inputMode:A.none(),placeholder:e.placeholder,flex:!0,disabled:!e.enabled,classname:"tox-textarea",validation:A.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),label:nE(((e,t)=>((e,t)=>{const o="tox-label";return{dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"label",classes:[o,..."center"===e.align?[`${o}--center`]:[],..."end"===e.align?[`${o}--end`]:[]]},components:[ai(t.providers.translate(e.label))]},...V(e.items,t.interpreter)],behaviours:El([UO(),th.config({}),(n=A.none(),qO(n,oa,na)),$p.config({mode:"acyclic"})])};var n})(e,t.shared))),iframe:(DA=(e,t,o)=>((e,t,o)=>{const n="tox-dialog__iframe",s=e.transparent?[]:[`${n}--opaque`],r=e.border?["tox-navobj-bordered"]:[],a={...e.label.map((e=>({title:e}))).getOr({}),...o.map((e=>({srcdoc:e}))).getOr({}),...e.sandboxed?{sandbox:"allow-scripts allow-same-origin"}:{}},i=((e,t)=>{const o=Ms(e.getOr(""));return{getValue:e=>o.get(),setValue:(e,n)=>{if(o.get()!==n){const o=e.element,s=()=>Ot(o,"srcdoc",n);t?w_.fold(x(x_),(e=>e.throttle))(o,n,s):s()}o.set(n)}}})(o,e.streamContent),l=e.label.map((e=>wk(e,t))),c=gk.parts.field({factory:{sketch:e=>n_(A.from(r),{uid:e.uid,dom:{tag:"iframe",attributes:a,classes:[n,...s]},behaviours:El([fk.config({}),ih.config({}),$O(o,i.getValue,i.setValue),Fl.config({channels:{[m_]:{onReceive:(e,t)=>{t.newFocus.each((t=>{it(e.element).each((o=>{(et(e.element,t)?ja:$a)(o,"tox-navobj-bordered-focus")}))}))}}}})])})}});return vk(l,c,["tox-form__group--stretched"],[])})(e,t.shared.providers,o),(e,t,o,n)=>{const s=vn(t,{source:"dynamic"});return nE(DA)(e,s,o,n)}),button:nE(((e,t)=>((e,t)=>{const o=WT(e.name,"custom");return n=A.none(),s=gk.parts.field({factory:Yh,...PT(e,A.some(o),t,[YO(""),UO()])}),vk(n,s,[],[]);var n,s})(e,t.shared.providers))),checkbox:nE(((e,t,o)=>((e,t,o)=>{const n=e=>(e.element.dom.click(),A.some(!0)),s=gk.parts.field({factory:{sketch:w},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:El([UO(),Hm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{it(e.element).each((e=>ja(e,"tox-checkbox--disabled")))},onEnabled:e=>{it(e.element).each((e=>$a(e,"tox-checkbox--disabled")))}}),fk.config({}),ih.config({}),qO(o,oE,tE),$p.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),oh("checkbox-events",[jr(tr(),((t,o)=>{Nr(t,Sk,{name:e.name})}))])])}),r=gk.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"]},components:[ai(t.translate(e.label))],behaviours:El([Uk.config({})])}),a=e=>nb("checked"===e?"selected":"unselected",{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+e]},t.icons),i=Kh({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[a("checked"),a("unchecked")]});return gk.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[s,i.asSpec(),r],fieldBehaviours:El([Hm.config({disabled:()=>!e.enabled||t.isDisabled()}),_x()])})})(e,t.shared.providers,o))),colorinput:nE(((e,t,o)=>((e,t,o,n)=>{const s=gk.parts.field({factory:Vv,inputClasses:["tox-textfield"],data:n,onSetValue:e=>Pk.run(e).get(b),inputBehaviours:El([Hm.config({disabled:t.providers.isDisabled}),_x(),fk.config({}),Pk.config({invalidClass:"tox-textbox-field-invalid",getRoot:e=>it(e.element),notify:{onValid:e=>{const t=yu.getValue(e);Nr(e,Wk,{color:t})}},validator:{validateOnLoad:!1,validate:e=>{const t=yu.getValue(e);if(0===t.length)return kS(an.value(!0));{const e=Ne("span");It(e,"background-color",t);const o=zt(e,"background-color").fold((()=>an.error("blah")),(e=>an.value(t)));return kS(o)}}}})]),selectOnFocus:!1}),r=e.label.map((e=>wk(e,t.providers))),a=(e,t)=>{Nr(e,jk,{value:t})},i=Kh(((e,t)=>LS.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:El([Tx(t.providers.isDisabled),_x(),Uk.config({}),fk.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:t.getSink,fetch:o=>SS((t=>e.fetch(t))).map((n=>A.from(GS(vn(sS(da("menu-value"),n,(t=>{e.onItemAction(o,t)}),e.columns,e.presets,fv.CLOSE_ON_EXECUTE,T,t.providers),{movement:aS(e.columns,e.presets)}))))),parts:{menu:Fv(0,0,e.presets)}}))({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:{onRtl:()=>[dl,cl,pl],onLtr:()=>[cl,dl,pl]},components:[],fetch:Kw(o.getColors(e.storageKey),e.storageKey,o.hasCustomColors()),columns:o.getColorCols(e.storageKey),presets:"color",onItemAction:(t,n)=>{i.getOpt(t).each((t=>{"custom"===n?o.colorPicker((o=>{o.fold((()=>Rr(t,Gk)),(o=>{a(t,o),Aw(e.storageKey,o)}))}),"#ffffff"):a(t,"remove"===n?"":n)}))}},t));return gk.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:r.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[s,i.asSpec()]}]),fieldBehaviours:El([oh("form-field-events",[jr(Wk,((t,o)=>{i.getOpt(t).each((e=>{It(e.element,"background-color",o.event.color)})),Nr(t,Sk,{name:e.name})})),jr(jk,((e,t)=>{gk.getField(e).each((o=>{yu.setValue(o,t.event.value),_m.getCurrent(e).each(ih.focus)}))})),jr(Gk,((e,t)=>{gk.getField(e).each((t=>{_m.getCurrent(e).each(ih.focus)}))}))])])})})(e,t.shared,t.colorinput,o))),colorpicker:nE(((e,t,o)=>((e,t,o)=>{const n=e=>"tox-"+e,s=PO((e=>t=>r(t)?e.translate(XO[t]):e.translate(t))(t),n),a=Kh(s.sketch({dom:{tag:"div",classes:[n("color-picker-container")],attributes:{role:"presentation"}},onValidHex:e=>{Nr(e,Ok,{name:"hex-valid",value:!0})},onInvalidHex:e=>{Nr(e,Ok,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[a.asSpec()],behaviours:El([$O(o,(e=>{const t=a.get(e);return _m.getCurrent(t).bind((e=>yu.getValue(e).hex)).map((e=>"#"+_e(e,"#"))).getOr("")}),((e,t)=>{const o=A.from(/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t)).bind((e=>te(e,1))),n=a.get(e);_m.getCurrent(n).fold((()=>{console.log("Can not find form")}),(e=>{yu.setValue(e,{hex:o.getOr("")}),RO.getField(e,"hex").each((e=>{Rr(e,er())}))}))})),UO()])}})(0,t.shared.providers,o))),dropzone:nE(((e,t,o)=>((e,t,o)=>{const n=(e,t)=>{t.stop()},s=e=>(t,o)=>{H(e,(e=>{e(t,o)}))},r=(e,t)=>{var o;if(!Hm.isDisabled(e)){const n=t.event.raw;i(e,null===(o=n.dataTransfer)||void 0===o?void 0:o.files)}},a=(e,t)=>{const o=t.event.raw.target;i(e,o.files)},i=(o,n)=>{n&&(yu.setValue(o,((e,t)=>{const o=ZO.explode(t.getOption("images_file_types"));return U(se(e),(e=>N(o,(t=>Ae(e.name.toLowerCase(),`.${t.toLowerCase()}`)))))})(n,t)),Nr(o,Sk,{name:e.name}))},l=Kh({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:El([oh("input-file-events",[Xr(or()),Xr(hr())])])}),c=e.label.map((e=>wk(e,t))),d=gk.parts.field({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:El([YO(o.getOr([])),UO(),Hm.config({}),hh.config({toggleClass:"dragenter",toggleOnExecute:!1}),oh("dropzone-events",[jr("dragenter",s([n,hh.toggle])),jr("dragleave",s([n,hh.toggle])),jr("dragover",n),jr("drop",s([n,r])),jr(tr(),a)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p"},components:[ai(t.translate("Drop an image here"))]},Yh.sketch({dom:{tag:"button",styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[ai(t.translate("Browse for an image")),l.asSpec()],action:e=>{l.get(e).element.dom.click()},buttonBehaviours:El([fk.config({}),Tx(t.isDisabled),_x()])})]}]})}});return vk(c,d,["tox-form__group--stretched"],[])})(e,t.shared.providers,o))),grid:nE(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-form__grid",`tox-form__grid--${e.columns}col`]},components:V(e.items,t.interpreter)}))(e,t.shared))),listbox:nE(((e,t,o)=>((e,t,o)=>{const n=t.shared.providers,s=o.bind((t=>H_(e.items,t))).orThunk((()=>oe(e.items).filter(L_))),r=e.label.map((e=>wk(e,n))),a=gk.parts.field({dom:{},factory:{sketch:o=>B_({uid:o.uid,text:s.map((e=>e.text)),icon:A.none(),tooltip:A.none(),role:A.none(),ariaLabel:e.label,fetch:(o,n)=>{const s=V_(o,e.name,e.items,yu.getValue(o));n(N_(s,fv.CLOSE_ON_EXECUTE,t,{isHorizontalMenu:!1,search:A.none()}))},onSetup:x(b),getApi:x({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[fk.config({}),$O(s.map((e=>e.value)),(e=>Tt(e.element,z_)),((t,o)=>{H_(e.items,o).each((e=>{Ot(t.element,z_,e.value),Nr(t,M_,{text:e.text})}))}))]},"tox-listbox",t.shared)}}),i={dom:{tag:"div",classes:["tox-listboxfield"]},components:[a]};return gk.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([r.toArray(),[i]]),fieldBehaviours:El([Hm.config({disabled:x(!e.enabled),onDisabled:e=>{gk.getField(e).each(Hm.disable)},onEnabled:e=>{gk.getField(e).each(Hm.enable)}})])})})(e,t,o))),selectbox:nE(((e,t,o)=>((e,t,o)=>{const n=V(e.items,(e=>({text:t.translate(e.text),value:e.value}))),s=e.label.map((e=>wk(e,t))),r=gk.parts.field({dom:{},...o.map((e=>({data:e}))).getOr({}),selectAttributes:{size:e.size},options:n,factory:P_,selectBehaviours:El([Hm.config({disabled:()=>!e.enabled||t.isDisabled()}),fk.config({}),oh("selectbox-change",[jr(tr(),((t,o)=>{Nr(t,Sk,{name:e.name})}))])])}),a=e.size>1?A.none():A.some(nb("chevron-down",{tag:"div",classes:["tox-selectfield__icon-js"]},t.icons)),i={dom:{tag:"div",classes:["tox-selectfield"]},components:q([[r],a.toArray()])};return gk.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([s.toArray(),[i]]),fieldBehaviours:El([Hm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{gk.getField(e).each(Hm.disable)},onEnabled:e=>{gk.getField(e).each(Hm.enable)}}),_x()])})})(e,t.shared.providers,o))),sizeinput:nE(((e,t)=>((e,t)=>{let o=Y_;const n=da("ratio-event"),s=e=>nb(e,{tag:"span",classes:["tox-icon","tox-lock-icon__"+e]},t.icons),r=e.label.getOr("Constrain proportions"),a=t.translate(r),i=G_.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{"aria-label":a,"data-mce-name":r}},components:[s("lock"),s("unlock")],buttonBehaviours:El([Hm.config({disabled:()=>!e.enabled||t.isDisabled()}),_x(),fk.config({}),wx.config(t.tooltips.getConfig({tooltipText:a}))])}),l=e=>({dom:{tag:"div",classes:["tox-form__group"]},components:e}),c=o=>gk.parts.field({factory:Vv,inputClasses:["tox-textfield"],inputBehaviours:El([Hm.config({disabled:()=>!e.enabled||t.isDisabled()}),_x(),fk.config({}),oh("size-input-events",[jr(Ks(),((e,t)=>{Nr(e,n,{isField1:o})})),jr(tr(),((t,o)=>{Nr(t,Sk,{name:e.name})}))])]),selectOnFocus:!1}),d=e=>({dom:{tag:"label",classes:["tox-label"]},components:[ai(t.translate(e))]}),u=G_.parts.field1(l([gk.parts.label(d("Width")),c(!0)])),m=G_.parts.field2(l([gk.parts.label(d("Height")),c(!1)]));return G_.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[u,m,l([d("\xa0"),i])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:(e,t,n)=>{$_(yu.getValue(e)).each((e=>{o(e).each((e=>{yu.setValue(t,(e=>{const t={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,in:4,"%":4};let o=e.value.toFixed((n=e.unit)in t?t[n]:1);var n;return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+e.unit})(e))}))}))},coupledFieldBehaviours:El([Hm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{G_.getField1(e).bind(gk.getField).each(Hm.disable),G_.getField2(e).bind(gk.getField).each(Hm.disable),G_.getLock(e).each(Hm.disable)},onEnabled:e=>{G_.getField1(e).bind(gk.getField).each(Hm.enable),G_.getField2(e).bind(gk.getField).each(Hm.enable),G_.getLock(e).each(Hm.enable)}}),_x(),oh("size-input-events2",[jr(n,((e,t)=>{const n=t.event.isField1,s=n?G_.getField1(e):G_.getField2(e),r=n?G_.getField2(e):G_.getField1(e),a=s.map(yu.getValue).getOr(""),i=r.map(yu.getValue).getOr("");o=((e,t)=>{const o=$_(e).toOptional(),n=$_(t).toOptional();return Se(o,n,((e,t)=>q_(e,t.unit).map((e=>t.value/e)).map((e=>{return o=e,n=t.unit,e=>q_(e,n).map((e=>({value:e*o,unit:n})));var o,n})).getOr(Y_))).getOr(Y_)})(a,i)}))])])})})(e,t.shared.providers))),slider:nE(((e,t,o)=>((e,t,o)=>{const n=TO.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[ai(t.translate(e.label))]}),s=TO.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),r=TO.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return TO.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e.min,maxX:e.max,getInitialValue:x(o.getOrThunk((()=>(Math.abs(e.max)-Math.abs(e.min))/2)))},components:[n,s,r],sliderBehaviours:El([UO(),ih.config({})]),onChoose:(t,o,n)=>{Nr(t,Sk,{name:e.name,value:n})}})})(e,t.shared.providers,o))),urlinput:nE(((e,t,o)=>((e,t,o,n)=>{const s=t.shared.providers,r=t=>{const n=yu.getValue(t);o.addToHistory(n.value,e.filetype)},a={...n.map((e=>({initialData:e}))).getOr({}),dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":eE,type:"url"},minChars:0,responseTime:0,fetch:n=>{const s=((e,t,o)=>{var n,s;const r=yu.getValue(t),a=null!==(s=null===(n=null==r?void 0:r.meta)||void 0===n?void 0:n.text)&&void 0!==s?s:r.value;return o.getLinkInformation().fold((()=>[]),(t=>{const n=QT(a,(e=>V(e,(e=>qT(e,e))))(o.getHistory(e)));return"file"===e?(s=[n,QT(a,XT(t)),QT(a,q([JT(t),KT(t),ZT(t)]))],j(s,((e,t)=>0===e.length||0===t.length?e.concat(t):e.concat(GT,t)),[])):n;var s}))})(e.filetype,n,o),r=N_(s,fv.BUBBLE_TO_SANDBOX,t,{isHorizontalMenu:!1,search:A.none()});return kS(r)},getHotspot:e=>g.getOpt(e),onSetValue:(e,t)=>{e.hasConfigured(Pk)&&Pk.run(e).get(b)},typeaheadBehaviours:El([...o.getValidationHandler().map((t=>Pk.config({getRoot:e=>it(e.element),invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:(e,t)=>{c.getOpt(e).each((e=>{Ot(e.element,"title",s.translate(t))}))}},validator:{validate:o=>{const n=yu.getValue(o);return LT((o=>{t({type:e.filetype,url:n.value},(e=>{if("invalid"===e.status){const t=an.error(e.message);o(t)}else{const t=an.value(e.message);o(t)}}))}))},validateOnLoad:!1}}))).toArray(),Hm.config({disabled:()=>!e.enabled||s.isDisabled()}),fk.config({}),oh("urlinput-events",[jr(er(),(t=>{const o=Ja(t.element),n=o.trim();n!==o&&Za(t.element,n),"file"===e.filetype&&Nr(t,Sk,{name:e.name})})),jr(tr(),(t=>{Nr(t,Sk,{name:e.name}),r(t)})),jr(ur(),(t=>{Nr(t,Sk,{name:e.name}),r(t)}))])]),eventOrder:{[er()]:["streaming","urlinput-events","invalidating"]},model:{getDisplayText:e=>e.value,selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:t.shared.getSink,parts:{menu:Fv(0,0,"normal")},onExecute:(e,t,o)=>{Nr(t,_k,{})},onItemExecute:(t,o,n,s)=>{r(t),Nr(t,Sk,{name:e.name})}},i=gk.parts.field({...a,factory:RT}),l=e.label.map((e=>wk(e,s))),c=Kh(((e,t,o=e,n=e)=>nb(o,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:{title:s.translate(n),"aria-live":"polite",...t.fold((()=>({})),(e=>({id:e})))}},s.icons))("invalid",A.some(eE),"warning")),d=Kh({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[c.asSpec()]}),u=o.getUrlPicker(e.filetype),m=da("browser.url.event"),g=Kh({dom:{tag:"div",classes:["tox-control-wrap"]},components:[i,d.asSpec()],behaviours:El([Hm.config({disabled:()=>!e.enabled||s.isDisabled()})])}),p=Kh(UT({name:e.name,icon:A.some("browse"),text:e.picker_text.or(e.label).getOr(""),enabled:e.enabled,primary:!1,buttonType:A.none(),borderless:!0},(e=>Rr(e,m)),s,[],["tox-browse-url"]));return gk.sketch({dom:xk([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:q([[g.asSpec()],u.map((()=>p.asSpec())).toArray()])}]),fieldBehaviours:El([Hm.config({disabled:()=>!e.enabled||s.isDisabled(),onDisabled:e=>{gk.getField(e).each(Hm.disable),p.getOpt(e).each(Hm.disable)},onEnabled:e=>{gk.getField(e).each(Hm.enable),p.getOpt(e).each(Hm.enable)}}),_x(),oh("url-input-events",[jr(m,(t=>{_m.getCurrent(t).each((o=>{const n=yu.getValue(o),s={fieldname:e.name,...n};u.each((n=>{n(s).get((n=>{yu.setValue(o,n),Nr(t,Sk,{name:e.name})}))}))}))}))])])})})(e,t,t.urlinput,o))),customeditor:nE((e=>{const t=sc(),o=Kh({dom:{tag:e.tag}}),n=sc(),s=!JO(e)&&e.onFocus.isSome()?[ih.config({onFocus:t=>{e.onFocus.each((e=>{e(t.element.dom)}))}}),fk.config({})]:[];return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:El([oh("custom-editor-events",[Zr((s=>{o.getOpt(s).each((o=>{(JO(e)?e.init(o.element.dom):KO.load(e.scriptId,e.scriptUrl).then((t=>t(o.element.dom,e.settings)))).then((e=>{n.on((t=>{e.setValue(t)})),n.clear(),t.set(e)}))}))}))]),$O(A.none(),(()=>t.get().fold((()=>n.get().getOr("")),(e=>e.getValue()))),((e,o)=>{t.get().fold((()=>n.set(o)),(e=>e.setValue(o)))})),UO()].concat(s)),components:[o.asSpec()]}})),htmlpanel:nE(((e,t)=>((e,t)=>"presentation"===e.presets?ck.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html},containerBehaviours:El([wx.config({...t.tooltips.getConfig({tooltipText:"",onShow:e=>{yi(e.element,"[data-mce-tooltip]:hover").orThunk((()=>Hl(e.element))).each((o=>{Et(o,"data-mce-tooltip").each((o=>{wx.setComponents(e,t.tooltips.getComponents({tooltipText:o}))}))}))}}),mode:"children-normal",anchor:e=>({type:"node",node:yi(e.element,"[data-mce-tooltip]:hover").orThunk((()=>Hl(e.element).filter((e=>Et(e,"data-mce-tooltip").isSome())))),root:e.element,layouts:{onLtr:x([pl,gl,cl,ul,dl,ml]),onRtl:x([pl,gl,cl,ul,dl,ml])},bubble:vc(0,-2,{})})})])}):ck.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html,attributes:{role:"document"}},containerBehaviours:El([fk.config({}),ih.config({})])}))(e,t.shared.providers))),imagepreview:nE(((e,t,o)=>((e,t)=>{const o=Ms(t.getOr({url:""})),n=Kh({dom:{tag:"img",classes:["tox-imagepreview__image"],attributes:t.map((e=>({src:e.url}))).getOr({})}}),s=Kh({dom:{tag:"div",classes:["tox-imagepreview__container"],attributes:{role:"presentation"}},components:[n.asSpec()]}),r={};e.height.each((e=>r.height=e));const a=t.map((e=>({url:e.url,zoom:A.from(e.zoom),cachedWidth:A.from(e.cachedWidth),cachedHeight:A.from(e.cachedHeight)})));return{dom:{tag:"div",classes:["tox-imagepreview"],styles:r,attributes:{role:"presentation"}},components:[s.asSpec()],behaviours:El([UO(),$O(a,(()=>o.get()),((e,t)=>{const r={url:t.url};t.zoom.each((e=>r.zoom=e)),t.cachedWidth.each((e=>r.cachedWidth=e)),t.cachedHeight.each((e=>r.cachedHeight=e)),o.set(r);const a=()=>{const{cachedWidth:t,cachedHeight:o,zoom:n}=r;if(!u(t)&&!u(o)){if(u(n)){const n=((e,t,o)=>{const n=Qt(e),s=Gt(e);return Math.min(n/t,s/o,1)})(e.element,t,o);r.zoom=n}const a=((e,t,o,n,s)=>{const r=o*s,a=n*s,i=Math.max(0,e/2-r/2),l=Math.max(0,t/2-a/2);return{left:i.toString()+"px",top:l.toString()+"px",width:r.toString()+"px",height:a.toString()+"px"}})(Qt(e.element),Gt(e.element),t,o,r.zoom);s.getOpt(e).each((e=>{Ft(e.element,a)}))}};n.getOpt(e).each((o=>{const n=o.element;var s;t.url!==Tt(n,"src")&&(Ot(n,"src",t.url),$a(e.element,"tox-imagepreview__loaded")),a(),(s=n,new Promise(((e,t)=>{const o=()=>{r(),e(s)},n=[ac(s,"load",o),ac(s,"error",(()=>{r(),t("Unable to load data from image: "+s.dom.src)}))],r=()=>H(n,(e=>e.unbind()));s.dom.complete&&o()}))).then((t=>{e.getSystem().isConnected()&&(ja(e.element,"tox-imagepreview__loaded"),r.cachedWidth=t.dom.naturalWidth,r.cachedHeight=t.dom.naturalHeight,a())}))}))}))])}})(e,o))),table:nE(((e,t)=>((e,t)=>{const o=e=>({dom:{tag:"td",innerHtml:t.translate(e)}});return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(s=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:V(s,(e=>({dom:{tag:"th",innerHtml:t.translate(e)}})))}]}),(n=e.cells,{dom:{tag:"tbody"},components:V(n,(e=>({dom:{tag:"tr"},components:V(e,o)})))})],behaviours:El([fk.config({}),ih.config({})])};var n,s})(e,t.shared.providers))),tree:nE(((e,t)=>((e,t)=>{const o=e.onLeafAction.getOr(b),n=e.onToggleExpand.getOr(b),s=e.defaultExpandedIds,r=Ms(s),a=Ms(e.defaultSelectedId),i=da("tree-id"),l=(n,s)=>e.items.map((e=>"leaf"===e.type?bT({leaf:e,selectedId:n,onLeafAction:o,visible:!0,treeId:i,backstage:t}):ST({directory:e,selectedId:n,onLeafAction:o,expandedIds:s,labelTabstopping:!0,treeId:i,backstage:t})));return{dom:{tag:"div",classes:["tox-tree"],attributes:{role:"tree"}},components:l(a.get(),r.get()),behaviours:El([$p.config({mode:"flow",selector:".tox-tree--leaf__label--visible, .tox-tree--directory__label--visible",cycles:!1}),oh(kT,[jr("expand-tree-node",((e,t)=>{const{expanded:o,node:s}=t.event;r.set(o?[...r.get(),s]:r.get().filter((e=>e!==s))),n(r.get(),{expanded:o,node:s})}))]),Fl.config({channels:{[`update-active-item-${i}`]:{onReceive:(e,t)=>{a.set(A.some(t.value)),th.set(e,l(A.some(t.value),r.get()))}}}}),th.config({})])}})(e,t))),panel:nE(((e,t)=>((e,t)=>({dom:{tag:"div",classes:e.classes},components:V(e.items,t.shared.interpreter)}))(e,t)))},rE={field:(e,t)=>t,record:x([])},aE=(e,t,o,n)=>{const s=vn(n,{shared:{interpreter:t=>iE(e,t,o,s)}});return iE(e,t,o,s)},iE=(e,t,o,n)=>be(sE,t.type).fold((()=>(console.error(`Unknown factory type "${t.type}", defaulting to container: `,t),t)),(s=>s(e,t,o,n))),lE=(e,t,o)=>iE(rE,e,t,o),cE="layout-inset",dE=e=>e.x,uE=(e,t)=>e.x+e.width/2-t.width/2,mE=(e,t)=>e.x+e.width-t.width,gE=e=>e.y,pE=(e,t)=>e.y+e.height-t.height,hE=(e,t)=>e.y+e.height/2-t.height/2,fE=(e,t,o)=>Wi(mE(e,t),pE(e,t),o.insetSouthwest(),Yi(),"southwest",tl(e,{right:0,bottom:3}),cE),bE=(e,t,o)=>Wi(dE(e),pE(e,t),o.insetSoutheast(),qi(),"southeast",tl(e,{left:1,bottom:3}),cE),vE=(e,t,o)=>Wi(mE(e,t),gE(e),o.insetNorthwest(),$i(),"northwest",tl(e,{right:0,top:2}),cE),yE=(e,t,o)=>Wi(dE(e),gE(e),o.insetNortheast(),Gi(),"northeast",tl(e,{left:1,top:2}),cE),xE=(e,t,o)=>Wi(uE(e,t),gE(e),o.insetNorth(),Xi(),"north",tl(e,{top:2}),cE),wE=(e,t,o)=>Wi(uE(e,t),pE(e,t),o.insetSouth(),Ki(),"south",tl(e,{bottom:3}),cE),SE=(e,t,o)=>Wi(mE(e,t),hE(e,t),o.insetEast(),Zi(),"east",tl(e,{right:0}),cE),kE=(e,t,o)=>Wi(dE(e),hE(e,t),o.insetWest(),Ji(),"west",tl(e,{left:1}),cE),CE=e=>{switch(e){case"north":return xE;case"northeast":return yE;case"northwest":return vE;case"south":return wE;case"southeast":return bE;case"southwest":return fE;case"east":return SE;case"west":return kE}},OE=(e,t,o,n,s)=>Ql(n).map(CE).getOr(xE)(e,t,o,n,s),_E=e=>{switch(e){case"north":return wE;case"northeast":return bE;case"northwest":return fE;case"south":return xE;case"southeast":return yE;case"southwest":return vE;case"east":return kE;case"west":return SE}},TE=(e,t,o,n,s)=>Ql(n).map(_E).getOr(xE)(e,t,o,n,s),EE={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},AE=(e,t,o)=>{const n={maxHeightFunction:pc()};return()=>o()?{type:"node",root:vt(bt(e())),node:A.from(e()),bubble:vc(12,12,EE),layouts:{onRtl:()=>[yE],onLtr:()=>[vE]},overrides:n}:{type:"hotspot",hotspot:t(),bubble:vc(-12,12,EE),layouts:{onRtl:()=>[cl,dl,pl],onLtr:()=>[dl,cl,pl]},overrides:n}},ME=(e,t,o,n)=>{const s={maxHeightFunction:pc()};return()=>n()?{type:"node",root:vt(bt(t())),node:A.from(t()),bubble:vc(12,12,EE),layouts:{onRtl:()=>[xE],onLtr:()=>[xE]},overrides:s}:e?{type:"node",root:vt(bt(t())),node:A.from(t()),bubble:vc(0,-$t(t()),EE),layouts:{onRtl:()=>[gl],onLtr:()=>[gl]},overrides:s}:{type:"hotspot",hotspot:o(),bubble:vc(0,0,EE),layouts:{onRtl:()=>[gl],onLtr:()=>[gl]},overrides:s}},DE=(e,t,o)=>()=>o()?{type:"node",root:vt(bt(e())),node:A.from(e()),layouts:{onRtl:()=>[xE],onLtr:()=>[xE]}}:{type:"hotspot",hotspot:t(),layouts:{onRtl:()=>[pl],onLtr:()=>[pl]}},BE=(e,t)=>()=>({type:"selection",root:t(),getSelection:()=>{const t=e.selection.getRng(),o=e.model.table.getSelectedCells();if(o.length>1){const e=o[0],t=o[o.length-1],n={firstCell:ze(e),lastCell:ze(t)};return A.some(n)}return A.some(Gc.range(ze(t.startContainer),t.startOffset,ze(t.endContainer),t.endOffset))}}),IE=e=>t=>({type:"node",root:e(),node:t}),FE=(e,t,o,n)=>{const s=lv(e),r=()=>ze(e.getBody()),a=()=>ze(e.getContentAreaContainer()),i=()=>s||!n();return{inlineDialog:AE(a,t,i),inlineBottomDialog:ME(e.inline,a,o,i),banner:DE(a,t,i),cursor:BE(e,r),node:IE(r)}},RE=e=>(t,o)=>{nS(e)(t,o)},NE=e=>()=>Ww(e),LE=e=>t=>Vw(e,t),zE=e=>t=>Uw(e,t),VE=e=>()=>Ub(e),HE=e=>ye(e,"items"),PE=e=>ye(e,"format"),UE=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],WE=e=>j(e,((e,t)=>{if(ve(t,"items")){const o=WE(t.items);return{customFormats:e.customFormats.concat(o.customFormats),formats:e.formats.concat([{title:t.title,items:o.formats}])}}if(ve(t,"inline")||(e=>ve(e,"block"))(t)||(e=>ve(e,"selector"))(t)){const o=`custom-${r(t.name)?t.name:t.title.toLowerCase()}`;return{customFormats:e.customFormats.concat([{name:o,format:t}]),formats:e.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return{...e,formats:e.formats.concat(t)}}),{customFormats:[],formats:[]}),jE=e=>wb(e).map((t=>{const o=((e,t)=>{const o=WE(t),n=t=>{H(t,(t=>{e.formatter.has(t.name)||e.formatter.register(t.name,t.format)}))};return e.formatter?n(o.customFormats):e.on("init",(()=>{n(o.customFormats)})),o.formats})(e,t);return Sb(e)?UE.concat(o):o})).getOr(UE),GE=(e,t,o)=>({...e,type:"formatter",isSelected:t(e.format),getStylePreview:o(e.format)}),$E=(e,t,o,n)=>{const s=t=>V(t,(t=>HE(t)?(e=>{const t=s(e.items);return{...e,type:"submenu",getStyleItems:x(t)}})(t):PE(t)?(e=>GE(e,o,n))(t):(e=>{const t=ae(e);return 1===t.length&&R(t,"title")})(t)?{...t,type:"separator"}:(t=>{const s=r(t.name)?t.name:da(t.title),a=`custom-${s}`,i={...t,type:"formatter",format:a,isSelected:o(a),getStylePreview:n(a)};return e.formatter.register(s,i),i})(t)));return s(t)},qE=e=>{let t=0;const o=e=>[{dom:{tag:"div",classes:["tox-tooltip__body"]},components:[ai(e.tooltipText)]}];return{getConfig:n=>({delayForShow:()=>t>0?60:300,delayForHide:x(300),exclusive:!0,lazySink:e,tooltipDom:{tag:"div",classes:["tox-tooltip","tox-tooltip--up"]},tooltipComponents:o(n),onShow:(e,o)=>{t++,n.onShow&&n.onShow(e,o)},onHide:()=>{t--}}),getComponents:o}},YE=ZO.trim,XE=e=>t=>{if((e=>g(e)&&1===e.nodeType)(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},KE=XE("true"),JE=XE("false"),ZE=(e,t,o,n,s)=>({type:e,title:t,url:o,level:n,attach:s}),QE=e=>e.innerText||e.textContent,eA=e=>(e=>e&&"A"===e.nodeName&&void 0!==(e.id||e.name))(e)&&oA(e),tA=e=>e&&/^(H[1-6])$/.test(e.nodeName),oA=e=>(e=>{let t=e;for(;t=t.parentNode;){const e=t.contentEditable;if(e&&"inherit"!==e)return KE(t)}return!1})(e)&&!JE(e),nA=e=>tA(e)&&oA(e),sA=e=>{var t;const o=(e=>e.id?e.id:da("h"))(e);return ZE("header",null!==(t=QE(e))&&void 0!==t?t:"","#"+o,(e=>tA(e)?parseInt(e.nodeName.substr(1),10):0)(e),(()=>{e.id=o}))},rA=e=>{const t=e.id||e.name,o=QE(e);return ZE("anchor",o||"#"+t,"#"+t,0,b)},aA=e=>YE(e.title).length>0,iA=e=>{const t=(e=>{const t=V(Qc(ze(e),"h1,h2,h3,h4,h5,h6,a:not([href])"),(e=>e.dom));return t})(e);return U((e=>V(U(e,nA),sA))(t).concat((e=>V(U(e,eA),rA))(t)),aA)},lA="tinymce-url-history",cA=e=>r(e)&&/^https?/.test(e),dA=e=>a(e)&&he(e,(e=>{return!(l(t=e)&&t.length<=5&&X(t,cA));var t})).isNone(),uA=()=>{const e=_w.getItem(lA);if(null===e)return{};let t;try{t=JSON.parse(e)}catch(e){if(e instanceof SyntaxError)return console.log("Local storage "+lA+" was not valid JSON",e),{};throw e}return dA(t)?t:(console.log("Local storage "+lA+" was not valid format",t),{})},mA=e=>{const t=uA();return be(t,e).getOr([])},gA=(e,t)=>{if(!cA(e))return;const o=uA(),n=be(o,t).getOr([]),s=U(n,(t=>t!==e));o[t]=[e].concat(s).slice(0,5),(e=>{if(!dA(e))throw new Error("Bad format for history:\n"+JSON.stringify(e));_w.setItem(lA,JSON.stringify(e))})(o)},pA=e=>!!e,hA=e=>ce(ZO.makeMap(e,/[, ]/),pA),fA=e=>A.from(Rb(e)),bA=e=>A.from(e).filter(r).getOrUndefined(),vA=e=>({getHistory:mA,addToHistory:gA,getLinkInformation:()=>(e=>Vb(e)?A.some({targets:iA(e.getBody()),anchorTop:bA(Hb(e)),anchorBottom:bA(Pb(e))}):A.none())(e),getValidationHandler:()=>(e=>A.from(Nb(e)))(e),getUrlPicker:t=>((e,t)=>((e,t)=>{const o=(e=>{const t=A.from(zb(e)).filter(pA).map(hA);return fA(e).fold(T,(e=>t.fold(E,(e=>ae(e).length>0&&e))))})(e);return d(o)?o?fA(e):A.none():o[t]?fA(e):A.none()})(e,t).map((o=>n=>SS((s=>{const i={filetype:t,fieldname:n.fieldname,...A.from(n.meta).getOr({})};o.call(e,((e,t)=>{if(!r(e))throw new Error("Expected value to be string");if(void 0!==t&&!a(t))throw new Error("Expected meta to be a object");s({value:e,meta:t})}),n.value,i)})))))(e,t)}),yA=hm,xA=Zu,wA=x([ws("shell",!1),ss("makeItem"),ws("setupItem",b),ku("listBehaviours",[th])]),SA=Xu({name:"items",overrides:()=>({behaviours:El([th.config({})])})}),kA=x([SA]),CA=Sm({name:x("CustomList")(),configFields:wA(),partFields:kA(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[th.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:Su(e.listBehaviours,s.behaviours),apis:{setItems:(t,o)=>{var n;(n=t,e.shell?A.some(n):im(n,e,"items")).fold((()=>{throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")}),(n=>{const s=th.contents(n),r=o.length,a=r-s.length,i=a>0?L(a,(()=>e.makeItem())):[],l=s.slice(r);H(l,(e=>th.remove(n,e))),H(i,(e=>th.append(n,e)));const c=th.contents(n);H(c,((n,s)=>{e.setupItem(t,n,o[s],s)}))}))}}}},apis:{setItems:(e,t,o)=>{e.setItems(t,o)}}}),OA=x([ss("dom"),ws("shell",!0),xu("toolbarBehaviours",[th])]),_A=x([Xu({name:"groups",overrides:()=>({behaviours:El([th.config({})])})})]),TA=Sm({name:"Toolbar",configFields:OA(),partFields:_A(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[th.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:Su(e.toolbarBehaviours,s.behaviours),apis:{setGroups:(t,o)=>{var n;(n=t,e.shell?A.some(n):im(n,e,"groups")).fold((()=>{throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")}),(e=>{th.set(e,o)}))},refresh:b},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)}}}),EA=b,AA=T,MA=x([]);var DA,BA=Object.freeze({__proto__:null,setup:EA,isDocked:AA,getBehaviours:MA});const IA=e=>(xe(zt(e,"position"),"fixed")?A.none():lt(e)).orThunk((()=>{const t=Ne("span");return at(e).bind((e=>{Ho(e,t);const o=lt(t);return Wo(t),o}))})),FA=e=>IA(e).map(Kt).getOrThunk((()=>Yt(0,0))),RA=(e,t)=>{const o=e.element;ja(o,t.transitionClass),$a(o,t.fadeOutClass),ja(o,t.fadeInClass),t.onShow(e)},NA=(e,t)=>{const o=e.element;ja(o,t.transitionClass),$a(o,t.fadeInClass),ja(o,t.fadeOutClass),t.onHide(e)},LA=(e,t)=>e.y>=t.y,zA=(e,t)=>e.bottom<=t.bottom,VA=(e,t,o)=>({location:"top",leftX:t,topY:o.bounds.y-e.y}),HA=(e,t,o)=>({location:"bottom",leftX:t,bottomY:e.bottom-o.bounds.bottom}),PA=e=>e.box.x-e.win.x,UA=(e,t,o)=>o.getInitialPos().map((o=>{const n=((e,t)=>{const o=t.optScrollEnv.fold(x(e.bounds.y),(t=>t.scrollElmTop+(e.bounds.y-t.currentScrollTop)));return Yt(e.bounds.x,o)})(o,t);return{box:Zo(n.left,n.top,Qt(e),Gt(e)),location:o.location}})),WA=(e,t,o,n,s)=>{const r=((e,t)=>{const o=t.optScrollEnv.fold(x(e.y),(t=>e.y+t.currentScrollTop-t.scrollElmTop));return Yt(e.x,o)})(t,o),a=Zo(r.left,r.top,t.width,t.height);n.setInitialPos({style:Vt(e),position:Nt(e,"position")||"static",bounds:a,location:s.location})},jA=(e,t,o)=>o.getInitialPos().bind((n=>{var s;switch(o.clearInitialPos(),n.position){case"static":return A.some({morph:"static"});case"absolute":const o=IA(e).getOr(St()),r=Qo(o),a=null!==(s=o.dom.scrollTop)&&void 0!==s?s:0;return A.some({morph:"absolute",positionCss:Ul("absolute",be(n.style,"left").map((e=>t.x-r.x)),be(n.style,"top").map((e=>t.y-r.y+a)),be(n.style,"right").map((e=>r.right-t.right)),be(n.style,"bottom").map((e=>r.bottom-t.bottom)))});default:return A.none()}})),GA=e=>{switch(e.location){case"top":return A.some({morph:"fixed",positionCss:Ul("fixed",A.some(e.leftX),A.some(e.topY),A.none(),A.none())});case"bottom":return A.some({morph:"fixed",positionCss:Ul("fixed",A.some(e.leftX),A.none(),A.none(),A.some(e.bottomY))});default:return A.none()}},$A=(e,t,o)=>{const n=e.element;return xe(zt(n,"position"),"fixed")?((e,t,o)=>((e,t,o)=>UA(e,t,o).filter((({box:e})=>((e,t,o)=>X(e,(e=>{switch(e){case"bottom":return zA(t,o.bounds);case"top":return LA(t,o.bounds)}})))(o.getModes(),e,t))).bind((({box:t})=>jA(e,t,o))))(e,t,o).orThunk((()=>t.optScrollEnv.bind((n=>UA(e,t,o))).bind((({box:e,location:o})=>{const n=on(),s=PA({win:n,box:e}),r="top"===o?VA(n,s,t):HA(n,s,t);return GA(r)})))))(n,t,o):((e,t,o)=>{const n=Qo(e),s=on(),r=((e,t,o)=>{const n=t.win,s=t.box,r=PA(t);return re(e,(e=>{switch(e){case"bottom":return zA(s,o.bounds)?A.none():A.some(HA(n,r,o));case"top":return LA(s,o.bounds)?A.none():A.some(VA(n,r,o));default:return A.none()}})).getOr({location:"no-dock"})})(o.getModes(),{win:s,box:n},t);return"top"===r.location||"bottom"===r.location?(WA(e,n,t,o,r),GA(r)):A.none()})(n,t,o)},qA=(e,t,o)=>{o.setDocked(!1),H(["left","right","top","bottom","position"],(t=>Pt(e.element,t))),t.onUndocked(e)},YA=(e,t,o,n)=>{const s="fixed"===n.position;o.setDocked(s),Wl(e.element,n),(s?t.onDocked:t.onUndocked)(e)},XA=(e,t,o,n,s=!1)=>{t.contextual.each((t=>{t.lazyContext(e).each((r=>{const a=((e,t)=>e.y<t.bottom&&e.bottom>t.y)(r,n.bounds);a!==o.isVisible()&&(o.setVisible(a),s&&!a?(Ya(e.element,[t.fadeOutClass]),t.onHide(e)):(a?RA:NA)(e,t))}))}))},KA=(e,t,o,n,s)=>{XA(e,t,o,n,!0),YA(e,t,o,s.positionCss)},JA=(e,t,o)=>{e.getSystem().isConnected()&&((e,t,o)=>{const n=t.lazyViewport(e);XA(e,t,o,n),$A(e,n,o).each((s=>{((e,t,o,n,s)=>{switch(s.morph){case"static":return qA(e,t,o);case"absolute":return YA(e,t,o,s.positionCss);case"fixed":KA(e,t,o,n,s)}})(e,t,o,n,s)}))})(e,t,o)},ZA=(e,t,o)=>{o.isDocked()&&((e,t,o)=>{const n=e.element;o.setDocked(!1);const s=t.lazyViewport(e);((e,t,o)=>{const n=e.element;return UA(n,t,o).bind((({box:e})=>jA(n,e,o)))})(e,s,o).each((n=>{switch(n.morph){case"static":qA(e,t,o);break;case"absolute":YA(e,t,o,n.positionCss)}})),o.setVisible(!0),t.contextual.each((t=>{Xa(n,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(e)})),JA(e,t,o)})(e,t,o)},QA=e=>(t,o,n)=>{const s=o.lazyViewport(t);((e,t,o,n)=>{const s=Qo(e),r=on(),a=n(r,PA({win:r,box:s}),t);return"bottom"===a.location||"top"===a.location?(((e,t,o,n,s)=>{n.getInitialPos().fold((()=>WA(e,t,o,n,s)),(()=>b))})(e,s,t,o,a),GA(a)):A.none()})(t.element,s,n,e).each((e=>{KA(t,o,n,s,e)}))},eM=QA(VA),tM=QA(HA);var oM=Object.freeze({__proto__:null,refresh:JA,reset:ZA,isDocked:(e,t,o)=>o.isDocked(),getModes:(e,t,o)=>o.getModes(),setModes:(e,t,o,n)=>o.setModes(n),forceDockToTop:eM,forceDockToBottom:tM}),nM=Object.freeze({__proto__:null,events:(e,t)=>Pr([Jr(sr(),((o,n)=>{e.contextual.each((e=>{qa(o.element,e.transitionClass)&&(Xa(o.element,[e.transitionClass,e.fadeInClass]),(t.isVisible()?e.onShown:e.onHidden)(o)),n.stop()}))})),jr(Sr(),((o,n)=>{JA(o,e,t)})),jr(Mr(),((o,n)=>{JA(o,e,t)})),jr(kr(),((o,n)=>{ZA(o,e,t)}))])}),sM=[xs("contextual",[is("fadeInClass"),is("fadeOutClass"),is("transitionClass"),cs("lazyContext"),Ni("onShow"),Ni("onShown"),Ni("onHide"),Ni("onHidden")]),Ts("lazyViewport",(()=>({bounds:on(),optScrollEnv:A.none()}))),Es("modes",["top","bottom"],Pn),Ni("onDocked"),Ni("onUndocked")];const rM=Ml({fields:sM,name:"docking",active:nM,apis:oM,state:Object.freeze({__proto__:null,init:e=>{const t=Ms(!1),o=Ms(!0),n=sc(),s=Ms(e.modes);return Ea({isDocked:t.get,setDocked:t.set,getInitialPos:n.get,setInitialPos:n.set,clearInitialPos:n.clear,isVisible:o.get,setVisible:o.set,getModes:s.get,setModes:s.set,readState:()=>`docked:  ${t.get()}, visible: ${o.get()}, modes: ${s.get().join(",")}`})}})}),aM=x(da("toolbar-height-change")),iM={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},lM="tox-tinymce--toolbar-sticky-on",cM="tox-tinymce--toolbar-sticky-off",dM=(e,t)=>R(rM.getModes(e),t),uM=e=>{const t=e.element;it(t).each((o=>{const n="padding-"+rM.getModes(e)[0];if(rM.isDocked(e)){const e=Qt(o);It(t,"width",e+"px"),It(o,n,(e=>$t(e)+(parseInt(Nt(e,"margin-top"),10)||0)+(parseInt(Nt(e,"margin-bottom"),10)||0))(t)+"px")}else Pt(t,"width"),Pt(o,n)}))},mM=(e,t)=>{t?($a(e,iM.fadeOutClass),Ya(e,[iM.transitionClass,iM.fadeInClass])):($a(e,iM.fadeInClass),Ya(e,[iM.fadeOutClass,iM.transitionClass]))},gM=(e,t)=>{const o=ze(e.getContainer());t?(ja(o,lM),$a(o,cM)):(ja(o,cM),$a(o,lM))},pM=(e,t)=>{const o=sc(),n=t.getSink,s=e=>{n().each((t=>e(t.element)))},r=t=>{e.inline||uM(t),gM(e,rM.isDocked(t)),t.getSystem().broadcastOn([tu()],{}),n().each((e=>e.getSystem().broadcastOn([tu()],{})))},a=e.inline?[]:[Fl.config({channels:{[aM()]:{onReceive:uM}}})];return[ih.config({}),rM.config({contextual:{lazyContext:t=>{const o=$t(t.element),n=e.inline?e.getContentAreaContainer():e.getContainer();return A.from(n).map((n=>{const s=Qo(ze(n));return JS(e,t.element).fold((()=>{const e=s.height-o,n=s.y+(dM(t,"top")?0:o);return Zo(s.x,n,s.width,e)}),(e=>{const n=tn(s,ZS(e)),r=dM(t,"top")?n.y:n.y+o;return Zo(n.x,r,n.width,n.height-o)}))}))},onShow:()=>{s((e=>mM(e,!0)))},onShown:e=>{s((e=>Xa(e,[iM.transitionClass,iM.fadeInClass]))),o.get().each((t=>{((e,t)=>{const o=ot(t);Vl(o).filter((e=>!et(t,e))).filter((t=>et(t,ze(o.dom.body))||tt(e,t))).each((()=>Nl(t)))})(e.element,t),o.clear()}))},onHide:e=>{((e,t)=>Hl(e).orThunk((()=>t().toOptional().bind((e=>Hl(e.element))))))(e.element,n).fold(o.clear,o.set),s((e=>mM(e,!1)))},onHidden:()=>{s((e=>Xa(e,[iM.transitionClass])))},...iM},lazyViewport:t=>JS(e,t.element).fold((()=>{const o=on(),n=Bb(e),s=o.y+(dM(t,"top")?n:0),r=o.height-(dM(t,"bottom")?n:0);return{bounds:Zo(o.x,s,o.width,r),optScrollEnv:A.none()}}),(e=>({bounds:ZS(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Kt(e.element).top})}))),modes:[t.header.getDockingMode()],onDocked:r,onUndocked:r}),...a]};var hM=Object.freeze({__proto__:null,setup:(e,t,o)=>{e.inline||(t.header.isPositionedAtTop()||e.on("ResizeEditor",(()=>{o().each(rM.reset)})),e.on("ResizeWindow ResizeEditor",(()=>{o().each(uM)})),e.on("SkinLoaded",(()=>{o().each((e=>{rM.isDocked(e)?rM.reset(e):rM.refresh(e)}))})),e.on("FullscreenStateChanged",(()=>{o().each(rM.reset)}))),e.on("AfterScrollIntoView",(e=>{o().each((t=>{rM.refresh(t);const o=t.element;Vg(o)&&((e,t)=>{const o=ot(t),n=rt(t).dom.innerHeight,s=jo(o),r=ze(e.elm),a=en(r),i=Gt(r),l=a.y,c=l+i,d=Kt(t),u=Gt(t),m=d.top,g=m+u,p=Math.abs(m-s.top)<2,h=Math.abs(g-(s.top+n))<2;if(p&&l<g)Go(s.left,l-u,o);else if(h&&c>m){const e=l-n+i+u;Go(s.left,e,o)}})(e,o)}))})),e.on("PostRender",(()=>{gM(e,!1)}))},isDocked:e=>e().map(rM.isDocked).getOr(!1),getBehaviours:pM});const fM=In([ny,rs("items",Rn([Ln([sy,ms("items",Pn)]),Pn]))].concat(Iy)),bM=[fs("text"),fs("tooltip"),fs("icon"),Ss("search",!1,Rn([Un,In([fs("placeholder")])],(e=>d(e)?e?A.some({placeholder:A.none()}):A.none():A.some(e)))),cs("fetch"),Ts("onSetup",(()=>b))],vM=In([ny,...bM]),yM=e=>Xn("menubutton",vM,e),xM=In([ny,vy,by,fy,wy,dy,py,Os("presets","normal",["normal","color","listpreview"]),_y(1),my,gy]);var wM=wm({factory:(e,t)=>{const o={focus:$p.focusIn,setMenus:(e,o)=>{const n=V(o,(e=>{const o={type:"menubutton",text:e.text,fetch:t=>{t(e.getItems())}},n=yM(o).mapError((e=>Zn(e))).getOrDie();return gT(n,"tox-mbtn",t.backstage,A.some("menuitem"))}));th.set(e,n)}};return{uid:e.uid,dom:e.dom,components:[],behaviours:El([th.config({}),oh("menubar-events",[Zr((t=>{e.onSetup(t)})),jr(Xs(),((e,t)=>{yi(e.element,".tox-mbtn--active").each((o=>{xi(t.event.target,".tox-mbtn").each((t=>{et(o,t)||e.getSystem().getByDom(o).each((o=>{e.getSystem().getByDom(t).each((e=>{LS.expand(e),LS.close(o),ih.focus(e)}))}))}))}))})),jr(Er(),((e,t)=>{t.event.prevFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((o=>{t.event.newFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((e=>{LS.isOpen(o)&&(LS.expand(e),LS.close(o))}))}))}))]),$p.config({mode:"flow",selector:".tox-mbtn",onEscape:t=>(e.onEscape(t),A.some(!0))}),fk.config({})]),apis:o,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[ss("dom"),ss("uid"),ss("onEscape"),ss("backstage"),ws("onSetup",b)],apis:{focus:(e,t)=>{e.focus(t)},setMenus:(e,t,o)=>{e.setMenus(t,o)}}});const SM="container",kM=[xu("slotBehaviours",[])],CM=e=>"<alloy.field."+e+">",OM=(e,t)=>{const o=t=>um(e),n=(t,o)=>(n,s)=>im(n,e,s).map((e=>t(e,s))).getOr(o),s=(e,t)=>"true"!==Tt(e.element,"aria-hidden"),r=n(s,!1),a=n(((e,t)=>{if(s(e)){const o=e.element;It(o,"display","none"),Ot(o,"aria-hidden","true"),Nr(e,Ar(),{name:t,visible:!1})}})),i=(e=>(t,o)=>{H(o,(o=>e(t,o)))})(a),l=n(((e,t)=>{if(!s(e)){const o=e.element;Pt(o,"display"),Mt(o,"aria-hidden"),Nr(e,Ar(),{name:t,visible:!0})}})),c={getSlotNames:o,getSlot:(t,o)=>im(t,e,o),isShowing:r,hideSlot:a,hideAllSlots:e=>i(e,o()),showSlot:l};return{uid:e.uid,dom:e.dom,components:t,behaviours:wu(e.slotBehaviours),apis:c}},_M=ce({getSlotNames:(e,t)=>e.getSlotNames(t),getSlot:(e,t,o)=>e.getSlot(t,o),isShowing:(e,t,o)=>e.isShowing(t,o),hideSlot:(e,t,o)=>e.hideSlot(t,o),hideAllSlots:(e,t)=>e.hideAllSlots(t),showSlot:(e,t,o)=>e.showSlot(t,o)},(e=>_a(e))),TM={..._M,sketch:e=>{const t=(()=>{const e=[];return{slot:(t,o)=>(e.push(t),om(SM,CM(t),o)),record:x(e)}})(),o=e(t),n=t.record(),s=V(n,(e=>qu({name:e,pname:CM(e)})));return bm(SM,kM,s,OM,o)}},EM=In([by,vy,Ts("onShow",b),Ts("onHide",b),py]),AM=e=>({element:()=>e.element.dom}),MM=(e,t)=>{const o=V(ae(t),(e=>{const o=t[e],n=Kn((e=>Xn("sidebar",EM,e))(o));return{name:e,getApi:AM,onSetup:n.onSetup,onShow:n.onShow,onHide:n.onHide}}));return V(o,(t=>{const n=Ms(b);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:dx([Dx(t,n),Bx(t,n),jr(Ar(),((e,t)=>{const n=t.event,s=G(o,(e=>e.name===n.name));s.each((t=>{(n.visible?t.onShow:t.onHide)(t.getApi(e))}))}))])})}))},DM=e=>TM.sketch((t=>({dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:MM(t,e),slotBehaviours:dx([Zr((e=>TM.hideAllSlots(e)))])}))),BM=(e,t)=>{Ot(e,"role",t)},IM=e=>_m.getCurrent(e).bind((e=>uT.isGrowing(e)||uT.hasGrown(e)?_m.getCurrent(e).bind((e=>G(TM.getSlotNames(e),(t=>TM.isShowing(e,t))))):A.none())),FM=da("FixSizeEvent"),RM=da("AutoSizeEvent");var NM=Object.freeze({__proto__:null,block:(e,t,o,n)=>{Ot(e.element,"aria-busy",!0);const s=t.getRoot(e).getOr(e),r=El([$p.config({mode:"special",onTab:()=>A.some(!0),onShiftTab:()=>A.some(!0)}),ih.config({})]),a=n(s,r),i=s.getSystem().build(a);th.append(s,ui(i)),i.hasConfigured($p)&&t.focus&&$p.focusIn(i),o.isBlocked()||t.onBlock(e),o.blockWith((()=>th.remove(s,i)))},unblock:(e,t,o)=>{Mt(e.element,"aria-busy"),o.isBlocked()&&t.onUnblock(e),o.clear()},isBlocked:(e,t,o)=>o.isBlocked()}),LM=[Ts("getRoot",A.none),_s("focus",!0),Ni("onBlock"),Ni("onUnblock")];const zM=Ml({fields:LM,name:"blocking",apis:NM,state:Object.freeze({__proto__:null,init:()=>{const e=oc((e=>e.destroy()));return Ea({readState:e.isSet,blockWith:t=>{e.set({destroy:t})},clear:e.clear,isBlocked:e.isSet})}})}),VM=e=>_m.getCurrent(e).each((e=>Nl(e.element,!0))),HM=(e,t,o)=>{const n=Ms(!1),s=sc(),r=o=>{var s;n.get()&&(!(e=>"focusin"===e.type)(s=o)||!(s.composed?oe(s.composedPath()):A.from(s.target)).map(ze).filter($e).exists((e=>qa(e,"mce-pastebin"))))&&(o.preventDefault(),VM(t()),e.editorManager.setActive(e))};e.inline||e.on("PreInit",(()=>{e.dom.bind(e.getWin(),"focusin",r),e.on("BeforeExecCommand",(e=>{"mcefocus"===e.command.toLowerCase()&&!0!==e.value&&r(e)}))}));const a=s=>{s!==n.get()&&(n.set(s),((e,t,o,n)=>{const s=t.element;if(((e,t)=>{const o="tabindex",n=`data-mce-${o}`;A.from(e.iframeElement).map(ze).each((e=>{t?(Et(e,o).each((t=>Ot(e,n,t))),Ot(e,o,-1)):(Mt(e,o),Et(e,n).each((t=>{Ot(e,o,t),Mt(e,n)})))}))})(e,o),o)zM.block(t,(e=>(t,o)=>({dom:{tag:"div",attributes:{"aria-label":e.translate("Loading..."),tabindex:"0"},classes:["tox-throbber__busy-spinner"]},components:[{dom:Xh('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}))(n)),Pt(s,"display"),Mt(s,"aria-hidden"),e.hasFocus()&&VM(t);else{const o=_m.getCurrent(t).exists((e=>zl(e.element)));zM.unblock(t),It(s,"display","none"),Ot(s,"aria-hidden","true"),o&&e.focus()}})(e,t(),s,o.providers),((e,t)=>{e.dispatch("AfterProgressState",{state:t})})(e,s))};e.on("ProgressState",(t=>{if(s.on(clearTimeout),h(t.time)){const o=qh.setEditorTimeout(e,(()=>a(t.state)),t.time);s.set(o)}else a(t.state),s.clear()}))},PM=(e,t,o)=>({within:e,extra:t,withinWidth:o}),UM=(e,t,o)=>{const n=j(e,((e,t)=>((e,t)=>{const n=o(e);return A.some({element:e,start:t,finish:t+n,width:n})})(t,e.len).fold(x(e),(t=>({len:t.finish,list:e.list.concat([t])})))),{len:0,list:[]}).list,s=U(n,(e=>e.finish<=t)),r=W(s,((e,t)=>e+t.width),0);return{within:s,extra:n.slice(s.length),withinWidth:r}},WM=e=>V(e,(e=>e.element)),jM=(e,t)=>{const o=V(t,(e=>ui(e)));TA.setGroups(e,o)},GM=(e,t,o)=>{const n=t.builtGroups.get();if(0===n.length)return;const s=lm(e,t,"primary"),r=bS.getCoupled(e,"overflowGroup");It(s.element,"visibility","hidden");const a=n.concat([r]),i=re(a,(e=>Hl(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()))));o([]),jM(s,a);const l=((e,t,o,n)=>{const s=((e,t,o)=>{const n=UM(t,e,o);return 0===n.extra.length?A.some(n):A.none()})(e,t,o).getOrThunk((()=>UM(t,e-o(n),o))),r=s.within,a=s.extra,i=s.withinWidth;return 1===a.length&&a[0].width<=o(n)?((e,t,o)=>{const n=WM(e.concat(t));return PM(n,[],o)})(r,a,i):a.length>=1?((e,t,o,n)=>{const s=WM(e).concat([o]);return PM(s,WM(t),n)})(r,a,n,i):((e,t,o)=>PM(WM(e),[],o))(r,0,i)})(Qt(s.element),t.builtGroups.get(),(e=>Math.ceil(e.element.dom.getBoundingClientRect().width)),r);0===l.extra.length?(th.remove(s,r),o([])):(jM(s,l.within),o(l.extra)),Pt(s.element,"visibility"),Ut(s.element),i.each(ih.focus)},$M=x([xu("splitToolbarBehaviours",[bS]),os("builtGroups",(()=>Ms([])))]),qM=x([Fi(["overflowToggledClass"]),vs("getOverflowBounds"),ss("lazySink"),os("overflowGroups",(()=>Ms([]))),Ni("onOpened"),Ni("onClosed")].concat($M())),YM=x([qu({factory:TA,schema:OA(),name:"primary"}),Yu({schema:OA(),name:"overflow"}),Yu({name:"overflow-button"}),Yu({name:"overflow-group"})]),XM=x(((e,t)=>{((e,t)=>{const o=Zt.max(e,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);It(e,"max-width",o+"px")})(e,Math.floor(t))})),KM=x([Fi(["toggledClass"]),ss("lazySink"),cs("fetch"),vs("getBounds"),xs("fireDismissalEventInstead",[ws("event",_r())]),_c(),Ni("onToggled")]),JM=x([Yu({name:"button",overrides:e=>({dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:El([hh.config({toggleClass:e.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1,onToggled:e.onToggled})])})}),Yu({factory:TA,schema:OA(),name:"toolbar",overrides:e=>({toolbarBehaviours:El([$p.config({mode:"cyclic",onEscape:t=>(im(t,e,"button").each(ih.focus),A.none())})])})})]),ZM=sc(),QM=(e,t)=>{const o=bS.getCoupled(e,"toolbarSandbox");Qd.isOpen(o)?Qd.close(o):Qd.open(o,t.toolbar())},eD=(e,t,o,n)=>{const s=o.getBounds.map((e=>e())),r=o.lazySink(e).getOrDie();Td.positionWithinBounds(r,t,{anchor:{type:"hotspot",hotspot:e,layouts:n,overrides:{maxWidthFunction:XM()}}},s)},tD=(e,t,o,n,s)=>{TA.setGroups(t,s),eD(e,t,o,n),hh.on(e)},oD=Sm({name:"FloatingToolbarButton",factory:(e,t,o,n)=>({...Yh.sketch({...n.button(),action:e=>{QM(e,n)},buttonBehaviours:Cu({dump:n.button().buttonBehaviours},[bS.config({others:{toolbarSandbox:t=>((e,t,o)=>{const n=Si();return{dom:{tag:"div",attributes:{id:n.id}},behaviours:El([$p.config({mode:"special",onEscape:e=>(Qd.close(e),A.some(!0))}),Qd.config({onOpen:(s,r)=>{const a=ZM.get().getOr(!1);o.fetch().get((s=>{tD(e,r,o,t.layouts,s),n.link(e.element),a||$p.focusIn(r)}))},onClose:()=>{hh.off(e),ZM.get().getOr(!1)||ih.focus(e),n.unlink(e.element)},isPartOf:(t,o,n)=>ki(o,n)||ki(e,n),getAttachPoint:()=>o.lazySink(e).getOrDie()}),Fl.config({channels:{...su({isExtraPart:T,...o.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...au({doReposition:()=>{Qd.getState(bS.getCoupled(e,"toolbarSandbox")).each((n=>{eD(e,n,o,t.layouts)}))}})}})])}})(t,o,e)}})])}),apis:{setGroups:(t,n)=>{Qd.getState(bS.getCoupled(t,"toolbarSandbox")).each((s=>{tD(t,s,e,o.layouts,n)}))},reposition:t=>{Qd.getState(bS.getCoupled(t,"toolbarSandbox")).each((n=>{eD(t,n,e,o.layouts)}))},toggle:e=>{QM(e,n)},toggleWithoutFocusing:e=>{((e,t)=>{ZM.set(!0),QM(e,t),ZM.clear()})(e,n)},getToolbar:e=>Qd.getState(bS.getCoupled(e,"toolbarSandbox")),isOpen:e=>Qd.isOpen(bS.getCoupled(e,"toolbarSandbox"))}}),configFields:KM(),partFields:JM(),apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggleWithoutFocusing(t)},getToolbar:(e,t)=>e.getToolbar(t),isOpen:(e,t)=>e.isOpen(t)}}),nD=x([ss("items"),Fi(["itemSelector"]),xu("tgroupBehaviours",[$p])]),sD=x([Ku({name:"items",unit:"item"})]),rD=Sm({name:"ToolbarGroup",configFields:nD(),partFields:sD(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Su(e.tgroupBehaviours,[$p.config({mode:"flow",selector:e.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}})}),aD=e=>V(e,(e=>ui(e))),iD=(e,t,o)=>{GM(e,o,(n=>{o.overflowGroups.set(n),t.getOpt(e).each((e=>{oD.setGroups(e,aD(n))}))}))},lD=Sm({name:"SplitFloatingToolbar",configFields:qM(),partFields:YM(),factory:(e,t,o,n)=>{const s=Kh(oD.sketch({fetch:()=>SS((t=>{t(aD(e.overflowGroups.get()))})),layouts:{onLtr:()=>[dl,cl],onRtl:()=>[cl,dl],onBottomLtr:()=>[ml,ul],onBottomRtl:()=>[ul,ml]},getBounds:o.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:n["overflow-button"](),toolbar:n.overflow()},onToggled:(t,o)=>e[o?"onOpened":"onClosed"](t)}));return{uid:e.uid,dom:e.dom,components:t,behaviours:Su(e.splitToolbarBehaviours,[bS.config({others:{overflowGroup:()=>rD.sketch({...n["overflow-group"](),items:[s.asSpec()]})}})]),apis:{setGroups:(t,o)=>{e.builtGroups.set(V(o,t.getSystem().build)),iD(t,s,e)},refresh:t=>iD(t,s,e),toggle:e=>{s.getOpt(e).each((e=>{oD.toggle(e)}))},toggleWithoutFocusing:e=>{s.getOpt(e).each(oD.toggleWithoutFocusing)},isOpen:e=>s.getOpt(e).map(oD.isOpen).getOr(!1),reposition:e=>{s.getOpt(e).each((e=>{oD.reposition(e)}))},getOverflow:e=>s.getOpt(e).bind(oD.getToolbar)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t),getOverflow:(e,t)=>e.getOverflow(t)}}),cD=x([Fi(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),Ni("onOpened"),Ni("onClosed")].concat($M())),dD=x([qu({factory:TA,schema:OA(),name:"primary"}),qu({factory:TA,schema:OA(),name:"overflow",overrides:e=>({toolbarBehaviours:El([uT.config({dimension:{property:"height"},closedClass:e.markers.closedClass,openClass:e.markers.openClass,shrinkingClass:e.markers.shrinkingClass,growingClass:e.markers.growingClass,onShrunk:t=>{im(t,e,"overflow-button").each((e=>{hh.off(e),ih.focus(e)})),e.onClosed(t)},onGrown:t=>{$p.focusIn(t),e.onOpened(t)},onStartGrow:t=>{im(t,e,"overflow-button").each(hh.on)}}),$p.config({mode:"acyclic",onEscape:t=>(im(t,e,"overflow-button").each(ih.focus),A.some(!0))})])})}),Yu({name:"overflow-button",overrides:e=>({buttonBehaviours:El([hh.config({toggleClass:e.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])})}),Yu({name:"overflow-group"})]),uD=(e,t)=>{im(e,t,"overflow-button").bind((()=>im(e,t,"overflow"))).each((o=>{mD(e,t),uT.toggleGrow(o)}))},mD=(e,t)=>{im(e,t,"overflow").each((o=>{GM(e,t,(e=>{const t=V(e,(e=>ui(e)));TA.setGroups(o,t)})),im(e,t,"overflow-button").each((e=>{uT.hasGrown(o)&&hh.on(e)})),uT.refresh(o)}))},gD=Sm({name:"SplitSlidingToolbar",configFields:cD(),partFields:dD(),factory:(e,t,o,n)=>{const s="alloy.toolbar.toggle";return{uid:e.uid,dom:e.dom,components:t,behaviours:Su(e.splitToolbarBehaviours,[bS.config({others:{overflowGroup:e=>rD.sketch({...n["overflow-group"](),items:[Yh.sketch({...n["overflow-button"](),action:t=>{Rr(e,s)}})]})}}),oh("toolbar-toggle-events",[jr(s,(t=>{uD(t,e)}))])]),apis:{setGroups:(t,o)=>{((t,o)=>{const n=V(o,t.getSystem().build);e.builtGroups.set(n)})(t,o),mD(t,e)},refresh:t=>mD(t,e),toggle:t=>uD(t,e),isOpen:t=>((e,t)=>im(e,t,"overflow").map(uT.hasGrown).getOr(!1))(t,e)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t)}}),pD=e=>{const t=e.title.fold((()=>({})),(e=>({attributes:{title:e}})));return{dom:{tag:"div",classes:["tox-toolbar__group"],...t},components:[rD.parts.items({})],items:e.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled]), .tox-number-input:not([disabled])"},tgroupBehaviours:El([fk.config({}),ih.config({})])}},hD=e=>rD.sketch(pD(e)),fD=(e,t)=>{const o=Zr((t=>{const o=V(e.initGroups,hD);TA.setGroups(t,o)}));return El([Ax(e.providers.isDisabled),_x(),$p.config({mode:t,onEscape:e.onEscape,selector:".tox-toolbar__group"}),oh("toolbar-events",[o])])},bD=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return{uid:e.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":pD({title:A.none(),items:[]}),"overflow-button":VT({name:"more",icon:A.some("more-drawer"),enabled:!0,tooltip:A.some("Reveal or hide additional toolbar items"),primary:!1,buttonType:A.none(),borderless:!1},A.none(),e.providers,[],"overflow-button")},splitToolbarBehaviours:fD(e,t)}},vD=e=>{const t=bD(e),o=lD.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return lD.sketch({...t,lazySink:e.getSink,getOverflowBounds:()=>{const t=e.moreDrawerData.lazyHeader().element,o=en(t),n=st(t),s=en(n),r=Math.max(n.dom.scrollHeight,s.height);return Zo(o.x+4,s.y,o.width-8,r)},parts:{...t.parts,overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:e.attributes}}},components:[o],markers:{overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>e.onToggled(t,!0),onClosed:t=>e.onToggled(t,!1)})},yD=e=>{const t=gD.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),o=gD.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),n=bD(e);return gD.sketch({...n,components:[t,o],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>{t.getSystem().broadcastOn([aM()],{type:"opened"}),e.onToggled(t,!0)},onClosed:t=>{t.getSystem().broadcastOn([aM()],{type:"closed"}),e.onToggled(t,!1)}})},xD=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return TA.sketch({uid:e.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(e.type===ab.scrolling?["tox-toolbar--scrolling"]:[])},components:[TA.parts.groups({})],toolbarBehaviours:fD(e,t)})},wD=[fy,by,fs("tooltip"),Os("buttonType","secondary",["primary","secondary"]),_s("borderless",!1),cs("onAction")],SD={button:[...wD,ay,ls("type",["button"])],togglebutton:[...wD,_s("active",!1),ls("type",["togglebutton"])]},kD=[ls("type",["group"]),Es("buttons",[],Qn("type",SD))],CD=Qn("type",{...SD,group:kD}),OD=In([Es("buttons",[],CD),cs("onShow"),cs("onHide")]),_D=(e,t)=>((e,t)=>{var o,n;const s="togglebutton"===e.type,r=e.icon.map((e=>E_(e,t.icons))).map(Kh),a={...e,name:s?e.text.getOr(e.icon.getOr("")):null!==(o=e.text)&&void 0!==o?o:e.icon.getOr(""),primary:"primary"===e.buttonType,buttonType:A.from(e.buttonType),tooltip:e.tooltip,icon:e.icon,enabled:!0,borderless:e.borderless},i=HT(null!==(n=e.buttonType)&&void 0!==n?n:"secondary"),l=s?e.text.map(t.translate):A.some(t.translate(e.text)),c=l.map(ai),d=a.tooltip.or(l).map((e=>({"aria-label":t.translate(e)}))).getOr({}),u=r.map((e=>e.asSpec())),m=Rx([u,c]),g=e.icon.isSome()&&c.isSome(),p={tag:"button",classes:i.concat(...e.icon.isSome()&&!g?["tox-button--icon"]:[]).concat(...g?["tox-button--icon-and-text"]:[]).concat(...e.borderless?["tox-button--naked"]:[]).concat(..."togglebutton"===e.type&&e.active?["tox-button--enabled"]:[]),attributes:d},h=zT(a,A.some((o=>{const n=e=>{r.map((n=>n.getOpt(o).each((o=>{th.set(o,[E_(e,t.icons)])}))))};return s?e.onAction({setIcon:n,setActive:e=>{const t=o.element;e?(ja(t,"tox-button--enabled"),Ot(t,"aria-pressed",!0)):($a(t,"tox-button--enabled"),Mt(t,"aria-pressed"))},isActive:()=>qa(o.element,"tox-button--enabled")}):"button"===e.type?e.onAction({setIcon:n}):void 0})),[],p,m,e.tooltip,t);return Yh.sketch(h)})(e,t),TD=Io().deviceType,ED=TD.isPhone(),AD=TD.isTablet();var MD=Sm({name:"silver.View",configFields:[ss("viewConfig")],partFields:[Xu({factory:{sketch:e=>{let t=!1;const o=V(e.buttons,(o=>"group"===o.type?(t=!0,((e,t)=>({dom:{tag:"div",classes:["tox-view__toolbar__group"]},components:V(e.buttons,(e=>_D(e,t)))}))(o,e.providers)):_D(o,e.providers)));return{uid:e.uid,dom:{tag:"div",classes:[t?"tox-view__toolbar":"tox-view__header",...ED||AD?["tox-view--mobile","tox-view--scrolling"]:[]]},behaviours:El([ih.config({}),$p.config({mode:"flow",selector:"button, .tox-button",focusInside:yg.OnEnterOrSpaceMode})]),components:t?o:[ck.sketch({dom:{tag:"div",classes:["tox-view__header-start"]},components:[]}),ck.sketch({dom:{tag:"div",classes:["tox-view__header-end"]},components:o})]}}},schema:[ss("buttons"),ss("providers")],name:"header"}),Xu({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-view__pane"]}})},schema:[],name:"pane"})],factory:(e,t,o,n)=>{const s={getPane:t=>yA.getPart(t,e,"pane"),getOnShow:t=>e.viewConfig.onShow,getOnHide:t=>e.viewConfig.onHide};return{uid:e.uid,dom:e.dom,components:t,apis:s}},apis:{getPane:(e,t)=>e.getPane(t),getOnShow:(e,t)=>e.getOnShow(t),getOnHide:(e,t)=>e.getOnHide(t)}});const DD=(e,t,o)=>pe(t,((t,n)=>{const s=Kn(Xn("view",OD,t));return e.slot(n,MD.sketch({dom:{tag:"div",classes:["tox-view"]},viewConfig:s,components:[...s.buttons.length>0?[MD.parts.header({buttons:s.buttons,providers:o})]:[],MD.parts.pane({})]}))})),BD=(e,t)=>TM.sketch((o=>({dom:{tag:"div",classes:["tox-view-wrap__slot-container"]},components:DD(o,e,t),slotBehaviours:dx([Zr((e=>TM.hideAllSlots(e)))])}))),ID=e=>G(TM.getSlotNames(e),(t=>TM.isShowing(e,t))),FD=(e,t,o)=>{TM.getSlot(e,t).each((e=>{MD.getPane(e).each((t=>{var n;o(e)((n=t.element.dom,{getContainer:x(n)}))}))}))};var RD=wm({factory:(e,t)=>{const o={setViews:(e,o)=>{th.set(e,[BD(o,t.backstage.shared.providers)])},whichView:e=>_m.getCurrent(e).bind(ID),toggleView:(e,t,o,n)=>_m.getCurrent(e).exists((s=>{const r=ID(s),a=r.exists((e=>n===e)),i=TM.getSlot(s,n).isSome();return i&&(TM.hideAllSlots(s),a?((e=>{const t=e.element;It(t,"display","none"),Ot(t,"aria-hidden","true")})(e),t()):(o(),(e=>{const t=e.element;Pt(t,"display"),Mt(t,"aria-hidden")})(e),TM.showSlot(s,n),((e,t)=>{FD(e,t,MD.getOnShow)})(s,n)),r.each((e=>((e,t)=>FD(e,t,MD.getOnHide))(s,e)))),i}))};return{uid:e.uid,dom:{tag:"div",classes:["tox-view-wrap"],attributes:{"aria-hidden":"true"},styles:{display:"none"}},components:[],behaviours:El([th.config({}),_m.config({find:e=>{const t=th.contents(e);return oe(t)}})]),apis:o}},name:"silver.ViewWrapper",configFields:[ss("backstage")],apis:{setViews:(e,t,o)=>e.setViews(t,o),toggleView:(e,t,o,n,s)=>e.toggleView(t,o,n,s),whichView:(e,t)=>e.whichView(t)}});const ND=xA.optional({factory:wM,name:"menubar",schema:[ss("backstage")]}),LD=xA.optional({factory:{sketch:e=>CA.sketch({uid:e.uid,dom:e.dom,listBehaviours:El([$p.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:()=>xD({type:e.type,uid:da("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:e.providers,onEscape:()=>(e.onEscape(),A.some(!0))}),setupItem:(e,t,o,n)=>{TA.setGroups(t,o)},shell:!0})},name:"multiple-toolbar",schema:[ss("dom"),ss("onEscape")]}),zD=xA.optional({factory:{sketch:e=>{const t=(e=>e.type===ab.sliding?yD:e.type===ab.floating?vD:xD)(e);return t({type:e.type,uid:e.uid,onEscape:()=>(e.onEscape(),A.some(!0)),onToggled:(t,o)=>e.onToolbarToggled(o),cyclicKeying:!1,initGroups:[],getSink:e.getSink,providers:e.providers,moreDrawerData:{lazyToolbar:e.lazyToolbar,lazyMoreButton:e.lazyMoreButton,lazyHeader:e.lazyHeader},attributes:e.attributes})}},name:"toolbar",schema:[ss("dom"),ss("onEscape"),ss("getSink")]}),VD=xA.optional({factory:{sketch:e=>{const t=e.editor,o=e.sticky?pM:MA;return{uid:e.uid,dom:e.dom,components:e.components,behaviours:El(o(t,e.sharedBackstage))}}},name:"header",schema:[ss("dom")]}),HD=xA.optional({factory:{sketch:e=>({uid:e.uid,dom:e.dom,components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/tinymce-self-hosted-premium-features/?utm_campaign=self_hosted_upgrade_promo&utm_source=tiny&utm_medium=referral",rel:"noopener",target:"_blank","aria-hidden":"true"},classes:["tox-promotion-link"],innerHtml:"\u26a1\ufe0fUpgrade"}}]})},name:"promotion",schema:[ss("dom")]}),PD=xA.optional({name:"socket",schema:[ss("dom")]}),UD=xA.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"presentation"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:El([fk.config({}),ih.config({}),uT.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:e=>{_m.getCurrent(e).each(TM.hideAllSlots),Rr(e,RM)},onGrown:e=>{Rr(e,RM)},onStartGrow:e=>{Nr(e,FM,{width:zt(e.element,"width").getOr("")})},onStartShrink:e=>{Nr(e,FM,{width:Qt(e.element)+"px"})}}),th.config({}),_m.config({find:e=>{const t=th.contents(e);return oe(t)}})])}],behaviours:El([WO(0),oh("sidebar-sliding-events",[jr(FM,((e,t)=>{It(e.element,"width",t.event.width)})),jr(RM,((e,t)=>{Pt(e.element,"width")}))])])})},name:"sidebar",schema:[ss("dom")]}),WD=xA.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:El([th.config({}),zM.config({focus:!1}),_m.config({find:e=>oe(e.components())})]),components:[]})},name:"throbber",schema:[ss("dom")]}),jD=xA.optional({factory:RD,name:"viewWrapper",schema:[ss("backstage")]}),GD=xA.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-editor-container"]},components:e.components})},name:"editorContainer",schema:[]});var $D=Sm({name:"OuterContainer",factory:(e,t,o)=>{let n=!1;const s={getSocket:t=>yA.getPart(t,e,"socket"),setSidebar:(t,o,n)=>{yA.getPart(t,e,"sidebar").each((e=>((e,t,o)=>{_m.getCurrent(e).each((n=>{th.set(n,[DM(t)]);const s=null==o?void 0:o.toLowerCase();r(s)&&ve(t,s)&&_m.getCurrent(n).each((t=>{TM.showSlot(t,s),uT.immediateGrow(n),Pt(n.element,"width"),BM(e.element,"region")}))}))})(e,o,n)))},toggleSidebar:(t,o)=>{yA.getPart(t,e,"sidebar").each((e=>((e,t)=>{_m.getCurrent(e).each((o=>{_m.getCurrent(o).each((n=>{uT.hasGrown(o)?TM.isShowing(n,t)?(uT.shrink(o),BM(e.element,"presentation")):(TM.hideAllSlots(n),TM.showSlot(n,t),BM(e.element,"region")):(TM.hideAllSlots(n),TM.showSlot(n,t),uT.grow(o),BM(e.element,"region"))}))}))})(e,o)))},whichSidebar:t=>yA.getPart(t,e,"sidebar").bind(IM).getOrNull(),getHeader:t=>yA.getPart(t,e,"header"),getToolbar:t=>yA.getPart(t,e,"toolbar"),setToolbar:(t,o)=>{yA.getPart(t,e,"toolbar").each((e=>{const t=V(o,hD);e.getApis().setGroups(e,t)}))},setToolbars:(t,o)=>{yA.getPart(t,e,"multiple-toolbar").each((e=>{const t=V(o,(e=>V(e,hD)));CA.setItems(e,t)}))},refreshToolbar:t=>{yA.getPart(t,e,"toolbar").each((e=>e.getApis().refresh(e)))},toggleToolbarDrawer:t=>{yA.getPart(t,e,"toolbar").each((e=>{ke(e.getApis().toggle,(t=>t(e)))}))},toggleToolbarDrawerWithoutFocusing:t=>{yA.getPart(t,e,"toolbar").each((e=>{ke(e.getApis().toggleWithoutFocusing,(t=>t(e)))}))},isToolbarDrawerToggled:t=>yA.getPart(t,e,"toolbar").bind((e=>A.from(e.getApis().isOpen).map((t=>t(e))))).getOr(!1),getThrobber:t=>yA.getPart(t,e,"throbber"),focusToolbar:t=>{yA.getPart(t,e,"toolbar").orThunk((()=>yA.getPart(t,e,"multiple-toolbar"))).each((e=>{$p.focusIn(e)}))},setMenubar:(t,o)=>{yA.getPart(t,e,"menubar").each((e=>{wM.setMenus(e,o)}))},focusMenubar:t=>{yA.getPart(t,e,"menubar").each((e=>{wM.focus(e)}))},setViews:(t,o)=>{yA.getPart(t,e,"viewWrapper").each((e=>{RD.setViews(e,o)}))},toggleView:(t,o)=>yA.getPart(t,e,"viewWrapper").exists((e=>RD.toggleView(e,(()=>s.showMainView(t)),(()=>s.hideMainView(t)),o))),whichView:t=>yA.getPart(t,e,"viewWrapper").bind(RD.whichView).getOrNull(),hideMainView:t=>{n=s.isToolbarDrawerToggled(t),n&&s.toggleToolbarDrawer(t),yA.getPart(t,e,"editorContainer").each((e=>{const t=e.element;It(t,"display","none"),Ot(t,"aria-hidden","true")}))},showMainView:t=>{n&&s.toggleToolbarDrawer(t),yA.getPart(t,e,"editorContainer").each((e=>{const t=e.element;Pt(t,"display"),Mt(t,"aria-hidden")}))}};return{uid:e.uid,dom:e.dom,components:t,apis:s,behaviours:e.behaviours}},configFields:[ss("dom"),ss("behaviours")],partFields:[VD,ND,zD,LD,PD,UD,HD,WD,jD,GD],apis:{getSocket:(e,t)=>e.getSocket(t),setSidebar:(e,t,o,n)=>{e.setSidebar(t,o,n)},toggleSidebar:(e,t,o)=>{e.toggleSidebar(t,o)},whichSidebar:(e,t)=>e.whichSidebar(t),getHeader:(e,t)=>e.getHeader(t),getToolbar:(e,t)=>e.getToolbar(t),setToolbar:(e,t,o)=>{e.setToolbar(t,o)},setToolbars:(e,t,o)=>{e.setToolbars(t,o)},refreshToolbar:(e,t)=>e.refreshToolbar(t),toggleToolbarDrawer:(e,t)=>{e.toggleToolbarDrawer(t)},toggleToolbarDrawerWithoutFocusing:(e,t)=>{e.toggleToolbarDrawerWithoutFocusing(t)},isToolbarDrawerToggled:(e,t)=>e.isToolbarDrawerToggled(t),getThrobber:(e,t)=>e.getThrobber(t),setMenubar:(e,t,o)=>{e.setMenubar(t,o)},focusMenubar:(e,t)=>{e.focusMenubar(t)},focusToolbar:(e,t)=>{e.focusToolbar(t)},setViews:(e,t,o)=>{e.setViews(t,o)},toggleView:(e,t,o)=>e.toggleView(t,o),whichView:(e,t)=>e.whichView(t)}});const qD={file:{title:"File",items:"newdocument restoredraft | preview | importword exportpdf exportword | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code revisionhistory | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed inserttemplate codesample inserttable accordion | charmap emoticons hr | pagebreak nonbreaking anchor tableofcontents footnotes | mergetags | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | styles blocks fontfamily fontsize align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"aidialog aishortcuts | spellchecker spellcheckerlanguage | autocorrect capitalization | a11ycheck code typography wordcount addtemplate"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},YD=e=>e.split(" "),XD=(e,t)=>{const o={...qD,...t.menus},n=ae(t.menus).length>0,s=void 0===t.menubar||!0===t.menubar?YD("file edit view insert format tools table help"):YD(!1===t.menubar?"":t.menubar),a=U(s,(e=>{const o=ve(qD,e);return n?o||be(t.menus,e).exists((e=>ve(e,"items"))):o})),i=V(a,(n=>{const s=o[n];return((e,t,o)=>{const n=Ob(o).split(/[ ,]/);return{text:e.title,getItems:()=>Y(e.items,(e=>{const o=e.toLowerCase();return 0===o.trim().length||N(n,(e=>e===o))?[]:"separator"===o||"|"===o?[{type:"separator"}]:t.menuItems[o]?[t.menuItems[o]]:[]}))}})({title:s.title,items:YD(s.items)},t,e)}));return U(i,(e=>e.getItems().length>0&&N(e.getItems(),(e=>r(e)||"separator"!==e.type))))},KD=(e,t,o)=>(e.on("remove",(()=>o.unload(t))),o.load(t)),JD=(e,t,o,n)=>(e.on("remove",(()=>n.unloadRawCss(t))),n.loadRawCss(t,o)),ZD=async(e,t)=>{const o="ui/"+tv(e).getOr("default")+"/skin.css",n=tinymce.Resource.get(o);return r(n)?Promise.resolve(JD(e,o,n,e.ui.styleSheetLoader)):KD(e,t+"/skin.min.css",e.ui.styleSheetLoader)},QD=async(e,t)=>{var o;if(o=ze(e.getElement()),yt(o).isSome()){const o="ui/"+tv(e).getOr("default")+"/skin.shadowdom.css",n=tinymce.Resource.get(o);return r(n)?(JD(e,o,n,lb.DOM.styleSheetLoader),Promise.resolve()):KD(e,t+"/skin.shadowdom.min.css",lb.DOM.styleSheetLoader)}},eB=(e,t)=>(async(e,t)=>{tv(t).fold((()=>{const o=ev(t);o&&t.contentCSS.push(o+(e?"/content.inline":"/content")+".min.css")}),(o=>{const n="ui/"+o+(e?"/content.inline":"/content")+".css",s=tinymce.Resource.get(n);if(r(s))JD(t,n,s,t.ui.styleSheetLoader);else{const o=ev(t);o&&t.contentCSS.push(o+(e?"/content.inline":"/content")+".min.css")}}));const o=ev(t);if(!Zb(t)&&r(o))return Promise.all([ZD(t,o),QD(t,o)]).then()})(e,t).then((e=>{const t=()=>{e._skinLoaded=!0,(e=>{e.dispatch("SkinLoaded")})(e)};return()=>{e.initialized?t():e.on("init",t)}})(t),((e,t)=>()=>((e,t)=>{e.dispatch("SkinLoadError",t)})(e,{message:"Skin could not be loaded"}))(t)),tB=k(eB,!1),oB=k(eB,!0),nB=(e,t,o)=>Be(o)?e.translate(t):e.translate([t,e.translate(o)]),sB=(e,t)=>{const o=(o,s,r,a)=>{const i=e.shared.providers.translate(o.title);if("separator"===o.type)return A.some({type:"separator",text:i});if("submenu"===o.type){const e=Y(o.getStyleItems(),(e=>n(e,s,a)));return 0===s&&e.length<=0?A.none():A.some({type:"nestedmenuitem",text:i,enabled:e.length>0,getSubmenuItems:()=>Y(o.getStyleItems(),(e=>n(e,s,a)))})}return A.some({type:"togglemenuitem",text:i,icon:o.icon,active:o.isSelected(a),enabled:!r,onAction:t.onAction(o),...o.getStylePreview().fold((()=>({})),(e=>({meta:{style:e}})))})},n=(e,n,s)=>{const r="formatter"===e.type&&t.isInvalid(e);return 0===n?r?[]:o(e,n,!1,s).toArray():o(e,n,r,s).toArray()},s=e=>{const o=t.getCurrentValue(),s=t.shouldHide?0:1;return Y(e,(e=>n(e,s,o)))};return{validateItems:s,getFetch:(e,t)=>(o,n)=>{const r=t(),a=s(r);n(N_(a,fv.CLOSE_ON_EXECUTE,e,{isHorizontalMenu:!1,search:A.none()}))}}},rB=(e,t)=>{const o=t.dataset,n="basic"===o.type?()=>V(o.data,(e=>GE(e,t.isSelectedFor,t.getPreviewFor))):o.getData;return{items:sB(e,t),getStyleItems:n}},aB=(e,t,o,n,s,r)=>{const{items:a,getStyleItems:i}=rB(t,o),l=Ms(o.tooltip);return B_({text:o.icon.isSome()?A.none():o.text,icon:o.icon,ariaLabel:A.some(o.tooltip),tooltip:A.none(),role:A.none(),fetch:a.getFetch(t,i),onSetup:t=>{const r=o=>t.setTooltip(nB(e,n(o.value),o.value));return e.on(s,r),xw(kw(e,"NodeChange",(t=>{const n=t.getComponent();o.updateText(n),Hm.set(t.getComponent(),!e.selection.isEditable())}))(t),(()=>e.off(s,r)))},getApi:e=>({getComponent:x(e),setTooltip:o=>{const n=t.shared.providers.translate(o);Ot(e.element,"aria-label",n),l.set(o)}}),columns:1,presets:"normal",classes:o.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[wx.config({...t.shared.providers.tooltips.getConfig({tooltipText:t.shared.providers.translate(o.tooltip),onShow:e=>{if(o.tooltip!==l.get()){const o=t.shared.providers.translate(l.get());wx.setComponents(e,t.shared.providers.tooltips.getComponents({tooltipText:o}))}}})})]},"tox-tbtn",t.shared,r)};var iB;!function(e){e[e.SemiColon=0]="SemiColon",e[e.Space=1]="Space"}(iB||(iB={}));const lB=(e,t,o)=>{const n=(s=((e,t)=>t===iB.SemiColon?e.replace(/;$/,"").split(";"):e.split(" "))(e.options.get(t),o),V(s,(e=>{let t=e,o=e;const n=e.split("=");return n.length>1&&(t=n[0],o=n[1]),{title:t,format:o}})));var s;return{type:"basic",data:n}},cB=x("Alignment {0}"),dB="left",uB=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],mB=e=>{const t={type:"basic",data:uB};return{tooltip:nB(e,cB(),dB),text:A.none(),icon:A.some("align-left"),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:e=>A.none,onAction:t=>()=>G(uB,(e=>e.format===t.format)).each((t=>e.execCommand(t.command))),updateText:t=>{const o=G(uB,(t=>e.formatter.match(t.format))).fold(x(dB),(e=>e.title.toLowerCase()));Nr(t,D_,{icon:`align-${o}`}),((e,t)=>{e.dispatch("AlignTextUpdate",t)})(e,{value:o})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},gB=(e,t)=>{const o=t(),n=V(o,(e=>e.format));return A.from(e.formatter.closest(n)).bind((e=>G(o,(t=>t.format===e))))},pB=x("Block {0}"),hB="Paragraph",fB=e=>{const t=lB(e,"block_formats",iB.SemiColon);return{tooltip:nB(e,pB(),hB),text:A.some(hB),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:Cw(e),updateText:o=>{const n=gB(e,(()=>t.data)).fold(x(hB),(e=>e.title));Nr(o,M_,{text:n}),((e,t)=>{e.dispatch("BlocksTextUpdate",t)})(e,{value:n})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},bB=x("Font {0}"),vB="System Font",yB=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],xB=e=>{const t=e.split(/\s*,\s*/);return V(t,(e=>e.replace(/^['"]+|['"]+$/g,"")))},wB=(e,t)=>t.length>0&&X(t,(t=>e.indexOf(t.toLowerCase())>-1)),SB=e=>{const t=()=>{const t=e=>e?xB(e)[0]:"",n=e.queryCommandValue("FontName"),s=o.data,r=n?n.toLowerCase():"",a=Jb(e),i=G(s,(e=>{const o=e.format;return o.toLowerCase()===r||t(o).toLowerCase()===t(r).toLowerCase()})).orThunk((()=>Ce(((e,t)=>{if(0===e.indexOf("-apple-system")||t.length>0){const o=xB(e.toLowerCase());return wB(o,yB)||wB(o,t)}return!1})(r,a),{title:vB,format:r})));return{matchOpt:i,font:n}},o=lB(e,"font_family_formats",iB.SemiColon);return{tooltip:nB(e,bB(),vB),text:A.some(vB),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getCurrentValue:()=>{const{matchOpt:e}=t();return e},getPreviewFor:e=>()=>A.some({tag:"div",styles:-1===e.indexOf("dings")?{"font-family":e}:{}}),onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontName",!1,t.format)}))},updateText:o=>{const{matchOpt:n,font:s}=t(),r=n.fold(x(s),(e=>e.title));Nr(o,M_,{text:r}),((e,t)=>{e.dispatch("FontFamilyTextUpdate",t)})(e,{value:r})},dataset:o,shouldHide:!1,isInvalid:T}},kB={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},CB=(()=>{const e="[0-9]+",t="[eE][+-]?"+e,o=e=>`(?:${e})?`,n=["Infinity",e+"\\."+o(e)+o(t),"\\."+e+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),OB=(e,t)=>A.from(CB.exec(e)).bind((e=>{const o=Number(e[1]),n=e[2];return((e,t)=>N(t,(t=>N(kB[t],(t=>e===t)))))(n,t)?A.some({value:o,unit:n}):A.none()})),_B={tab:x(9),escape:x(27),enter:x(13),backspace:x(8),delete:x(46),left:x(37),up:x(38),right:x(39),down:x(40),space:x(32),home:x(36),end:x(35),pageUp:x(33),pageDown:x(34)},TB=x("Font size {0}"),EB="12pt",AB={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},MB={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},DB=(e,t)=>/[0-9.]+px$/.test(e)?((e,t)=>{const o=Math.pow(10,t);return Math.round(e*o)/o})(72*parseInt(e,10)/96,t||0)+"pt":be(MB,e).getOr(e),BB=e=>be(AB,e).getOr(""),IB=e=>{const t=()=>{let t=A.none();const o=n.data,s=e.queryCommandValue("FontSize");if(s)for(let e=3;t.isNone()&&e>=0;e--){const n=DB(s,e),r=BB(n);t=G(o,(e=>e.format===s||e.format===n||e.format===r))}return{matchOpt:t,size:s}},o=x(A.none),n=lB(e,"font_size_formats",iB.Space);return{tooltip:nB(e,TB(),EB),text:A.some(EB),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getPreviewFor:o,getCurrentValue:()=>{const{matchOpt:e}=t();return e},onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontSize",!1,t.format)}))},updateText:o=>{const{matchOpt:n,size:s}=t(),r=n.fold(x(s),(e=>e.title));Nr(o,M_,{text:r}),((e,t)=>{e.dispatch("FontSizeTextUpdate",t)})(e,{value:r})},dataset:n,shouldHide:!1,isInvalid:T}},FB=e=>Be(e)?"Formats":"Format {0}",RB=(e,t)=>{const o="Formats";return{tooltip:nB(e,FB(""),""),text:A.some(o),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:Cw(e),updateText:t=>{const n=e=>HE(e)?Y(e.items,n):PE(e)?[{title:e.title,format:e.format}]:[],s=Y(jE(e),n),r=gB(e,x(s)).fold(x({title:o,tooltipLabel:""}),(e=>({title:e.title,tooltipLabel:e.title})));Nr(t,M_,{text:r.title}),((e,t)=>{e.dispatch("StylesTextUpdate",t)})(e,{value:r.tooltipLabel})},shouldHide:kb(e),isInvalid:t=>!e.formatter.canApply(t.format),dataset:t}},NB=x([ss("toggleClass"),ss("fetch"),zi("onExecute"),ws("getHotspot",A.some),ws("getAnchorOverrides",x({})),_c(),zi("onItemExecute"),gs("lazySink"),ss("dom"),Ni("onOpen"),xu("splitDropdownBehaviours",[bS,$p,ih]),ws("matchWidth",!1),ws("useMinWidth",!1),ws("eventOrder",{}),gs("role")].concat(FS())),LB=qu({factory:Yh,schema:[ss("dom")],name:"arrow",defaults:()=>({buttonBehaviours:El([ih.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each(Lr)},buttonBehaviours:El([hh.config({toggleOnExecute:!1,toggleClass:e.toggleClass})])})}),zB=qu({factory:Yh,schema:[ss("dom")],name:"button",defaults:()=>({buttonBehaviours:El([ih.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each((o=>{e.onExecute(o,t)}))}})}),VB=x([LB,zB,Xu({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[ss("text")],name:"aria-descriptor"}),Yu({schema:[Ii()],name:"menu",defaults:e=>({onExecute:(t,o)=>{t.getSystem().getByUid(e.uid).each((n=>{e.onItemExecute(n,t,o)}))}})}),OS()]),HB=Sm({name:"SplitDropdown",configFields:NB(),partFields:VB(),factory:(e,t,o,n)=>{const s=e=>{_m.getCurrent(e).each((e=>{Km.highlightFirst(e),$p.focusIn(e)}))},r=t=>{AS(e,w,t,n,s,Wh.HighlightMenuAndItem).get(b)},a=t=>{const o=lm(t,e,"button");return Lr(o),A.some(!0)},i={...Pr([Zr(((t,o)=>{im(t,e,"aria-descriptor").each((e=>{const o=da("aria");Ot(e.element,"id",o),Ot(t.element,"aria-describedby",o)}))}))]),...bh(A.some(r))},l={repositionMenus:e=>{hh.isOn(e)&&IS(e)}};return{uid:e.uid,dom:e.dom,components:t,apis:l,eventOrder:{...e.eventOrder,[gr()]:["disabling","toggling","alloy.base.behaviour"]},events:i,behaviours:Su(e.splitDropdownBehaviours,[bS.config({others:{sandbox:t=>{const o=lm(t,e,"arrow");return BS(e,t,{onOpen:()=>{hh.on(o),hh.on(t)},onClose:()=>{hh.off(o),hh.off(t)}})}}}),$p.config({mode:"special",onSpace:a,onEnter:a,onDown:e=>(r(e),A.some(!0))}),ih.config({}),hh.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:e.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:(e,t)=>e.repositionMenus(t)}}),PB=e=>({isEnabled:()=>!Hm.isDisabled(e),setEnabled:t=>Hm.set(e,!t),setText:t=>Nr(e,M_,{text:t}),setIcon:t=>Nr(e,D_,{icon:t})}),UB=e=>({setActive:t=>{hh.set(e,t)},isActive:()=>hh.isOn(e),isEnabled:()=>!Hm.isDisabled(e),setEnabled:t=>Hm.set(e,!t),setText:t=>Nr(e,M_,{text:t}),setIcon:t=>Nr(e,D_,{icon:t})}),WB=(e,t)=>e.map((e=>({"aria-label":t.translate(e)}))).getOr({}),jB=da("focus-button"),GB=(e,t,o,n,s,r)=>{const a=t.map((e=>Kh(A_(e,"tox-tbtn",s)))),i=e.map((e=>Kh(E_(e,s.icons))));return{dom:{tag:"button",classes:["tox-tbtn"].concat(t.isSome()?["tox-tbtn--select"]:[]),attributes:{...WB(o,s),...g(r)?{"data-mce-name":r}:{}}},components:Rx([i.map((e=>e.asSpec())),a.map((e=>e.asSpec()))]),eventOrder:{[Gs()]:["focusing","alloy.base.behaviour",k_],[Cr()]:[k_,"toolbar-group-button-events"]},buttonBehaviours:El([Ax(s.isDisabled),_x(),oh(k_,[Zr(((e,t)=>O_(e))),jr(M_,((e,t)=>{a.bind((t=>t.getOpt(e))).each((e=>{th.set(e,[ai(s.translate(t.event.text))])}))})),jr(D_,((e,t)=>{i.bind((t=>t.getOpt(e))).each((e=>{th.set(e,[E_(t.event.icon,s.icons)])}))})),jr(Gs(),((e,t)=>{t.event.prevent(),Rr(e,jB)}))])].concat(n.getOr([])))}},$B=(e,t,o,n)=>{var s;const r=Ms(b),a=GB(e.icon,e.text,e.tooltip,A.none(),o,n);return Yh.sketch({dom:a.dom,components:a.components,eventOrder:C_,buttonBehaviours:{...El([oh("toolbar-button-events",[(i={onAction:e.onAction,getApi:t.getApi},ta(((e,t)=>{Mx(i,e)((t=>{Nr(e,S_,{buttonApi:t}),i.onAction(t)}))}))),Dx(t,r),Bx(t,r)]),...e.tooltip.map((t=>wx.config(o.tooltips.getConfig({tooltipText:o.translate(t)+e.shortcut.map((e=>` (${zx(e)})`)).getOr("")})))).toArray(),Ax((()=>!e.enabled||o.isDisabled())),_x()].concat(t.toolbarButtonBehaviours)),[k_]:null===(s=a.buttonBehaviours)||void 0===s?void 0:s[k_]}});var i},qB=(e,t,o,n)=>$B(e,{toolbarButtonBehaviours:o.length>0?[oh("toolbarButtonWith",o)]:[],getApi:PB,onSetup:e.onSetup},t,n),YB=(e,t,o,n)=>$B(e,{toolbarButtonBehaviours:[th.config({}),hh.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(o.length>0?[oh("toolbarToggleButtonWith",o)]:[]),getApi:UB,onSetup:e.onSetup},t,n),XB=(e,t,o)=>n=>SS((e=>t.fetch(e))).map((s=>A.from(GS(vn(sS(da("menu-value"),s,(o=>{t.onItemAction(e(n),o)}),t.columns,t.presets,fv.CLOSE_ON_EXECUTE,t.select.getOr(T),o),{movement:aS(t.columns,t.presets),menuBehaviours:dx("auto"!==t.columns?[]:[Zr(((e,o)=>{cx(e,4,Ev(t.presets)).each((({numRows:t,numColumns:o})=>{$p.setGridSize(e,t,o)}))}))])}))))),KB=[{name:"history",items:["undo","redo"]},{name:"ai",items:["aidialog","aishortcuts"]},{name:"styles",items:["styles"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],JB=(e,t)=>(o,n,s,r)=>{const a=e(o).mapError((e=>Zn(e))).getOrDie();return t(a,n,s,r)},ZB={button:JB(Ry,((e,t,o,n)=>((e,t,o)=>qB(e,t,[],o))(e,t.shared.providers,n))),togglebutton:JB(zy,((e,t,o,n)=>((e,t,o)=>YB(e,t,[],o))(e,t.shared.providers,n))),menubutton:JB(yM,((e,t,o,n)=>gT(e,"tox-tbtn",t,A.none(),!1,n))),splitbutton:JB((e=>Xn("SplitButton",xM,e)),((e,t,o,n)=>((e,t,o)=>{const n=Ms(e.tooltip.getOr("")),s=e=>({isEnabled:()=>!Hm.isDisabled(e),setEnabled:t=>Hm.set(e,!t),setIconFill:(t,o)=>{yi(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{Ot(e,"fill",o)}))},setActive:t=>{Ot(e.element,"aria-pressed",t),yi(e.element,"span").each((o=>{e.getSystem().getByDom(o).each((e=>hh.set(e,t)))}))},isActive:()=>yi(e.element,"span").exists((t=>e.getSystem().getByDom(t).exists(hh.isOn))),setText:t=>yi(e.element,"span").each((o=>e.getSystem().getByDom(o).each((e=>Nr(e,M_,{text:t}))))),setIcon:t=>yi(e.element,"span").each((o=>e.getSystem().getByDom(o).each((e=>Nr(e,D_,{icon:t}))))),setTooltip:o=>{const s=t.providers.translate(o);Ot(e.element,"aria-label",s),n.set(o)}}),r=Ms(b),a={getApi:s,onSetup:e.onSetup};return HB.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:{"aria-pressed":!1,...WB(e.tooltip,t.providers),...g(o)?{"data-mce-name":o}:{}}},onExecute:t=>{const o=s(t);o.isEnabled()&&e.onAction(o)},onItemExecute:(e,t,o)=>{},splitDropdownBehaviours:El([Ex(t.providers.isDisabled),_x(),oh("split-dropdown-events",[Zr(((e,t)=>O_(e))),jr(jB,ih.focus),Dx(a,r),Bx(a,r)]),Uk.config({}),...e.tooltip.map((e=>wx.config({...t.providers.tooltips.getConfig({tooltipText:t.providers.translate(e),onShow:o=>{if(n.get()!==e){const e=t.providers.translate(n.get());wx.setComponents(o,t.providers.tooltips.getComponents({tooltipText:e}))}}})}))).toArray()]),eventOrder:{[Cr()]:["alloy.base.behaviour","split-dropdown-events","tooltipping"],[Or()]:["split-dropdown-events","tooltipping"]},toggleClass:"tox-tbtn--enabled",lazySink:t.getSink,fetch:XB(s,e,t.providers),parts:{menu:Fv(0,e.columns,e.presets)},components:[HB.parts.button(GB(e.icon,e.text,A.none(),A.some([hh.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),t.providers)),HB.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:eb("chevron-down",t.providers.icons)},buttonBehaviours:El([Ex(t.providers.isDisabled),_x(),tb()])}),HB.parts["aria-descriptor"]({text:t.providers.translate("To open the popup, press Shift+Enter")})]})})(e,t.shared,n))),grouptoolbarbutton:JB((e=>Xn("GroupToolbarButton",fM,e)),((e,t,o,n)=>{const s=o.ui.registry.getAll().buttons,r={[Cc]:t.shared.header.isPositionedAtTop()?kc.TopToBottom:kc.BottomToTop};if(_b(o)===ab.floating)return((e,t,o,n,s)=>{const r=t.shared,a=Ms(b),i={toolbarButtonBehaviours:[],getApi:PB,onSetup:e.onSetup},l=[oh("toolbar-group-button-events",[Dx(i,a),Bx(i,a)])];return oD.sketch({lazySink:r.getSink,fetch:()=>SS((t=>{t(V(o(e.items),hD))})),markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:GB(e.icon,e.text,e.tooltip,A.some(l),r.providers,s),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:n}}}})})(e,t,(e=>eI(o,{buttons:s,toolbar:e,allowToolbarGroups:!1},t,A.none())),r,n);throw new Error("Toolbar groups are only supported when using floating toolbar mode")}))},QB={styles:(e,t)=>{const o={type:"advanced",...t.styles};return aB(e,t,RB(e,o),FB,"StylesTextUpdate","styles")},fontsize:(e,t)=>aB(e,t,IB(e),TB,"FontSizeTextUpdate","fontsize"),fontsizeinput:(e,t)=>((e,t,o,n)=>{let s=A.none();const r=kw(e,"NodeChange SwitchMode",(t=>{const n=t.getComponent();s=A.some(n),o.updateInputValue(n),Hm.set(n,!e.selection.isEditable())})),a=e=>({getComponent:x(e)}),i=Ms(b),l=da("custom-number-input-events"),c=(e,t,n)=>{const r=s.map((e=>yu.getValue(e))).getOr(""),a=o.getNewValue(r,e),i=r.length-`${a}`.length,l=s.map((e=>e.element.dom.selectionStart-i)),c=s.map((e=>e.element.dom.selectionEnd-i));o.onAction(a,n),s.each((e=>{yu.setValue(e,a),t&&(l.each((t=>e.element.dom.selectionStart=t)),c.each((t=>e.element.dom.selectionEnd=t)))}))},d=(e,t)=>c(((e,t)=>e-t),e,t),u=(e,t)=>c(((e,t)=>e+t),e,t),m=e=>it(e.element).fold(A.none,(e=>(Nl(e),A.some(!0)))),p=e=>zl(e.element)?(ut(e.element).each((e=>Nl(e))),A.some(!0)):A.none(),h=(o,n,s,r)=>{const i=Ms(b),l=t.shared.providers.translate(s),c=da("altExecuting"),d=kw(e,"NodeChange SwitchMode",(t=>{Hm.set(t.getComponent(),!e.selection.isEditable())})),u=e=>{Hm.isDisabled(e)||o(!0)};return Yh.sketch({dom:{tag:"button",attributes:{"aria-label":l,"data-mce-name":n},classes:r.concat(n)},components:[T_(n,t.shared.providers.icons)],buttonBehaviours:El([Hm.config({}),wx.config(t.shared.providers.tooltips.getConfig({tooltipText:l})),oh(c,[Dx({onSetup:d,getApi:a},i),Bx({getApi:a},i),jr(Zs(),((e,t)=>{t.event.raw.keyCode!==_B.space()&&t.event.raw.keyCode!==_B.enter()||Hm.isDisabled(e)||o(!1)})),jr(or(),u),jr(Ws(),u)])]),eventOrder:{[Zs()]:[c,"keying"],[or()]:[c,"alloy.base.behaviour"],[Ws()]:[c,"alloy.base.behaviour"],[Cr()]:["alloy.base.behaviour",c,"tooltipping"],[Or()]:[c,"tooltipping"]}})},f=Kh(h((e=>d(!1,e)),"minus","Decrease font size",[])),v=Kh(h((e=>u(!1,e)),"plus","Increase font size",[])),y=Kh({dom:{tag:"div",classes:["tox-input-wrapper"]},components:[Vv.sketch({inputBehaviours:El([Hm.config({}),oh(l,[Dx({onSetup:r,getApi:a},i),Bx({getApi:a},i)]),oh("input-update-display-text",[jr(M_,((e,t)=>{yu.setValue(e,t.event.text)})),jr(Js(),(e=>{o.onAction(yu.getValue(e))})),jr(tr(),(e=>{o.onAction(yu.getValue(e))}))]),$p.config({mode:"special",onEnter:e=>(c(w,!0,!0),A.some(!0)),onEscape:m,onUp:e=>(u(!0,!1),A.some(!0)),onDown:e=>(d(!0,!1),A.some(!0)),onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())})])})],behaviours:El([ih.config({}),$p.config({mode:"special",onEnter:p,onSpace:p,onEscape:m}),oh("input-wrapper-events",[jr(Xs(),(e=>{H([f,v],(t=>{const o=ze(t.get(e).element.dom);zl(o)&&Ll(o)}))}))])])});return{dom:{tag:"div",classes:["tox-number-input"],attributes:{...g(n)?{"data-mce-name":n}:{}}},components:[f.asSpec(),y.asSpec(),v.asSpec()],behaviours:El([ih.config({}),$p.config({mode:"flow",focusInside:yg.OnEnterOrSpaceMode,cycles:!1,selector:"button, .tox-input-wrapper",onEscape:e=>zl(e.element)?A.none():(Nl(e.element),A.some(!0))})])}})(e,t,(e=>{const t=()=>e.queryCommandValue("FontSize");return{updateInputValue:e=>Nr(e,M_,{text:t()}),onAction:(t,o)=>e.execCommand("FontSize",!1,t,{skip_focus:!o}),getNewValue:(o,n)=>{OB(o,["unsupportedLength","empty"]);const s=t(),r=OB(o,["unsupportedLength","empty"]).or(OB(s,["unsupportedLength","empty"])),a=r.map((e=>e.value)).getOr(16),i=Lb(e),l=r.map((e=>e.unit)).filter((e=>""!==e)).getOr(i),c=n(a,(e=>{var t;return null!==(t={em:{step:.1},cm:{step:.1},in:{step:.1},pc:{step:.1},ch:{step:.1},rem:{step:.1}}[e])&&void 0!==t?t:{step:1}})(l).step),d=`${(e=>e>=0)(c)?c:a}${l}`;return d!==s&&((e,t)=>{e.dispatch("FontSizeInputTextUpdate",t)})(e,{value:d}),d}}})(e),"fontsizeinput"),fontfamily:(e,t)=>aB(e,t,SB(e),bB,"FontFamilyTextUpdate","fontfamily"),blocks:(e,t)=>aB(e,t,fB(e),pB,"BlocksTextUpdate","blocks"),align:(e,t)=>aB(e,t,mB(e),cB,"AlignTextUpdate","align")},eI=(e,t,o,n)=>{const s=(e=>{const t=e.toolbar,o=e.buttons;return!1===t?[]:void 0===t||!0===t?(e=>{const t=V(KB,(t=>{const o=U(t.items,(t=>ve(e,t)||ve(QB,t)));return{name:t.name,items:o}}));return U(t,(e=>e.items.length>0))})(o):r(t)?(e=>{const t=e.split("|");return V(t,(e=>({items:e.trim().split(" ")})))})(t):(e=>f(e,(e=>ve(e,"name")&&ve(e,"items"))))(t)?t:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])})(t),a=V(s,(s=>{const r=Y(s.items,(s=>0===s.trim().length?[]:((e,t,o,n,s,r)=>be(t,o.toLowerCase()).orThunk((()=>r.bind((e=>re(e,(e=>be(t,e+o.toLowerCase()))))))).fold((()=>be(QB,o.toLowerCase()).map((t=>t(e,s)))),(t=>"grouptoolbarbutton"!==t.type||n?((e,t,o,n)=>be(ZB,e.type).fold((()=>(console.error("skipping button defined by",e),A.none())),(s=>A.some(s(e,t,o,n)))))(t,s,e,o.toLowerCase()):(console.warn(`Ignoring the '${o}' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested.`),A.none()))))(e,t.buttons,s,t.allowToolbarGroups,o,n).toArray()));return{title:A.from(e.translate(s.name)),items:r}}));return U(a,(e=>e.items.length>0))},tI=(e,t,o,n)=>{const s=t.mainUi.outerContainer,a=o.toolbar,i=o.buttons;if(f(a,r)){const t=a.map((t=>{const s={toolbar:t,buttons:i,allowToolbarGroups:o.allowToolbarGroups};return eI(e,s,n,A.none())}));$D.setToolbars(s,t)}else $D.setToolbar(s,eI(e,o,n,A.none()))},oI=Io(),nI=oI.os.isiOS()&&oI.os.version.major<=12;var sI=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=t,i=Ms(0),l=r.outerContainer;tB(e);const d=ze(s.targetNode),u=vt(bt(d));Hd(d,r.mothership),((e,t,o)=>{mv(e)&&Hd(o.mainUi.mothership.element,o.popupUi.mothership),Vd(t,o.dialogUi.mothership)})(e,u,t),e.on("SkinLoaded",(()=>{$D.setSidebar(l,o.sidebar,Yb(e)),tI(e,t,o,n),i.set(e.getWin().innerWidth),$D.setMenubar(l,XD(e,o)),$D.setViews(l,o.views),((e,t)=>{const{uiMotherships:o}=t,n=e.dom;let s=e.getWin();const r=e.getDoc().documentElement,a=Ms(Yt(s.innerWidth,s.innerHeight)),i=Ms(Yt(r.offsetWidth,r.offsetHeight)),l=()=>{const t=a.get();t.left===s.innerWidth&&t.top===s.innerHeight||(a.set(Yt(s.innerWidth,s.innerHeight)),bw(e))},c=()=>{const t=e.getDoc().documentElement,o=i.get();o.left===t.offsetWidth&&o.top===t.offsetHeight||(i.set(Yt(t.offsetWidth,t.offsetHeight)),bw(e))},d=t=>{((e,t)=>{e.dispatch("ScrollContent",t)})(e,t)};n.bind(s,"resize",l),n.bind(s,"scroll",d);const u=ic(ze(e.getBody()),"load",c);e.on("hide",(()=>{H(o,(e=>{It(e.element,"display","none")}))})),e.on("show",(()=>{H(o,(e=>{Pt(e.element,"display")}))})),e.on("NodeChange",c),e.on("remove",(()=>{u.unbind(),n.unbind(s,"resize",l),n.unbind(s,"scroll",d),s=null}))})(e,t)}));const m=$D.getSocket(l).getOrDie("Could not find expected socket element");if(nI){Ft(m.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"});const t=((e,t)=>{let o=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null)},throttle:(...t)=>{c(o)&&(o=setTimeout((()=>{o=null,e.apply(null,t)}),20))}}})((()=>{e.dispatch("ScrollContent")})),o=ac(m.element,"scroll",t.throttle);e.on("remove",o.unbind)}Ox(e,t),e.addCommand("ToggleSidebar",((t,o)=>{$D.toggleSidebar(l,o),e.dispatch("ToggleSidebar")})),e.addQueryValueHandler("ToggleSidebar",(()=>{var e;return null!==(e=$D.whichSidebar(l))&&void 0!==e?e:""})),e.addCommand("ToggleView",((t,o)=>{if($D.toggleView(l,o)){const t=l.element;r.mothership.broadcastOn([eu()],{target:t}),H(a,(e=>{e.broadcastOn([eu()],{target:t})})),c($D.whichView(l))&&(e.focus(),e.nodeChanged(),$D.refreshToolbar(l))}})),e.addQueryValueHandler("ToggleView",(()=>{var e;return null!==(e=$D.whichView(l))&&void 0!==e?e:""}));const g=_b(e);g!==ab.sliding&&g!==ab.floating||e.on("ResizeWindow ResizeEditor ResizeContent",(()=>{const o=e.getWin().innerWidth;o!==i.get()&&($D.refreshToolbar(t.mainUi.outerContainer),i.set(o))}));const p={setEnabled:e=>{Cx(t,!e)},isEnabled:()=>!Hm.isDisabled(l)};return{iframeContainer:m.element.dom,editorContainer:l.element.dom,api:p}}});const rI=e=>/^[0-9\.]+(|px)$/i.test(""+e)?A.some(parseInt(""+e,10)):A.none(),aI=e=>h(e)?e+"px":e,iI=(e,t,o)=>{const n=t.filter((t=>e<t)),s=o.filter((t=>e>t));return n.or(s).getOr(e)},lI=e=>{const t=fb(e),o=bb(e),n=yb(e);return rI(t).map((e=>iI(e,o,n)))},{ToolbarLocation:cI,ToolbarMode:dI}=pv,uI=(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=o,i=lb.DOM,l=lv(e),c=uv(e),d=yb(e).or(lI(e)),u=n.shared.header,m=u.isPositionedAtTop,g=_b(e),p=g===dI.sliding||g===dI.floating,h=Ms(!1),f=()=>h.get()&&!e.removed,b=e=>p?e.fold(x(0),(e=>e.components().length>1?Gt(e.components()[1].element):0)):0,v=()=>{H(a,(e=>{e.broadcastOn([tu()],{})}))},y=o=>{if(!f())return;l||s.on((e=>{const o=d.getOrThunk((()=>{const e=rI(Nt(St(),"margin-left")).getOr(0);return Qt(St())-Kt(t).left+e}));It(e.element,"max-width",o+"px")}));const n=!(l||l||!(Kt(r.outerContainer.element).left+eo(r.outerContainer.element)>=window.innerWidth-40||zt(r.outerContainer.element,"width").isSome())||(It(r.outerContainer.element,"position","absolute"),It(r.outerContainer.element,"left","0px"),Pt(r.outerContainer.element,"width"),0));p&&$D.refreshToolbar(r.outerContainer),l||(o=>{s.on((n=>{const s=$D.getToolbar(r.outerContainer),a=b(s),i=Qo(t),l=((e,t)=>mv(e)?IA(t):A.none())(e,r.outerContainer.element),c=l.fold((()=>i.x),(e=>{const t=Qo(e);return et(e,St())?i.x:i.x-t.x})),d=Ce(o,Math.ceil(r.outerContainer.element.dom.getBoundingClientRect().width)).filter((e=>e>150)).map((e=>{const t=jo(),o=window.innerWidth-(c-t.left),n=Math.max(Math.min(e,o),150);return o<e&&It(r.outerContainer.element,"width",n+"px"),{width:n+"px"}})).getOr({}),u={position:"absolute",left:Math.round(c)+"px",top:l.fold((()=>m()?Math.max(i.y-Gt(n.element)+a,0):i.bottom),(e=>{var t;const o=Qo(e),s=null!==(t=e.dom.scrollTop)&&void 0!==t?t:0,r=et(e,St())?Math.max(i.y-Gt(n.element)+a,0):i.y-o.y+s-Gt(n.element)+a;return m()?r:i.bottom}))+"px"};Ft(r.outerContainer.element,{...u,...d})}))})(n),c&&s.on(o),v()},w=()=>!(l||!c||!f())&&s.get().exists((o=>{const n=u.getDockingMode(),a=(o=>{switch(Eb(e)){case cI.auto:const e=$D.getToolbar(r.outerContainer),n=b(e),s=Gt(o.element)-n,a=Qo(t);if(a.y>s)return"top";{const e=st(t),o=Math.max(e.dom.scrollHeight,Gt(e));return a.bottom<o-s||on().bottom<a.bottom-s?"bottom":"top"}case cI.bottom:return"bottom";case cI.top:default:return"top"}})(o);return a!==n&&(i=a,s.on((e=>{rM.setModes(e,[i]),u.setDockingMode(i);const t=m()?kc.TopToBottom:kc.BottomToTop;Ot(e.element,Cc,t)})),!0);var i}));return{isVisible:f,isPositionedAtTop:m,show:()=>{h.set(!0),It(r.outerContainer.element,"display","flex"),i.addClass(e.getBody(),"mce-edit-focus"),H(a,(e=>{Pt(e.element,"display")})),w(),mv(e)?y((e=>rM.isDocked(e)?rM.reset(e):rM.refresh(e))):y(rM.refresh)},hide:()=>{h.set(!1),It(r.outerContainer.element,"display","none"),i.removeClass(e.getBody(),"mce-edit-focus"),H(a,(e=>{It(e.element,"display","none")}))},update:y,updateMode:()=>{w()&&y(rM.reset)},repositionPopups:v}},mI=(e,t)=>{const o=Qo(e);return{pos:t?o.y:o.bottom,bounds:o}};var gI=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mainUi:r}=t,a=sc(),i=ze(s.targetNode),l=uI(e,i,t,n,a),c=Db(e);oB(e);const d=()=>{if(a.isSet())return void l.show();a.set($D.getHeader(r.outerContainer).getOrDie());const s=cv(e);mv(e)?(Hd(i,r.mothership),Hd(i,t.popupUi.mothership)):Vd(s,r.mothership),Vd(s,t.dialogUi.mothership);const d=()=>{tI(e,t,o,n),$D.setMenubar(r.outerContainer,XD(e,o)),l.show(),((e,t,o,n)=>{const s=Ms(mI(t,o.isPositionedAtTop())),r=n=>{const{pos:r,bounds:a}=mI(t,o.isPositionedAtTop()),{pos:i,bounds:l}=s.get(),c=a.height!==l.height||a.width!==l.width;s.set({pos:r,bounds:a}),c&&bw(e,n),o.isVisible()&&(i!==r?o.update(rM.reset):c&&(o.updateMode(),o.repositionPopups()))};n||(e.on("activate",o.show),e.on("deactivate",o.hide)),e.on("SkinLoaded ResizeWindow",(()=>o.update(rM.reset))),e.on("NodeChange keydown",(e=>{requestAnimationFrame((()=>r(e)))}));let a=0;const i=QO((()=>o.update(rM.refresh)),33);e.on("ScrollWindow",(()=>{const e=jo().left;e!==a&&(a=e,i.throttle()),o.updateMode()})),mv(e)&&e.on("ElementScroll",(e=>{o.update(rM.refresh)}));const l=nc();l.set(ic(ze(e.getBody()),"load",(e=>r(e.raw)))),e.on("remove",(()=>{l.clear()}))})(e,i,l,c),e.nodeChanged()};c?e.once("SkinLoaded",d):d()};e.on("show",d),e.on("hide",l.hide),c||(e.on("focus",d),e.on("blur",l.hide)),e.on("init",(()=>{(e.hasFocus()||c)&&d()})),Ox(e,t);const u={show:d,hide:l.hide,setEnabled:e=>{Cx(t,!e)},isEnabled:()=>!Hm.isDisabled(r.outerContainer)};return{editorContainer:r.outerContainer.element.dom,api:u}}});const pI="contexttoolbar-hide",hI=(e,t)=>jr(S_,((o,n)=>{const s=(e=>({hide:()=>Rr(e,br()),getValue:()=>yu.getValue(e)}))(e.get(o));t.onAction(s,n.event.buttonApi)})),fI=(e,t)=>{const o=e.label.fold((()=>({})),(e=>({"aria-label":e}))),n=Kh(Vv.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:e.initValue(),inputAttributes:o,selectOnFocus:!0,inputBehaviours:El([$p.config({mode:"special",onEnter:e=>s.findPrimary(e).map((e=>(Lr(e),!0))),onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())})])})),s=((e,t,o)=>{const n=V(t,(t=>Kh(((e,t,o)=>(e=>"contextformtogglebutton"===e.type)(t)?((e,t,o)=>{const{primary:n,...s}=t.original,r=Kn(zy({...s,type:"togglebutton",onAction:b}));return YB(r,o,[hI(e,t)])})(e,t,o):((e,t,o)=>{const{primary:n,...s}=t.original,r=Kn(Ry({...s,type:"button",onAction:b}));return qB(r,o,[hI(e,t)])})(e,t,o))(e,t,o))));return{asSpecs:()=>V(n,(e=>e.asSpec())),findPrimary:e=>re(t,((t,o)=>t.primary?A.from(n[o]).bind((t=>t.getOpt(e))).filter(C(Hm.isDisabled)):A.none()))}})(n,e.commands,t);return[{title:A.none(),items:[n.asSpec()]},{title:A.none(),items:s.asSpecs()}]},bI=(e,t,o)=>t.bottom-e.y>=o&&e.bottom-t.y>=o,vI=e=>{const t=(e=>{const t=e.getBoundingClientRect();if(t.height<=0&&t.width<=0){const o=gt(ze(e.startContainer),e.startOffset).element;return(qe(o)?at(o):A.some(o)).filter($e).map((e=>e.dom.getBoundingClientRect())).getOr(t)}return t})(e.selection.getRng());if(e.inline){const e=jo();return Zo(e.left+t.left,e.top+t.top,t.width,t.height)}{const o=en(ze(e.getBody()));return Zo(o.x+t.left,o.y+t.top,t.width,t.height)}},yI=(e,t,o,n=0)=>{const s=qo(window),r=Qo(ze(e.getContentAreaContainer())),a=Qb(e)||nv(e)||rv(e),{x:i,width:l}=((e,t,o)=>{const n=Math.max(e.x+o,t.x);return{x:n,width:Math.min(e.right-o,t.right)-n}})(r,s,n);if(e.inline&&!a)return Zo(i,s.y,l,s.height);{const a=t.header.isPositionedAtTop(),{y:c,bottom:d}=((e,t,o,n,s,r)=>{const a=ze(e.getContainer()),i=yi(a,".tox-editor-header").getOr(a),l=Qo(i),c=l.y>=t.bottom,d=n&&!c;if(e.inline&&d)return{y:Math.max(l.bottom+r,o.y),bottom:o.bottom};if(e.inline&&!d)return{y:o.y,bottom:Math.min(l.y-r,o.bottom)};const u="line"===s?Qo(a):t;return d?{y:Math.max(l.bottom+r,o.y),bottom:Math.min(u.bottom-r,o.bottom)}:{y:Math.max(u.y+r,o.y),bottom:Math.min(l.y-r,o.bottom)}})(e,r,s,a,o,n);return Zo(i,c,l,d-c)}},xI={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"],inset:["tox-pop--inset"]},wI={maxHeightFunction:pc(),maxWidthFunction:XM()},SI=e=>"node"===e,kI=(e,t,o,n,s)=>{const r=vI(e),a=n.lastElement().exists((e=>et(o,e)));return((e,t)=>{const o=e.selection.getRng(),n=gt(ze(o.startContainer),o.startOffset);return o.startContainer===o.endContainer&&o.startOffset===o.endOffset-1&&et(n.element,t)})(e,o)?a?OE:xE:a?((e,o,s)=>{const a=zt(e,"position");It(e,"position",o);const i=bI(r,Qo(t),-20)&&!n.isReposition()?TE:OE;return a.each((t=>It(e,"position",t))),i})(t,n.getMode()):("fixed"===n.getMode()?s.y+jo().top:s.y)+(Gt(t)+12)<=r.y?xE:wE},CI=(e,t,o,n)=>{const s=t=>(n,s,r,a,i)=>({...kI(e,a,t,o,i)({...n,y:i.y,height:i.height},s,r,a,i),alwaysFit:!0}),r=e=>SI(n)?[s(e)]:[];return t?{onLtr:e=>[pl,cl,dl,ul,ml,gl].concat(r(e)),onRtl:e=>[pl,dl,cl,ml,ul,gl].concat(r(e))}:{onLtr:e=>[gl,pl,ul,cl,ml,dl].concat(r(e)),onRtl:e=>[gl,pl,ml,dl,ul,cl].concat(r(e))}},OI=(e,t)=>{const o=U(t,(t=>t.predicate(e.dom))),{pass:n,fail:s}=P(o,(e=>"contexttoolbar"===e.type));return{contextToolbars:n,contextForms:s}},_I=(e,t)=>{const o={},n=[],s=[],r={},a={},i=ae(e);return H(i,(i=>{const l=e[i];"contextform"===l.type?((e,i)=>{const l=Kn(Xn("ContextForm",Gy,i));o[e]=l,l.launch.map((o=>{r["form:"+e]={...i.launch,type:"contextformtogglebutton"===o.type?"togglebutton":"button",onAction:()=>{t(l)}}})),"editor"===l.scope?s.push(l):n.push(l),a[e]=l})(i,l):"contexttoolbar"===l.type&&((e,t)=>{var o;(o=t,Xn("ContextToolbar",$y,o)).each((o=>{"editor"===t.scope?s.push(o):n.push(o),a[e]=o}))})(i,l)})),{forms:o,inNodeScope:n,inEditorScope:s,lookupTable:a,formNavigators:r}},TI=da("forward-slide"),EI=da("backward-slide"),AI=da("change-slide-event"),MI="tox-pop--resizing",DI="tox-pop--transition",BI=(e,t,o,n)=>{const s=n.backstage,r=s.shared,a=Io().deviceType.isTouch,i=sc(),l=sc(),c=sc(),d=di((e=>{const t=Ms([]);return $h.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:e=>{t.set([]),$h.getContent(e).each((e=>{Pt(e.element,"visibility")})),$a(e.element,MI),Pt(e.element,"width")},inlineBehaviours:El([oh("context-toolbar-events",[Jr(sr(),((e,t)=>{"width"===t.event.raw.propertyName&&($a(e.element,MI),Pt(e.element,"width"))})),jr(AI,((e,t)=>{const o=e.element;Pt(o,"width");const n=Qt(o);$h.setContent(e,t.event.contents),ja(o,MI);const s=Qt(o);It(o,"width",n+"px"),$h.getContent(e).each((e=>{t.event.focus.bind((e=>(Nl(e),Hl(o)))).orThunk((()=>($p.focusIn(e),Vl(bt(o)))))})),setTimeout((()=>{It(e.element,"width",s+"px")}),0)})),jr(TI,((e,o)=>{$h.getContent(e).each((o=>{t.set(t.get().concat([{bar:o,focus:Vl(bt(e.element))}]))})),Nr(e,AI,{contents:o.event.forwardContents,focus:A.none()})})),jr(EI,((e,o)=>{ne(t.get()).each((o=>{t.set(t.get().slice(0,t.get().length-1)),Nr(e,AI,{contents:ui(o.bar),focus:o.focus})}))}))]),$p.config({mode:"special",onEscape:o=>ne(t.get()).fold((()=>e.onEscape()),(e=>(Rr(o,EI),A.some(!0))))})]),lazySink:()=>an.value(e.sink)})})({sink:o,onEscape:()=>(e.focus(),A.some(!0))})),u=()=>{const t=c.get().getOr("node"),o=SI(t)?1:0;return yI(e,r,t,o)},m=()=>!(e.removed||a()&&s.isContextMenuOpen()),g=()=>{if(m()){const t=u(),o=xe(c.get(),"node")?((e,t)=>t.filter((e=>wt(e)&&Ge(e))).map(en).getOrThunk((()=>vI(e))))(e,i.get()):vI(e);return t.height<=0||!bI(o,t,.01)}return!0},p=()=>{i.clear(),l.clear(),c.clear(),$h.hide(d)},h=()=>{if($h.isOpen(d)){const e=d.element;Pt(e,"display"),g()?It(e,"display","none"):(l.set(0),$h.reposition(d))}},f=t=>({dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:El([$p.config({mode:"acyclic"}),oh("pop-dialog-wrap-events",[Zr((t=>{e.shortcuts.add("ctrl+F9","focus statusbar",(()=>$p.focusIn(t)))})),Qr((t=>{e.shortcuts.remove("ctrl+F9")}))])])}),v=to((()=>_I(t,(e=>{const t=y([e]);Nr(d,TI,{forwardContents:f(t)})})))),y=t=>{const{buttons:o}=e.ui.registry.getAll(),s={...o,...v().formNavigators},a=_b(e)===ab.scrolling?ab.scrolling:ab.default,i=q(V(t,(t=>"contexttoolbar"===t.type?((t,o)=>eI(e,{buttons:t,toolbar:o.items,allowToolbarGroups:!1},n.backstage,A.some(["form:"])))(s,t):((e,t)=>fI(e,t))(t,r.providers))));return xD({type:a,uid:da("context-toolbar"),initGroups:i,onEscape:A.none,cyclicKeying:!0,providers:r.providers})},x=(t,n)=>{if(S.cancel(),!m())return;const s=y(t),p=t[0].position,h=((t,n)=>{const s="node"===t?r.anchors.node(n):r.anchors.cursor(),c=((e,t,o,n)=>"line"===t?{bubble:vc(12,0,xI),layouts:{onLtr:()=>[hl],onRtl:()=>[fl]},overrides:wI}:{bubble:vc(0,12,xI,1/12),layouts:CI(e,o,n,t),overrides:wI})(e,t,a(),{lastElement:i.get,isReposition:()=>xe(l.get(),0),getMode:()=>Td.getMode(o)});return vn(s,c)})(p,n);c.set(p),l.set(1);const b=d.element;Pt(b,"display"),(e=>xe(Se(e,i.get(),et),!0))(n)||($a(b,DI),Td.reset(o,d)),$h.showWithinBounds(d,f(s),{anchor:h,transition:{classes:[DI],mode:"placement"}},(()=>A.some(u()))),n.fold(i.clear,i.set),g()&&It(b,"display","none")};let w=!1;const S=QO((()=>{!e.hasFocus()||e.removed||w||(qa(d.element,DI)?S.throttle():((e,t)=>{const o=ze(t.getBody()),n=e=>et(e,o),s=ze(t.selection.getNode());return(e=>!n(e)&&!tt(o,e))(s)?A.none():((e,t,o)=>{const n=OI(e,t);if(n.contextForms.length>0)return A.some({elem:e,toolbars:[n.contextForms[0]]});{const t=OI(e,o);if(t.contextForms.length>0)return A.some({elem:e,toolbars:[t.contextForms[0]]});if(n.contextToolbars.length>0||t.contextToolbars.length>0){const o=(e=>{if(e.length<=1)return e;{const t=t=>N(e,(e=>e.position===t)),o=t=>U(e,(e=>e.position===t)),n=t("selection"),s=t("node");if(n||s){if(s&&n){const e=o("node"),t=V(o("selection"),(e=>({...e,position:"node"})));return e.concat(t)}return o(n?"selection":"node")}return o("line")}})(n.contextToolbars.concat(t.contextToolbars));return A.some({elem:e,toolbars:o})}return A.none()}})(s,e.inNodeScope,e.inEditorScope).orThunk((()=>((e,t,o)=>e(t)?A.none():Rs(t,(e=>{if($e(e)){const{contextToolbars:t,contextForms:n}=OI(e,o.inNodeScope),s=n.length>0?n:(e=>{if(e.length<=1)return e;{const t=t=>G(e,(e=>e.position===t));return t("selection").orThunk((()=>t("node"))).orThunk((()=>t("line"))).map((e=>e.position)).fold((()=>[]),(t=>U(e,(e=>e.position===t))))}})(t);return s.length>0?A.some({elem:e,toolbars:s}):A.none()}return A.none()}),e))(n,s,e)))})(v(),e).fold(p,(e=>{x(e.toolbars,A.some(e.elem))})))}),17);e.on("init",(()=>{e.on("remove",p),e.on("ScrollContent ScrollWindow ObjectResized ResizeEditor longpress",h),e.on("click keyup focus SetContent",S.throttle),e.on(pI,p),e.on("contexttoolbar-show",(t=>{const o=v();be(o.lookupTable,t.toolbarKey).each((o=>{x([o],Ce(t.target!==e,t.target)),$h.getContent(d).each($p.focusIn)}))})),e.on("focusout",(t=>{qh.setEditorTimeout(e,(()=>{Hl(o.element).isNone()&&Hl(d.element).isNone()&&p()}),0)})),e.on("SwitchMode",(()=>{e.mode.isReadOnly()&&p()})),e.on("ExecCommand",(({command:e})=>{"toggleview"===e.toLowerCase()&&p()})),e.on("AfterProgressState",(t=>{t.state?p():e.hasFocus()&&S.throttle()})),e.on("dragstart",(()=>{w=!0})),e.on("dragend drop",(()=>{w=!1})),e.on("NodeChange",(e=>{Hl(d.element).fold(S.throttle,b)}))}))},II=(e,t)=>{const o=()=>{const o=t.getOptions(e),n=t.getCurrent(e).map(t.hash),s=sc();return V(o,(o=>({type:"togglemenuitem",text:t.display(o),onSetup:r=>{const a=e=>{e&&(s.on((e=>e.setActive(!1))),s.set(r)),r.setActive(e)};a(xe(n,t.hash(o)));const i=t.watcher(e,o,a);return()=>{s.clear(),i()}},onAction:()=>t.setCurrent(e,o)})))};e.ui.registry.addMenuButton(t.name,{tooltip:t.text,icon:t.icon,fetch:e=>e(o()),onSetup:t.onToolbarSetup}),e.ui.registry.addNestedMenuItem(t.name,{type:"nestedmenuitem",text:t.text,getSubmenuItems:o,onSetup:t.onMenuSetup})},FI=e=>{II(e,(e=>({name:"lineheight",text:"Line height",icon:"line-height",getOptions:ov,hash:e=>((e,t)=>OB(e,["fixed","relative","empty"]).map((({value:e,unit:t})=>e+t)))(e).getOr(e),display:w,watcher:(e,t,o)=>e.formatter.formatChanged("lineheight",o,!1,{value:t}).unbind,getCurrent:e=>A.from(e.queryCommandValue("LineHeight")),setCurrent:(e,t)=>e.execCommand("LineHeight",!1,t),onToolbarSetup:ww(e),onMenuSetup:ww(e)}))(e)),(e=>A.from(Cb(e)).map((t=>({name:"language",text:"Language",icon:"language",getOptions:x(t),hash:e=>u(e.customCode)?e.code:`${e.code}/${e.customCode}`,display:e=>e.title,watcher:(e,t,o)=>{var n;return e.formatter.formatChanged("lang",o,!1,{value:t.code,customValue:null!==(n=t.customCode)&&void 0!==n?n:null}).unbind},getCurrent:e=>{const t=ze(e.selection.getNode());return Ns(t,(e=>A.some(e).filter($e).bind((e=>Et(e,"lang").map((t=>({code:t,customCode:Et(e,"data-mce-lang").getOrUndefined(),title:""})))))))},setCurrent:(e,t)=>e.execCommand("Lang",!1,t),onToolbarSetup:t=>{const o=nc();return t.setActive(e.formatter.match("lang",{},void 0,!0)),o.set(e.formatter.formatChanged("lang",t.setActive,!0)),xw(o.clear,ww(e)(t))},onMenuSetup:ww(e)}))))(e).each((t=>II(e,t)))},RI=e=>kw(e,"NodeChange",(t=>{t.setEnabled(e.queryCommandState("outdent")&&e.selection.isEditable())})),NI=(e,t)=>o=>{o.setActive(t.get());const n=e=>{t.set(e.state),o.setActive(e.state)};return e.on("PastePlainTextToggle",n),xw((()=>e.off("PastePlainTextToggle",n)),ww(e)(o))},LI=(e,t)=>()=>{e.execCommand("mceToggleFormat",!1,t)},zI=e=>{(e=>{(e=>{ZO.each([{name:"bold",text:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],((t,o)=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:Sw(e,t.name),onAction:LI(e,t.name),shortcut:t.shortcut})}));for(let t=1;t<=6;t++){const o="h"+t,n=`Access+${t}`;e.ui.registry.addToggleButton(o,{text:o.toUpperCase(),tooltip:"Heading "+t,onSetup:Sw(e,o),onAction:LI(e,o),shortcut:n})}})(e),(e=>{ZO.each([{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"help",text:"Help",action:"mceHelp",icon:"help",shortcut:"Alt+0"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"print",text:"Print",action:"mcePrint",icon:"print",shortcut:"Meta+P"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:Ow(e,t.action),shortcut:t.shortcut})})),ZO.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:ww(e),onAction:Ow(e,t.action)})}))})(e),(e=>{ZO.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:Ow(e,t.action),onSetup:Sw(e,t.name)})}))})(e)})(e),(e=>{ZO.each([{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"},{name:"print",text:"Print...",action:"mcePrint",icon:"print",shortcut:"Meta+P"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:Ow(e,t.action)})})),ZO.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onSetup:ww(e),onAction:Ow(e,t.action)})})),e.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onSetup:ww(e),onAction:LI(e,"code")})})(e)},VI=(e,t)=>kw(e,"Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",(o=>{o.setEnabled(!e.mode.isReadOnly()&&e.undoManager[t]())})),HI=e=>kw(e,"VisualAid",(t=>{t.setActive(e.hasVisual)})),PI=(e,t)=>{(e=>{H([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:Ow(e,t.cmd),onSetup:Sw(e,t.name)})})),e.ui.registry.addButton("alignnone",{tooltip:"No alignment",icon:"align-none",onSetup:ww(e),onAction:Ow(e,"JustifyNone")})})(e),zI(e),((e,t)=>{((e,t)=>{const o=rB(t,mB(e));e.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),onSetup:ww(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=rB(t,SB(e));e.ui.registry.addNestedMenuItem("fontfamily",{text:t.shared.providers.translate("Fonts"),onSetup:ww(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o={type:"advanced",...t.styles},n=rB(t,RB(e,o));e.ui.registry.addNestedMenuItem("styles",{text:"Formats",onSetup:ww(e),getSubmenuItems:()=>n.items.validateItems(n.getStyleItems())})})(e,t),((e,t)=>{const o=rB(t,fB(e));e.ui.registry.addNestedMenuItem("blocks",{text:"Blocks",onSetup:ww(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=rB(t,IB(e));e.ui.registry.addNestedMenuItem("fontsize",{text:"Font sizes",onSetup:ww(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t)})(e,t),(e=>{(e=>{e.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:VI(e,"hasUndo"),onAction:Ow(e,"undo")}),e.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:VI(e,"hasRedo"),onAction:Ow(e,"redo")})})(e),(e=>{e.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",enabled:!1,onSetup:VI(e,"hasUndo"),onAction:Ow(e,"undo"),shortcut:"Meta+Z"}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",enabled:!1,onSetup:VI(e,"hasRedo"),onAction:Ow(e,"redo"),shortcut:"Meta+Y"})})(e)})(e),(e=>{(e=>{e.addCommand("mceApplyTextcolor",((t,o)=>{((e,t,o)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.apply(t,{value:o}),e.nodeChanged()}))})(e,t,o)})),e.addCommand("mceRemoveTextcolor",(t=>{((e,t)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.remove(t,{value:null},void 0,!0),e.nodeChanged()}))})(e,t)}))})(e);const t=jw(e),o=Gw(e),n=Ms(t),s=Ms(o);tS(e,"forecolor","forecolor",n),tS(e,"backcolor","hilitecolor",s),oS(e,"forecolor","forecolor","Text color",n),oS(e,"backcolor","hilitecolor","Background color",s)})(e),(e=>{(e=>{e.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:Ow(e,"mceToggleVisualAid")})})(e),(e=>{e.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:HI(e),onAction:Ow(e,"mceToggleVisualAid")})})(e)})(e),(e=>{(e=>{e.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:RI(e),onAction:Ow(e,"outdent")}),e.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onSetup:ww(e),onAction:Ow(e,"indent")})})(e)})(e),FI(e),(e=>{const t=Ms(qb(e)),o=()=>e.execCommand("mceTogglePlainTextPaste");e.ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:o,onSetup:NI(e,t)}),e.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:o,onSetup:NI(e,t)})})(e)},UI=e=>r(e)?e.split(/[ ,]/):e,WI=e=>t=>t.options.get(e),jI=WI("contextmenu_never_use_native"),GI=WI("contextmenu_avoid_overlap"),$I=e=>{const t=e.ui.registry.getAll().contextMenus,o=e.options.get("contextmenu");return e.options.isSet("contextmenu")?o:U(o,(e=>ve(t,e)))},qI=(e,t)=>({type:"makeshift",x:e,y:t}),YI=e=>"longpress"===e.type||0===e.type.indexOf("touch"),XI=(e,t)=>"contextmenu"===t.type||"longpress"===t.type?e.inline?(e=>{if(YI(e)){const t=e.touches[0];return qI(t.pageX,t.pageY)}return qI(e.pageX,e.pageY)})(t):((e,t)=>{const o=lb.DOM.getPos(e);return((e,t,o)=>qI(e.x+t,e.y+o))(t,o.x,o.y)})(e.getContentAreaContainer(),(e=>{if(YI(e)){const t=e.touches[0];return qI(t.clientX,t.clientY)}return qI(e.clientX,e.clientY)})(t)):KI(e),KI=e=>({type:"selection",root:ze(e.selection.getNode())}),JI=(e,t,o)=>{switch(o){case"node":return(e=>({type:"node",node:A.some(ze(e.selection.getNode())),root:ze(e.getBody())}))(e);case"point":return XI(e,t);case"selection":return KI(e)}},ZI=(e,t,o,n,s,r)=>{const a=o(),i=JI(e,t,r);N_(a,fv.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!1,search:A.none()}).map((e=>{t.preventDefault(),$h.showMenuAt(s,{anchor:i},{menu:{markers:Dv("normal")},data:e})}))},QI={onLtr:()=>[pl,cl,dl,ul,ml,gl,xE,wE,yE,bE,vE,fE],onRtl:()=>[pl,dl,cl,ml,ul,gl,xE,wE,vE,fE,yE,bE]},eF={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},tF=(e,t,o,n,s,r)=>{const a=Io(),i=a.os.isiOS(),l=a.os.isMacOS(),c=a.os.isAndroid(),d=a.deviceType.isTouch(),u=()=>{const a=o();((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>{const n=JI(e,t,o);return{bubble:vc(0,"point"===o?12:0,eF),layouts:QI,overrides:{maxWidthFunction:XM(),maxHeightFunction:pc()},...n}})(e,t,r);N_(o,fv.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!0,search:A.none()}).map((o=>{t.preventDefault();const l=a?Wh.HighlightMenuAndItem:Wh.HighlightNone;$h.showMenuWithinBounds(s,{anchor:i},{menu:{markers:Dv("normal"),highlightOnOpen:l},data:o,type:"horizontal"},(()=>A.some(yI(e,n.shared,"node"===r?"node":"selection")))),e.dispatch(pI)}))})(e,t,a,n,s,r,!(c||i||l&&d))};if((l||i)&&"node"!==r){const o=()=>{(e=>{const t=e.selection.getRng(),o=()=>{qh.setEditorTimeout(e,(()=>{e.selection.setRng(t)}),10),r()};e.once("touchend",o);const n=e=>{e.preventDefault(),e.stopImmediatePropagation()};e.on("mousedown",n,!0);const s=()=>r();e.once("longpresscancel",s);const r=()=>{e.off("touchend",o),e.off("longpresscancel",s),e.off("mousedown",n)}})(e),u()};((e,t)=>{const o=e.selection;if(o.isCollapsed()||t.touches.length<1)return!1;{const n=t.touches[0],s=o.getRng();return od(e.getWin(),Gc.domRange(s)).exists((e=>e.left<=n.clientX&&e.right>=n.clientX&&e.top<=n.clientY&&e.bottom>=n.clientY))}})(e,t)?o():(e.once("selectionchange",o),e.once("touchend",(()=>e.off("selectionchange",o))))}else u()},oF=e=>r(e)?"|"===e:"separator"===e.type,nF={type:"separator"},sF=e=>{const t=e=>({text:e.text,icon:e.icon,enabled:e.enabled,shortcut:e.shortcut});if(r(e))return e;switch(e.type){case"separator":return nF;case"submenu":return{type:"nestedmenuitem",...t(e),getSubmenuItems:()=>{const t=e.getSubmenuItems();return r(t)?t:V(t,sF)}};default:const o=e;return{type:"menuitem",...t(o),onAction:v(o.onAction)}}},rF=(e,t)=>{if(0===t.length)return e;const o=ne(e).filter((e=>!oF(e))).fold((()=>[]),(e=>[nF]));return e.concat(o).concat(t).concat([nF])},aF=(e,t)=>!(e=>"longpress"===e.type||ve(e,"touches"))(t)&&(2!==t.button||t.target===e.getBody()&&""===t.pointerType),iF=(e,t)=>aF(e,t)?e.selection.getStart(!0):t.target,lF=(e,t,o)=>{const n=Io().deviceType.isTouch,s=di($h.sketch({dom:{tag:"div"},lazySink:t,onEscape:()=>e.focus(),onShow:()=>o.setContextMenuState(!0),onHide:()=>o.setContextMenuState(!1),fireDismissalEventInstead:{},inlineBehaviours:El([oh("dismissContextMenu",[jr(_r(),((t,o)=>{Qd.close(t),e.focus()}))])])})),a=()=>$h.hide(s),i=t=>{if(jI(e)&&t.preventDefault(),((e,t)=>t.ctrlKey&&!jI(e))(e,t)||(e=>0===$I(e).length)(e))return;const a=((e,t)=>{const o=GI(e),n=aF(e,t)?"selection":"point";if(De(o)){const s=iF(e,t);return QS(ze(s),o)?"node":n}return n})(e,t);(n()?tF:ZI)(e,t,(()=>{const o=iF(e,t),n=e.ui.registry.getAll(),s=$I(e);return((e,t,o)=>{const n=j(t,((t,n)=>be(e,n.toLowerCase()).map((e=>{const n=e.update(o);if(r(n)&&De(Me(n)))return rF(t,n.split(" "));if(l(n)&&n.length>0){const e=V(n,sF);return rF(t,e)}return t})).getOrThunk((()=>t.concat([n])))),[]);return n.length>0&&oF(n[n.length-1])&&n.pop(),n})(n.contextMenus,s,o)}),o,s,a)};e.on("init",(()=>{const t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(n()?"":" ResizeWindow");e.on(t,a),e.on("longpress contextmenu",i)}))},cF=Ds([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),dF=e=>t=>t.translate(-e.left,-e.top),uF=e=>t=>t.translate(e.left,e.top),mF=e=>(t,o)=>j(e,((e,t)=>t(e)),Yt(t,o)),gF=(e,t,o)=>e.fold(mF([uF(o),dF(t)]),mF([dF(t)]),mF([])),pF=(e,t,o)=>e.fold(mF([uF(o)]),mF([]),mF([uF(t)])),hF=(e,t,o)=>e.fold(mF([]),mF([dF(o)]),mF([uF(t),dF(o)])),fF=(e,t,o)=>{const n=e.fold(((e,t)=>({position:A.some("absolute"),left:A.some(e+"px"),top:A.some(t+"px")})),((e,t)=>({position:A.some("absolute"),left:A.some(e-o.left+"px"),top:A.some(t-o.top+"px")})),((e,t)=>({position:A.some("fixed"),left:A.some(e+"px"),top:A.some(t+"px")})));return{right:A.none(),bottom:A.none(),...n}},bF=(e,t,o,n)=>{const s=(e,s)=>(r,a)=>{const i=e(t,o,n);return s(r.getOr(i.left),a.getOr(i.top))};return e.fold(s(hF,vF),s(pF,yF),s(gF,xF))},vF=cF.offset,yF=cF.absolute,xF=cF.fixed,wF=(e,t)=>{const o=Tt(e,t);return u(o)?NaN:parseInt(o,10)},SF=(e,t,o,n,s,r)=>{const a=((e,t,o,n)=>((e,t)=>{const o=e.element,n=wF(o,t.leftAttr),s=wF(o,t.topAttr);return isNaN(n)||isNaN(s)?A.none():A.some(Yt(n,s))})(e,t).fold((()=>o),(e=>xF(e.left+n.left,e.top+n.top))))(e,t,o,n),i=t.mustSnap?CF(e,t,a,s,r):OF(e,t,a,s,r),l=gF(a,s,r);return((e,t,o)=>{const n=e.element;Ot(n,t.leftAttr,o.left+"px"),Ot(n,t.topAttr,o.top+"px")})(e,t,l),i.fold((()=>({coord:xF(l.left,l.top),extra:A.none()})),(e=>({coord:e.output,extra:e.extra})))},kF=(e,t,o,n)=>re(e,(e=>{const s=e.sensor,r=((e,t,o,n,s,r)=>{const a=pF(e,s,r),i=pF(t,s,r);return Math.abs(a.left-i.left)<=o&&Math.abs(a.top-i.top)<=n})(t,s,e.range.left,e.range.top,o,n);return r?A.some({output:bF(e.output,t,o,n),extra:e.extra}):A.none()})),CF=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return kF(r,o,n,s).orThunk((()=>{const e=j(r,((e,t)=>{const r=t.sensor,a=((e,t,o,n,s,r)=>{const a=pF(e,s,r),i=pF(t,s,r),l=Math.abs(a.left-i.left),c=Math.abs(a.top-i.top);return Yt(l,c)})(o,r,t.range.left,t.range.top,n,s);return e.deltas.fold((()=>({deltas:A.some(a),snap:A.some(t)})),(o=>(a.left+a.top)/2<=(o.left+o.top)/2?{deltas:A.some(a),snap:A.some(t)}:e))}),{deltas:A.none(),snap:A.none()});return e.snap.map((e=>({output:bF(e.output,o,n,s),extra:e.extra})))}))},OF=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return kF(r,o,n,s)};var _F=Object.freeze({__proto__:null,snapTo:(e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const t=ot(e.element),o=jo(t),r=FA(s),a=((e,t,o)=>({coord:bF(e.output,e.output,t,o),extra:e.extra}))(n,o,r),i=fF(a.coord,0,r);Rt(s,i)}}});const TF="data-initial-z-index",EF=(e,t)=>{e.getSystem().addToGui(t),(e=>{at(e.element).filter($e).each((t=>{zt(t,"z-index").each((e=>{Ot(t,TF,e)})),It(t,"z-index",Nt(e.element,"z-index"))}))})(t)},AF=e=>{(e=>{at(e.element).filter($e).each((e=>{Et(e,TF).fold((()=>Pt(e,"z-index")),(t=>It(e,"z-index",t))),Mt(e,TF)}))})(e),e.getSystem().removeFromGui(e)},MF=(e,t,o)=>e.getSystem().build(ck.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[t]},events:o}));var DF=xs("snaps",[ss("getSnapPoints"),Ni("onSensor"),ss("leftAttr"),ss("topAttr"),ws("lazyViewport",on),ws("mustSnap",!1)]);const BF=[ws("useFixed",T),ss("blockerClass"),ws("getTarget",w),ws("onDrag",b),ws("repositionTarget",!0),ws("onDrop",b),Ts("getBounds",on),DF],IF=e=>{return(t=zt(e,"left"),o=zt(e,"top"),n=zt(e,"position"),t.isSome()&&o.isSome()&&n.isSome()?A.some(((e,t,o)=>("fixed"===o?xF:vF)(parseInt(e,10),parseInt(t,10)))(t.getOrDie(),o.getOrDie(),n.getOrDie())):A.none()).getOrThunk((()=>{const t=Kt(e);return yF(t.left,t.top)}));var t,o,n},FF=(e,t)=>({bounds:e.getBounds(),height:$t(t.element),width:eo(t.element)}),RF=(e,t,o,n,s)=>{const r=o.update(n,s),a=o.getStartData().getOrThunk((()=>FF(t,e)));r.each((o=>{((e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const r=ot(e.element),a=jo(r),i=FA(s),l=IF(s),c=((e,t,o,n,s,r,a)=>((e,t,o,n,s)=>{const r=s.bounds,a=pF(t,o,n),i=el(a.left,r.x,r.x+r.width-s.width),l=el(a.top,r.y,r.y+r.height-s.height),c=yF(i,l);return t.fold((()=>{const e=hF(c,o,n);return vF(e.left,e.top)}),x(c),(()=>{const e=gF(c,o,n);return xF(e.left,e.top)}))})(0,t.fold((()=>{const e=(t=o,a=r.left,i=r.top,t.fold(((e,t)=>vF(e+a,t+i)),((e,t)=>yF(e+a,t+i)),((e,t)=>xF(e+a,t+i))));var t,a,i;const l=gF(e,n,s);return xF(l.left,l.top)}),(t=>{const a=SF(e,t,o,r,n,s);return a.extra.each((o=>{t.onSensor(e,o)})),a.coord})),n,s,a))(e,t.snaps,l,a,i,n,o),d=fF(c,0,i);Rt(s,d)}t.onDrag(e,s,n)})(e,t,a,o)}))},NF=(e,t,o,n)=>{t.each(AF),o.snaps.each((t=>{((e,t)=>{((e,t)=>{const o=e.element;Mt(o,t.leftAttr),Mt(o,t.topAttr)})(e,t)})(e,t)}));const s=o.getTarget(e.element);n.reset(),o.onDrop(e,s)},LF=e=>(t,o)=>{const n=e=>{o.setStartData(FF(t,e))};return Pr([jr(Sr(),(e=>{o.getStartData().each((()=>n(e)))})),...e(t,o,n)])};var zF=Object.freeze({__proto__:null,getData:e=>A.from(Yt(e.x,e.y)),getDelta:(e,t)=>Yt(t.left-e.left,t.top-e.top)});const VF=(e,t,o)=>[jr(Gs(),((n,s)=>{if(0!==s.event.raw.button)return;s.stop();const r=()=>NF(n,A.some(l),e,t),a=ek(r,200),i={drop:r,delayDrop:a.schedule,forceDrop:r,move:o=>{a.cancel(),RF(n,e,t,zF,o)}},l=MF(n,e.blockerClass,(e=>Pr([jr(Gs(),e.forceDrop),jr(Ys(),e.drop),jr($s(),((t,o)=>{e.move(o.event)})),jr(qs(),e.delayDrop)]))(i));o(n),EF(n,l)}))],HF=[...BF,Hi("dragger",{handlers:LF(VF)})];var PF=Object.freeze({__proto__:null,getData:e=>{const t=e.raw.touches;return 1===t.length?(e=>{const t=e[0];return A.some(Yt(t.clientX,t.clientY))})(t):A.none()},getDelta:(e,t)=>Yt(t.left-e.left,t.top-e.top)});const UF=(e,t,o)=>{const n=sc(),s=o=>{NF(o,n.get(),e,t),n.clear()};return[jr(Ps(),((r,a)=>{a.stop();const i=()=>s(r),l={drop:i,delayDrop:b,forceDrop:i,move:o=>{RF(r,e,t,PF,o)}},c=MF(r,e.blockerClass,(e=>Pr([jr(Ps(),e.forceDrop),jr(Ws(),e.drop),jr(js(),e.drop),jr(Us(),((t,o)=>{e.move(o.event)}))]))(l));n.set(c),o(r),EF(r,c)})),jr(Us(),((o,n)=>{n.stop(),RF(o,e,t,PF,n.event)})),jr(Ws(),((e,t)=>{t.stop(),s(e)})),jr(js(),s)]},WF=HF,jF=[...BF,Hi("dragger",{handlers:LF(UF)})],GF=[...BF,Hi("dragger",{handlers:LF(((e,t,o)=>[...VF(e,t,o),...UF(e,t,o)]))})];var $F=Object.freeze({__proto__:null,mouse:WF,touch:jF,mouseOrTouch:GF}),qF=Object.freeze({__proto__:null,init:()=>{let e=A.none(),t=A.none();const o=x({});return Ea({readState:o,reset:()=>{e=A.none(),t=A.none()},update:(t,o)=>t.getData(o).bind((o=>((t,o)=>{const n=e.map((e=>t.getDelta(e,o)));return e=A.some(o),n})(t,o))),getStartData:()=>t,setStartData:e=>{t=A.some(e)}})}});const YF=Bl({branchKey:"mode",branches:$F,name:"dragging",active:{events:(e,t)=>e.dragger.handlers(e,t)},extra:{snap:e=>({sensor:e.sensor,range:e.range,output:e.output,extra:A.from(e.extra)})},state:qF,apis:_F}),XF=(e,t,o,n,s,r)=>e.fold((()=>YF.snap({sensor:yF(o-20,n-20),range:Yt(s,r),output:yF(A.some(o),A.some(n)),extra:{td:t}})),(e=>{const s=o-20,r=n-20,a=e.element.dom.getBoundingClientRect();return YF.snap({sensor:yF(s,r),range:Yt(40,40),output:yF(A.some(o-a.width/2),A.some(n-a.height/2)),extra:{td:t}})})),KF=(e,t,o)=>({getSnapPoints:e,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:(e,n)=>{const s=n.td;((e,t)=>e.exists((e=>et(e,t))))(t.get(),s)||(t.set(s),o(s))},mustSnap:!0}),JF=e=>Kh(Yh.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:El([YF.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:e}),Uk.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}})),ZF=(e,t)=>{const o=Ms([]),n=Ms([]),s=Ms(!1),r=sc(),a=sc(),i=e=>{const o=en(e);return XF(u.getOpt(t),e,o.x,o.y,o.width,o.height)},l=e=>{const o=en(e);return XF(m.getOpt(t),e,o.right,o.bottom,o.width,o.height)},c=KF((()=>V(o.get(),(e=>i(e)))),r,(t=>{a.get().each((o=>{e.dispatch("TableSelectorChange",{start:t,finish:o})}))})),d=KF((()=>V(n.get(),(e=>l(e)))),a,(t=>{r.get().each((o=>{e.dispatch("TableSelectorChange",{start:o,finish:t})}))})),u=JF(c),m=JF(d),g=di(u.asSpec()),p=di(m.asSpec()),h=(t,o,n,s)=>{const r=n(o);YF.snapTo(t,r),((t,o,n,r)=>{const a=o.dom.getBoundingClientRect();Pt(t.element,"display");const i=rt(ze(e.getBody())).dom.innerHeight,l=a[s]<0,c=((e,t)=>e[s]>t)(a,i);(l||c)&&It(t.element,"display","none")})(t,o)},f=e=>h(g,e,i,"top"),b=e=>h(p,e,l,"bottom");if(Io().deviceType.isTouch()){const i=e=>V(e,ze);e.on("TableSelectionChange",(e=>{s.get()||(Fd(t,g),Fd(t,p),s.set(!0));const l=ze(e.start),c=ze(e.finish);r.set(l),a.set(c),A.from(e.otherCells).each((e=>{o.set(i(e.upOrLeftCells)),n.set(i(e.downOrRightCells)),f(l),b(c)}))})),e.on("ResizeEditor ResizeWindow ScrollContent",(()=>{r.get().each(f),a.get().each(b)})),e.on("TableSelectionClear",(()=>{s.get()&&(Ld(g),Ld(p),s.set(!1)),r.clear(),a.clear()}))}},QF=(e,t,o)=>{var n;const s=null!==(n=t.delimiter)&&void 0!==n?n:"\u203a";return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:El([$p.config({mode:"flow",selector:"div[role=button]"}),Hm.config({disabled:o.isDisabled}),_x(),fk.config({}),th.config({}),oh("elementPathEvents",[Zr(((t,n)=>{e.shortcuts.add("alt+F11","focus statusbar elementpath",(()=>$p.focusIn(t))),e.on("NodeChange",(n=>{const r=(t=>{const o=[];let n=t.length;for(;n-- >0;){const r=t[n];if(1===r.nodeType&&"BR"!==(s=r).nodeName&&!s.getAttribute("data-mce-bogus")&&"bookmark"!==s.getAttribute("data-mce-type")){const t=yw(e,r);if(t.isDefaultPrevented()||o.push({name:t.name,element:r}),t.isPropagationStopped())break}}var s;return o})(n.parents),a=r.length>0?j(r,((t,n,r)=>{const a=((t,n,s)=>Yh.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{"data-index":s,"aria-level":s+1}},components:[ai(t)],action:t=>{e.focus(),e.selection.select(n),e.nodeChanged()},buttonBehaviours:El([Tx(o.isDisabled),_x()])}))(n.name,n.element,r);return 0===r?t.concat([a]):t.concat([{dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0}},components:[ai(` ${s} `)]},a])}),[]):[];th.set(t,a)}))}))])]),components:[]}};var eR;!function(e){e[e.None=0]="None",e[e.Both=1]="Both",e[e.Vertical=2]="Vertical"}(eR||(eR={}));const tR=(e,t,o)=>{const n=ze(e.getContainer()),s=((e,t,o,n,s)=>{const r={height:iI(n+t.top,vb(e),xb(e))};return o===eR.Both&&(r.width=iI(s+t.left,bb(e),yb(e))),r})(e,t,o,Gt(n),Qt(n));le(s,((e,t)=>{h(e)&&It(n,t,aI(e))})),(e=>{e.dispatch("ResizeEditor")})(e)},oR=(e,t,o,n)=>{const s=Yt(20*o,20*n);return tR(e,s,t),A.some(!0)},nR=(e,t)=>{const o=()=>{const o=[],n=Kb(e),s=jb(e),r=Gb(e)||e.hasPlugin("wordcount");return s&&o.push(QF(e,{},t)),n&&o.push((()=>{const e=zx("Alt+0");return{dom:{tag:"div",classes:["tox-statusbar__help-text"]},components:[ai(Yf.translate(["Press {0} for help",e]))]}})()),r&&o.push((()=>{const o=[];return e.hasPlugin("wordcount")&&o.push(((e,t)=>{const o=(e,o,n)=>th.set(e,[ai(t.translate(["{0} "+n,o[n]]))]);return Yh.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:El([Tx(t.isDisabled),_x(),fk.config({}),th.config({}),yu.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),oh("wordcount-events",[ta((e=>{const t=yu.getValue(e),n="words"===t.mode?"characters":"words";yu.setValue(e,{mode:n,count:t.count}),o(e,t.count,n)})),Zr((t=>{e.on("wordCountUpdate",(e=>{const{mode:n}=yu.getValue(t);yu.setValue(t,{mode:n,count:e.wordCount}),o(t,e.wordCount,n)}))}))])]),eventOrder:{[gr()]:["disabling","alloy.base.behaviour","wordcount-events"]}})})(e,t)),Gb(e)&&o.push({dom:{tag:"span",classes:["tox-statusbar__branding"]},components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/powered-by-tiny?utm_campaign=poweredby&utm_source=tiny&utm_medium=referral&utm_content=v7",rel:"noopener",target:"_blank","aria-label":Yf.translate(["Powered by {0}","Tiny"])},innerHtml:'<svg width="50px" height="16px" viewBox="0 0 50 16" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M10.143 0c2.608.015 5.186 2.178 5.186 5.331 0 0 .077 3.812-.084 4.87-.361 2.41-2.164 4.074-4.65 4.496-1.453.284-2.523.49-3.212.623-.373.071-.634.122-.785.152-.184.038-.997.145-1.35.145-2.732 0-5.21-2.04-5.248-5.33 0 0 0-3.514.03-4.442.093-2.4 1.758-4.342 4.926-4.963 0 0 3.875-.752 4.036-.782.368-.07.775-.1 1.15-.1Zm1.826 2.8L5.83 3.989v2.393l-2.455.475v5.968l6.137-1.189V9.243l2.456-.476V2.8ZM5.83 6.382l3.682-.713v3.574l-3.682.713V6.382Zm27.173-1.64-.084-1.066h-2.226v9.132h2.456V7.743c-.008-1.151.998-2.064 2.149-2.072 1.15-.008 1.987.92 1.995 2.072v5.065h2.455V7.359c-.015-2.18-1.657-3.929-3.837-3.913a3.993 3.993 0 0 0-2.908 1.296Zm-6.3-4.266L29.16 0v2.387l-2.456.475V.476Zm0 3.2v9.132h2.456V3.676h-2.456Zm18.179 11.787L49.11 3.676H46.58l-1.612 4.527-.46 1.382-.384-1.382-1.611-4.527H39.98l3.3 9.132L42.15 16l2.732-.537ZM22.867 9.738c0 .752.568 1.075.921 1.075.353 0 .668-.047.998-.154l.537 1.765c-.23.154-.92.537-2.225.537-1.305 0-2.655-.997-2.686-2.686a136.877 136.877 0 0 1 0-4.374H18.8V3.676h1.612v-1.98l2.455-.476v2.456h2.302V5.9h-2.302v3.837Z"/>\n</svg>\n'.trim()},behaviours:El([ih.config({})])}]}),{dom:{tag:"div",classes:["tox-statusbar__right-container"]},components:o}})()),o.length>0?[{dom:{tag:"div",classes:["tox-statusbar__text-container",...(()=>{const e="tox-statusbar__text-container--flex-start",t="tox-statusbar__text-container--flex-end";if(n){const o="tox-statusbar__text-container-3-cols";return r||s?r&&!s?[o,t]:[o,e]:[o,"tox-statusbar__text-container--space-around"]}return[r&&!s?t:e]})()]},components:o}]:[]};return{dom:{tag:"div",classes:["tox-statusbar"]},components:(()=>{const n=o(),s=((e,t)=>{const o=(e=>{const t=$b(e);return!1===t?eR.None:"both"===t?eR.Both:eR.Vertical})(e);if(o===eR.None)return A.none();const n=o===eR.Both?"Press the arrow keys to resize the editor.":"Press the Up and Down arrow keys to resize the editor.";return A.some(nb("resize-handle",{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{"aria-label":t.translate(n),"data-mce-name":"resize-handle"},behaviours:[YF.config({mode:"mouse",repositionTarget:!1,onDrag:(t,n,s)=>tR(e,s,o),blockerClass:"tox-blocker"}),$p.config({mode:"special",onLeft:()=>oR(e,o,-1,0),onRight:()=>oR(e,o,1,0),onUp:()=>oR(e,o,0,-1),onDown:()=>oR(e,o,0,1)}),fk.config({}),ih.config({}),wx.config(t.tooltips.getConfig({tooltipText:t.translate("Resize")}))]},t.icons))})(e,t);return n.concat(s.toArray())})()}},sR=(e,t)=>t.get().getOrDie(`UI for ${e} has not been rendered`),rR=(e,t)=>{const o=e.inline,n=o?gI:sI,s=uv(e)?hM:BA,r=(()=>{const e=sc(),t=sc(),o=sc();return{dialogUi:e,popupUi:t,mainUi:o,getUiMotherships:()=>{const o=e.get().map((e=>e.mothership)),n=t.get().map((e=>e.mothership));return o.fold((()=>n.toArray()),(e=>n.fold((()=>[e]),(t=>et(e.element,t.element)?[e]:[e,t]))))},lazyGetInOuterOrDie:(e,t)=>()=>o.get().bind((e=>t(e.outerContainer))).getOrDie(`Could not find ${e} element in OuterContainer`)}})(),a=sc(),i=sc(),l=sc(),c=Io().deviceType.isTouch()?["tox-platform-touch"]:[],d=av(e),u=_b(e),m=Kh({dom:{tag:"div",classes:["tox-anchorbar"]}}),g=Kh({dom:{tag:"div",classes:["tox-bottom-anchorbar"]}}),p=()=>r.mainUi.get().map((e=>e.outerContainer)).bind($D.getHeader),h=r.lazyGetInOuterOrDie("anchor bar",m.getOpt),f=r.lazyGetInOuterOrDie("bottom anchor bar",g.getOpt),b=r.lazyGetInOuterOrDie("toolbar",$D.getToolbar),v=r.lazyGetInOuterOrDie("throbber",$D.getThrobber),y=((e,t,o,n)=>{const s=Ms(!1),r=(e=>{const t=Ms(av(e)?"bottom":"top");return{isPositionedAtTop:()=>"top"===t.get(),getDockingMode:t.get,setDockingMode:t.set}})(t),a={icons:()=>t.ui.registry.getAll().icons,menuItems:()=>t.ui.registry.getAll().menuItems,translate:Yf.translate,isDisabled:()=>t.mode.isReadOnly()||!t.ui.isEnabled(),getOption:t.options.get,tooltips:qE(e.dialog)},i=vA(t),l=(e=>{const t=t=>()=>e.formatter.match(t),o=t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},n=Ms([]),s=Ms([]),r=Ms(!1);return e.on("PreInit",(s=>{const r=jE(e),a=$E(e,r,t,o);n.set(a)})),e.on("addStyleModifications",(n=>{const a=$E(e,n.items,t,o);s.set(a),r.set(n.replace)})),{getData:()=>{const e=r.get()?[]:n.get(),t=s.get();return e.concat(t)}}})(t),c=(e=>({colorPicker:RE(e),hasCustomColors:NE(e),getColors:LE(e),getColorCols:zE(e)}))(t),d=(e=>({isDraggableModal:VE(e)}))(t),u={shared:{providers:a,anchors:FE(t,o,n,r.isPositionedAtTop),header:r},urlinput:i,styles:l,colorinput:c,dialog:d,isContextMenuOpen:()=>s.get(),setContextMenuState:e=>s.set(e)},m={...u,shared:{...u.shared,interpreter:e=>lE(e,{},m),getSink:e.popup}},g={...u,shared:{...u.shared,interpreter:e=>lE(e,{},g),getSink:e.dialog}};return{popup:m,dialog:g}})({popup:()=>an.fromOption(r.popupUi.get().map((e=>e.sink)),"(popup) UI has not been rendered"),dialog:()=>an.fromOption(r.dialogUi.get().map((e=>e.sink)),"UI has not been rendered")},e,h,f),x=()=>{const t=(()=>{const t={attributes:{[Cc]:d?kc.BottomToTop:kc.TopToBottom}},o=$D.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:y.popup,onEscape:()=>{e.focus()}}),n=$D.parts.toolbar({dom:{tag:"div",classes:["tox-toolbar"]},getSink:y.popup.shared.getSink,providers:y.popup.shared.providers,onEscape:()=>{e.focus()},onToolbarToggled:t=>{((e,t)=>{e.dispatch("ToggleToolbarDrawer",{state:t})})(e,t)},type:u,lazyToolbar:b,lazyHeader:()=>p().getOrDie("Could not find header element"),...t}),s=$D.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:y.popup.shared.providers,onEscape:()=>{e.focus()},type:u}),r=rv(e),a=nv(e),i=Qb(e),l=Xb(e),c=$D.parts.promotion({dom:{tag:"div",classes:["tox-promotion"]}}),g=r||a||i,h=l?[c,o]:[o];return $D.parts.header({dom:{tag:"div",classes:["tox-editor-header"].concat(g?[]:["tox-editor-header--empty"]),...t},components:q([i?h:[],r?[s]:a?[n]:[],lv(e)?[]:[m.asSpec()]]),sticky:uv(e),editor:e,sharedBackstage:y.popup.shared})})(),n={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[$D.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),$D.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}})]},s=$D.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:y.popup}),r=$D.parts.viewWrapper({backstage:y.popup}),i=Wb(e)&&!o?A.some(nR(e,y.popup.shared.providers)):A.none(),l=q([d?[]:[t],o?[]:[n],d?[t]:[]]),h=$D.parts.editorContainer({components:q([l,o?[]:[g.asSpec(),...i.toArray()]])}),f=dv(e),v={role:"application",...Yf.isRtl()?{dir:"rtl"}:{},...f?{"aria-hidden":"true"}:{}},x=di($D.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(o?["tox-tinymce-inline"]:[]).concat(d?["tox-tinymce--toolbar-bottom"]:[]).concat(c),styles:{visibility:"hidden",...f?{opacity:"0",border:"0"}:{}},attributes:v},components:[h,...o?[]:[r],s],behaviours:El([_x(),Hm.config({disableClass:"tox-tinymce--disabled"}),$p.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])})),w=dk(x);return a.set(w),{mothership:w,outerContainer:x}},w=t=>{const o=aI((e=>{const t=(e=>{const t=hb(e),o=vb(e),n=xb(e);return rI(t).map((e=>iI(e,o,n)))})(e);return t.getOr(hb(e))})(e)),n=aI((e=>lI(e).getOr(fb(e)))(e));return e.inline||(Ht("div","width",n)&&It(t.element,"width",n),Ht("div","height",o)?It(t.element,"height",o):It(t.element,"height","400px")),o};return{popups:{backstage:y.popup,getMothership:()=>sR("popups",l)},dialogs:{backstage:y.dialog,getMothership:()=>sR("dialogs",i)},renderUI:()=>{const o=x(),a=(()=>{const t=cv(e),o=et(St(),t)&&"grid"===Nt(t,"display"),n={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(c),attributes:{...Yf.isRtl()?{dir:"rtl"}:{}}},behaviours:El([Td.config({useFixed:()=>s.isDocked(p)})])},r={dom:{styles:{width:document.body.clientWidth+"px"}},events:Pr([jr(kr(),(e=>{It(e.element,"width",document.body.clientWidth+"px")}))])},a=di(vn(n,o?r:{})),l=dk(a);return i.set(l),{sink:a,mothership:l}})(),d=mv(e)?(()=>{const e={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-silver-popup-sink","tox-tinymce-aux"].concat(c),attributes:{...Yf.isRtl()?{dir:"rtl"}:{}}},behaviours:El([Td.config({useFixed:()=>s.isDocked(p),getBounds:()=>t.getPopupSinkBounds()})])},o=di(e),n=dk(o);return l.set(n),{sink:o,mothership:n}})():(e=>(l.set(e.mothership),e))(a);r.dialogUi.set(a),r.popupUi.set(d),r.mainUi.set(o);return(t=>{const{mainUi:o,popupUi:r,uiMotherships:a}=t;ce(Tb(e),((t,o)=>{e.ui.registry.addGroupToolbarButton(o,t)}));const{buttons:i,menuItems:l,contextToolbars:c,sidebars:d,views:m}=e.ui.registry.getAll(),g=sv(e),h={menuItems:l,menus:gv(e),menubar:Ib(e),toolbar:g.getOrThunk((()=>Fb(e))),allowToolbarGroups:u===ab.floating,buttons:i,sidebar:d,views:m};var f;f=o.outerContainer,e.addShortcut("alt+F9","focus menubar",(()=>{$D.focusMenubar(f)})),e.addShortcut("alt+F10","focus toolbar",(()=>{$D.focusToolbar(f)})),e.addCommand("ToggleToolbarDrawer",((e,t)=>{(null==t?void 0:t.skipFocus)?$D.toggleToolbarDrawerWithoutFocusing(f):$D.toggleToolbarDrawer(f)})),e.addQueryStateHandler("ToggleToolbarDrawer",(()=>$D.isToolbarDrawerToggled(f))),((e,t,o)=>{const n=(e,n)=>{H([t,...o],(t=>{t.broadcastEvent(e,n)}))},s=(e,n)=>{H([t,...o],(t=>{t.broadcastOn([e],n)}))},r=e=>s(eu(),{target:e.target}),a=Yo(),i=ac(a,"touchstart",r),l=ac(a,"touchmove",(e=>n(xr(),e))),c=ac(a,"touchend",(e=>n(wr(),e))),d=ac(a,"mousedown",r),u=ac(a,"mouseup",(e=>{0===e.raw.button&&s(ou(),{target:e.target})})),m=e=>s(eu(),{target:ze(e.target)}),g=e=>{0===e.button&&s(ou(),{target:ze(e.target)})},p=()=>{H(e.editorManager.get(),(t=>{e!==t&&t.dispatch("DismissPopups",{relatedTarget:e})}))},h=e=>n(Sr(),lc(e)),f=e=>{s(tu(),{}),n(kr(),lc(e))},b=bt(ze(e.getElement())),v=ic(b,"scroll",(o=>{requestAnimationFrame((()=>{if(null!=e.getContainer()){const s=JS(e,t.element).map((e=>[e.element,...e.others])).getOr([]);N(s,(e=>et(e,o.target)))&&(e.dispatch("ElementScroll",{target:o.target.dom}),n(Mr(),o))}}))})),y=()=>s(tu(),{}),x=t=>{t.state&&s(eu(),{target:ze(e.getContainer())})},w=e=>{s(eu(),{target:ze(e.relatedTarget.getContainer())})},S=t=>e.dispatch("focusin",t),k=t=>e.dispatch("focusout",t);e.on("PostRender",(()=>{e.on("click",m),e.on("tap",m),e.on("mouseup",g),e.on("mousedown",p),e.on("ScrollWindow",h),e.on("ResizeWindow",f),e.on("ResizeEditor",y),e.on("AfterProgressState",x),e.on("DismissPopups",w),H([t,...o],(e=>{e.element.dom.addEventListener("focusin",S),e.element.dom.addEventListener("focusout",k)}))})),e.on("remove",(()=>{e.off("click",m),e.off("tap",m),e.off("mouseup",g),e.off("mousedown",p),e.off("ScrollWindow",h),e.off("ResizeWindow",f),e.off("ResizeEditor",y),e.off("AfterProgressState",x),e.off("DismissPopups",w),H([t,...o],(e=>{e.element.dom.removeEventListener("focusin",S),e.element.dom.removeEventListener("focusout",k)})),d.unbind(),i.unbind(),l.unbind(),c.unbind(),u.unbind(),v.unbind()})),e.on("detach",(()=>{H([t,...o],Ud),H([t,...o],(e=>e.destroy()))}))})(e,o.mothership,a),s.setup(e,y.popup.shared,p),PI(e,y.popup),lF(e,y.popup.shared.getSink,y.popup),(e=>{const{sidebars:t}=e.ui.registry.getAll();H(ae(t),(o=>{const n=t[o],s=()=>xe(A.from(e.queryCommandValue("ToggleSidebar")),o);e.ui.registry.addToggleButton(o,{icon:n.icon,tooltip:n.tooltip,onAction:t=>{e.execCommand("ToggleSidebar",!1,o),t.setActive(s())},onSetup:t=>{t.setActive(s());const o=()=>t.setActive(s());return e.on("ToggleSidebar",o),()=>{e.off("ToggleSidebar",o)}}})}))})(e),HM(e,v,y.popup.shared),BI(e,c,r.sink,{backstage:y.popup}),ZF(e,r.sink);const b={targetNode:e.getElement(),height:w(o.outerContainer)};return n.render(e,t,h,y.popup,b)})({popupUi:d,dialogUi:a,mainUi:o,uiMotherships:r.getUiMotherships()})}}},aR=x([ss("lazySink"),gs("dragBlockClass"),Ts("getBounds",on),ws("useTabstopAt",E),ws("firstTabstop",0),ws("eventOrder",{}),xu("modalBehaviours",[$p]),Li("onExecute"),Vi("onEscape")]),iR={sketch:w},lR=x([Xu({name:"draghandle",overrides:(e,t)=>({behaviours:El([YF.config({mode:"mouse",getTarget:e=>bi(e,'[role="dialog"]').getOr(e),blockerClass:e.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:e.getDragBounds})])})}),qu({schema:[ss("dom")],name:"title"}),qu({factory:iR,schema:[ss("dom")],name:"close"}),qu({factory:iR,schema:[ss("dom")],name:"body"}),Xu({factory:iR,schema:[ss("dom")],name:"footer"}),Yu({factory:{sketch:(e,t)=>({...e,dom:t.dom,components:t.components})},schema:[ws("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),ws("components",[])],name:"blocker"})]),cR=Sm({name:"ModalDialog",configFields:aR(),partFields:lR(),factory:(e,t,o,n)=>{const s=sc(),r=da("modal-events"),a={...e.eventOrder,[Cr()]:[r].concat(e.eventOrder["alloy.system.attached"]||[])};return{uid:e.uid,dom:e.dom,components:t,apis:{show:t=>{s.set(t);const o=e.lazySink(t).getOrDie(),r=n.blocker(),a=o.getSystem().build({...r,components:r.components.concat([ui(t)]),behaviours:El([ih.config({}),oh("dialog-blocker-events",[Jr(Ks(),(()=>{zM.isBlocked(t)||$p.focusIn(t)}))])])});Fd(o,a),$p.focusIn(t)},hide:e=>{s.clear(),at(e.element).each((t=>{e.getSystem().getByDom(t).each((e=>{Ld(e)}))}))},getBody:t=>lm(t,e,"body"),getFooter:t=>im(t,e,"footer"),setIdle:e=>{zM.unblock(e)},setBusy:(e,t)=>{zM.block(e,t)}},eventOrder:a,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:Su(e.modalBehaviours,[th.config({}),$p.config({mode:"cyclic",onEnter:e.onExecute,onEscape:e.onEscape,useTabstopAt:e.useTabstopAt,firstTabstop:e.firstTabstop}),zM.config({getRoot:s.get}),oh(r,[Zr((t=>{((e,t)=>{const o=Et(e,"id").fold((()=>{const e=da("dialog-label");return Ot(t,"id",e),e}),w);Ot(e,"aria-labelledby",o)})(t.element,lm(t,e,"title").element)}))])])}},apis:{show:(e,t)=>{e.show(t)},hide:(e,t)=>{e.hide(t)},getBody:(e,t)=>e.getBody(t),getFooter:(e,t)=>e.getFooter(t),setBusy:(e,t,o)=>{e.setBusy(t,o)},setIdle:(e,t)=>{e.setIdle(t)}}}),dR=In([ny,sy].concat(Qy)),uR=Un,mR=[My("button"),by,Os("align","end",["start","end"]),Oy,Cy,bs("buttonType",["primary","secondary"])],gR=[...mR,ay],pR=[ls("type",["submit","cancel","custom"]),...gR],hR=[ls("type",["menu"]),fy,vy,by,ms("items",dR),...mR],fR=[...mR,ls("type",["togglebutton"]),vy,by,fy,_s("active",!1)],bR=Qn("type",{submit:pR,cancel:pR,custom:pR,menu:hR,togglebutton:fR}),vR=[ny,ay,ls("level",["info","warn","error","success"]),ly,ws("url","")],yR=In(vR),xR=[ny,ay,Cy,My("button"),by,ky,bs("buttonType",["primary","secondary","toolbar"]),Oy],wR=In(xR),SR=[ny,sy],kR=SR.concat([yy]),CR=SR.concat([ry,Cy]),OR=In(CR),_R=Un,TR=kR.concat([_y("auto")]),ER=In(TR),AR=Ln([cy,ay,ly]),MR=kR.concat([Cs("storageKey","default")]),DR=In(MR),BR=Pn,IR=In(kR),FR=Pn,RR=SR.concat([Cs("tag","textarea"),is("scriptId"),is("scriptUrl"),vs("onFocus"),Ss("settings",void 0,Gn)]),NR=SR.concat([Cs("tag","textarea"),cs("init")]),LR=qn((e=>Xn("customeditor.old",Bn(NR),e).orThunk((()=>Xn("customeditor.new",Bn(RR),e))))),zR=Pn,VR=In(kR),HR=Fn(Tn),PR=e=>[ny,as("columns"),e],UR=[ny,is("html"),Os("presets","presentation",["presentation","document"])],WR=In(UR),jR=kR.concat([_s("border",!1),_s("sandboxed",!0),_s("streamContent",!1),_s("transparent",!0)]),GR=In(jR),$R=Pn,qR=In(SR.concat([fs("height")])),YR=In([is("url"),hs("zoom"),hs("cachedWidth"),hs("cachedHeight")]),XR=kR.concat([fs("inputMode"),fs("placeholder"),_s("maximized",!1),Cy]),KR=In(XR),JR=Pn,ZR=e=>[ny,ry,e,Os("align","start",["start","center","end"])],QR=[ay,cy],eN=[ay,ms("items",es(0,(()=>tN)))],tN=Rn([In(QR),In(eN)]),oN=kR.concat([ms("items",tN),Cy]),nN=In(oN),sN=Pn,rN=kR.concat([us("items",[ay,cy]),ks("size",1),Cy]),aN=In(rN),iN=Pn,lN=kR.concat([_s("constrain",!0),Cy]),cN=In(lN),dN=In([is("width"),is("height")]),uN=SR.concat([ry,ks("min",0),ks("max",0)]),mN=In(uN),gN=Hn,pN=[ny,ms("header",Pn),ms("cells",Fn(Pn))],hN=In(pN),fN=kR.concat([fs("placeholder"),_s("maximized",!1),Cy]),bN=In(fN),vN=Pn,yN=[ls("type",["directory","leaf"]),iy,is("id"),ps("menu",vM)],xN=In(yN),wN=yN.concat([ms("children",es(0,(()=>$n("type",{directory:SN,leaf:xN}))))]),SN=In(wN),kN=$n("type",{directory:SN,leaf:xN}),CN=[ny,ms("items",kN),vs("onLeafAction"),vs("onToggleExpand"),Es("defaultExpandedIds",[],Pn),fs("defaultSelectedId")],ON=In(CN),_N=kR.concat([Os("filetype","file",["image","media","file"]),Cy,fs("picker_text")]),TN=In(_N),EN=In([cy,Ty]),AN=e=>ts("items","items",{tag:"required",process:{}},Fn(qn((t=>Xn(`Checking item of ${e}`,MN,t).fold((e=>an.error(Zn(e))),(e=>an.value(e))))))),MN=Mn((()=>{return $n("type",{alertbanner:yR,bar:In((e=AN("bar"),[ny,e])),button:wR,checkbox:OR,colorinput:DR,colorpicker:IR,dropzone:VR,grid:In(PR(AN("grid"))),iframe:GR,input:KR,listbox:nN,selectbox:aN,sizeinput:cN,slider:mN,textarea:bN,urlinput:TN,customeditor:LR,htmlpanel:WR,imagepreview:qR,collection:ER,label:In(ZR(AN("label"))),table:hN,tree:ON,panel:BN});var e})),DN=[ny,ws("classes",[]),ms("items",MN)],BN=In(DN),IN=[My("tab"),iy,ms("items",MN)],FN=[ny,us("tabs",IN)],RN=In(FN),NN=gR,LN=bR,zN=In([is("title"),rs("body",$n("type",{panel:BN,tabpanel:RN})),Cs("size","normal"),Es("buttons",[],LN),ws("initialData",{}),Ts("onAction",b),Ts("onChange",b),Ts("onSubmit",b),Ts("onClose",b),Ts("onCancel",b),Ts("onTabChange",b)]),VN=In([ls("type",["cancel","custom"]),...NN]),HN=In([is("title"),is("url"),hs("height"),hs("width"),ys("buttons",VN),Ts("onAction",b),Ts("onCancel",b),Ts("onClose",b),Ts("onMessage",b)]),PN=e=>a(e)?[e].concat(Y(fe(e),PN)):l(e)?Y(e,PN):[],UN=e=>r(e.type)&&r(e.name),WN={checkbox:_R,colorinput:BR,colorpicker:FR,dropzone:HR,input:JR,iframe:$R,imagepreview:YR,selectbox:iN,sizeinput:dN,slider:gN,listbox:sN,size:dN,textarea:vN,urlinput:EN,customeditor:zR,collection:AR,togglemenuitem:uR},jN=e=>{const t=(e=>U(PN(e),UN))(e),o=Y(t,(e=>(e=>A.from(WN[e.type]))(e).fold((()=>[]),(t=>[rs(e.name,t)]))));return In(o)},GN=e=>{var t;return{internalDialog:Kn(Xn("dialog",zN,e)),dataValidator:jN(e),initialData:null!==(t=e.initialData)&&void 0!==t?t:{}}},$N={open:(e,t)=>{const o=GN(t);return e(o.internalDialog,o.initialData,o.dataValidator)},openUrl:(e,t)=>e(Kn(Xn("dialog",HN,t))),redial:e=>GN(e)};var qN=Object.freeze({__proto__:null,events:(e,t)=>{const o=(o,n)=>{e.updateState.each((e=>{const s=e(o,n);t.set(s)})),e.renderComponents.each((s=>{const r=s(n,t.get());(e.reuseDom?Yp:qp)(o,r)}))};return Pr([jr(mr(),((t,n)=>{const s=n;if(!s.universal){const n=e.channel;R(s.channels,n)&&o(t,s.data)}})),Zr(((t,n)=>{e.initialData.each((e=>{o(t,e)}))}))])}}),YN=Object.freeze({__proto__:null,getState:(e,t,o)=>o}),XN=[ss("channel"),gs("renderComponents"),gs("updateState"),gs("initialData"),_s("reuseDom",!0)];const KN=Ml({fields:XN,name:"reflecting",active:qN,apis:YN,state:Object.freeze({__proto__:null,init:()=>{const e=Ms(A.none());return{readState:()=>e.get().getOr("none"),get:e.get,set:e.set,clear:()=>e.set(A.none())}}})}),JN=e=>{const t=[],o={};return le(e,((e,n)=>{e.fold((()=>{t.push(n)}),(e=>{o[n]=e}))})),t.length>0?an.error(t):an.value(o)},ZN=(e,t,o)=>{const n=Kh(RO.sketch((n=>({dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:V(e.items,(e=>aE(n,e,t,o)))}))));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[n.asSpec()]}],behaviours:El([$p.config({mode:"acyclic",useTabstopAt:C(a_)}),(s=n,_m.config({find:s.getOpt})),GO(n,{postprocess:e=>JN(e).fold((e=>(console.error(e),{})),w)}),oh("dialog-body-panel",[jr(Ks(),((e,t)=>{e.getSystem().broadcastOn([m_],{newFocus:A.some(t.event.target)})}))])])};var s},QN=wm({name:"TabButton",configFields:[ws("uid",void 0),ss("value"),ts("dom","dom",Sn((()=>({attributes:{role:"tab",id:da("aria"),"aria-selected":"false"}}))),zn()),gs("action"),ws("domModification",{}),xu("tabButtonBehaviours",[ih,$p,yu]),ss("view")],factory:(e,t)=>({uid:e.uid,dom:e.dom,components:e.components,events:bh(e.action),behaviours:Su(e.tabButtonBehaviours,[ih.config({}),$p.config({mode:"execution",useSpace:!0,useEnter:!0}),yu.config({store:{mode:"memory",initialValue:e.value}})]),domModification:e.domModification})}),eL=x([ss("tabs"),ss("dom"),ws("clickToDismiss",!1),xu("tabbarBehaviours",[Km,$p]),Fi(["tabClass","selectedClass"])]),tL=Ku({factory:QN,name:"tabs",unit:"tab",overrides:e=>{const t=(e,t)=>{Km.dehighlight(e,t),Nr(e,Br(),{tabbar:e,button:t})},o=(e,t)=>{Km.highlight(e,t),Nr(e,Dr(),{tabbar:e,button:t})};return{action:n=>{const s=n.getSystem().getByUid(e.uid).getOrDie(),r=Km.isHighlighted(s,n);(r&&e.clickToDismiss?t:r?b:o)(s,n)},domModification:{classes:[e.markers.tabClass]}}}}),oL=x([tL]),nL=Sm({name:"Tabbar",configFields:eL(),partFields:oL(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:Su(e.tabbarBehaviours,[Km.config({highlightClass:e.markers.selectedClass,itemClass:e.markers.tabClass,onHighlight:(e,t)=>{Ot(t.element,"aria-selected","true")},onDehighlight:(e,t)=>{Ot(t.element,"aria-selected","false")}}),$p.config({mode:"flow",getInitial:e=>Km.getHighlighted(e).map((e=>e.element)),selector:"."+e.markers.tabClass,executeOnMove:!0})])})}),sL=wm({name:"Tabview",configFields:[xu("tabviewBehaviours",[th])],factory:(e,t)=>({uid:e.uid,dom:e.dom,behaviours:Su(e.tabviewBehaviours,[th.config({})]),domModification:{attributes:{role:"tabpanel"}}})}),rL=x([ws("selectFirst",!0),Ni("onChangeTab"),Ni("onDismissTab"),ws("tabs",[]),xu("tabSectionBehaviours",[])]),aL=qu({factory:nL,schema:[ss("dom"),ds("markers",[ss("tabClass"),ss("selectedClass")])],name:"tabbar",defaults:e=>({tabs:e.tabs})}),iL=qu({factory:sL,name:"tabview"}),lL=x([aL,iL]),cL=Sm({name:"TabSection",configFields:rL(),partFields:lL(),factory:(e,t,o,n)=>{const s=(t,o)=>{im(t,e,"tabbar").each((e=>{o(e).each(Lr)}))};return{uid:e.uid,dom:e.dom,components:t,behaviours:wu(e.tabSectionBehaviours),events:Pr(q([e.selectFirst?[Zr(((e,t)=>{s(e,Km.getFirst)}))]:[],[jr(Dr(),((t,o)=>{(t=>{const o=yu.getValue(t);im(t,e,"tabview").each((n=>{G(e.tabs,(e=>e.value===o)).each((o=>{const s=o.view();Et(t.element,"id").each((e=>{Ot(n.element,"aria-labelledby",e)})),th.set(n,s),e.onChangeTab(n,t,s)}))}))})(o.event.button)})),jr(Br(),((t,o)=>{const n=o.event.button;e.onDismissTab(t,n)}))]])),apis:{getViewItems:t=>im(t,e,"tabview").map((e=>th.contents(e))).getOr([]),showTab:(e,t)=>{s(e,(e=>{const o=Km.getCandidates(e);return G(o,(e=>yu.getValue(e)===t)).filter((t=>!Km.isHighlighted(e,t)))}))}}}},apis:{getViewItems:(e,t)=>e.getViewItems(t),showTab:(e,t,o)=>{e.showTab(t,o)}}}),dL=(e,t)=>{It(e,"height",t+"px"),It(e,"flex-basis",t+"px")},uL=(e,t,o)=>{bi(e,'[role="dialog"]').each((e=>{yi(e,'[role="tablist"]').each((n=>{o.get().map((o=>(It(t,"height","0"),It(t,"flex-basis","0"),Math.min(o,((e,t,o)=>{const n=st(e).dom,s=bi(e,".tox-dialog-wrap").getOr(e);let r;r="fixed"===Nt(s,"position")?Math.max(n.clientHeight,window.innerHeight):Math.max(n.offsetHeight,n.scrollHeight);const a=Gt(t),i=t.dom.offsetLeft>=o.dom.offsetLeft+Qt(o)?Math.max(Gt(o),a):a,l=parseInt(Nt(e,"margin-top"),10)||0,c=parseInt(Nt(e,"margin-bottom"),10)||0;return r-(Gt(e)+l+c-i)})(e,t,n))))).each((e=>{dL(t,e)}))}))}))},mL=e=>yi(e,'[role="tabpanel"]'),gL="send-data-to-section",pL="send-data-to-view",hL=(e,t,o)=>{const n=Ms({}),s=e=>{const t=yu.getValue(e),o=JN(t).getOr({}),s=n.get(),r=vn(s,o);n.set(r)},r=e=>{const t=n.get();yu.setValue(e,t)},a=Ms(null),i=V(e.tabs,(e=>({value:e.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"]},components:[ai(o.shared.providers.translate(e.title))],view:()=>[RO.sketch((n=>({dom:{tag:"div",classes:["tox-form"]},components:V(e.items,(e=>aE(n,e,t,o))),formBehaviours:El([$p.config({mode:"acyclic",useTabstopAt:C(a_)}),oh("TabView.form.events",[Zr(r),Qr(s)]),Fl.config({channels:Is([{key:gL,value:{onReceive:s}},{key:pL,value:{onReceive:r}}])})])})))]}))),l=(e=>{const t=sc(),o=[Zr((o=>{const n=o.element;mL(n).each((s=>{It(s,"visibility","hidden"),o.getSystem().getByDom(s).toOptional().each((o=>{const n=((e,t,o)=>V(e,((n,s)=>{th.set(o,e[s].view());const r=t.dom.getBoundingClientRect();return th.set(o,[]),r.height})))(e,s,o),r=(e=>oe(ee(e,((e,t)=>e>t?-1:e<t?1:0))))(n);r.fold(t.clear,t.set)})),uL(n,s,t),Pt(s,"visibility"),((e,t)=>{oe(e).each((e=>cL.showTab(t,e.value)))})(e,o),requestAnimationFrame((()=>{uL(n,s,t)}))}))})),jr(kr(),(e=>{const o=e.element;mL(o).each((e=>{uL(o,e,t)}))})),jr(Mk,((e,o)=>{const n=e.element;mL(n).each((e=>{const o=Vl(bt(e));It(e,"visibility","hidden");const s=zt(e,"height").map((e=>parseInt(e,10)));Pt(e,"height"),Pt(e,"flex-basis");const r=e.dom.getBoundingClientRect().height;s.forall((e=>r>e))?(t.set(r),uL(n,e,t)):s.each((t=>{dL(e,t)})),Pt(e,"visibility"),o.each(Nl)}))}))];return{extraEvents:o,selectFirst:!1}})(i);return cL.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:(e,t,o)=>{const n=yu.getValue(t);Nr(e,Ak,{name:n,oldName:a.get()}),a.set(n)},tabs:i,components:[cL.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[nL.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:El([fk.config({})])}),cL.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:l.selectFirst,tabSectionBehaviours:El([oh("tabpanel",l.extraEvents),$p.config({mode:"acyclic"}),_m.config({find:e=>oe(cL.getViewItems(e))}),$O(A.none(),(e=>(e.getSystem().broadcastOn([gL],{}),n.get())),((e,t)=>{n.set(t),e.getSystem().broadcastOn([pL],{})}))])})},fL=(e,t,o,n,s)=>({dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:{...o.map((e=>({id:e}))).getOr({}),...s?{"aria-live":"polite"}:{}}},components:[],behaviours:El([WO(0),KN.config({channel:`${c_}-${t}`,updateState:(e,t)=>A.some({isTabPanel:()=>"tabpanel"===t.body.type}),renderComponents:e=>{const t=e.body;return"tabpanel"===t.type?[hL(t,e.initialData,n)]:[ZN(t,e.initialData,n)]},initialData:e})])}),bL=db.deviceType.isTouch(),vL=(e,t)=>({dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[e,t]}),yL=(e,t)=>cR.parts.close(Yh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:e,buttonBehaviours:El([fk.config({})])})),xL=()=>cR.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}}),wL=(e,t)=>cR.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:Xh(`<p>${qf(t.translate(e))}</p>`)}]}]}),SL=e=>cR.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:e}),kL=(e,t)=>[ck.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:e}),ck.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})],CL=e=>{const t="tox-dialog",o=t+"-wrap",n=o+"__backdrop",s=t+"__disable-scroll";return cR.sketch({lazySink:e.lazySink,onEscape:t=>(e.onEscape(t),A.some(!0)),useTabstopAt:e=>!a_(e),firstTabstop:e.firstTabstop,dom:{tag:"div",classes:[t].concat(e.extraClasses),styles:{position:"relative",...e.extraStyles}},components:[e.header,e.body,...e.footer.toArray()],parts:{blocker:{dom:Xh(`<div class="${o}"></div>`),components:[{dom:{tag:"div",classes:bL?[n,n+"--opaque"]:[n]}}]}},dragBlockClass:o,modalBehaviours:El([ih.config({}),oh("dialog-events",e.dialogEvents.concat([Jr(Ks(),((e,t)=>{zM.isBlocked(e)||$p.focusIn(e)})),jr(Er(),((e,t)=>{e.getSystem().broadcastOn([m_],{newFocus:t.event.newFocus})}))])),oh("scroll-lock",[Zr((()=>{ja(St(),s)})),Qr((()=>{$a(St(),s)}))]),...e.extraBehaviours]),eventOrder:{[gr()]:["dialog-events"],[Cr()]:["scroll-lock","dialog-events","alloy.base.behaviour"],[Or()]:["alloy.base.behaviour","dialog-events","scroll-lock"],...e.eventOrder}})},OL=e=>Yh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":e.translate("Close"),"data-mce-name":"close"}},buttonBehaviours:El([fk.config({}),wx.config(e.tooltips.getConfig({tooltipText:e.translate("Close")}))]),components:[nb("close",{tag:"span",classes:["tox-icon"]},e.icons)],action:e=>{Rr(e,Ck)}}),_L=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__title"],attributes:{...o.map((e=>({id:e}))).getOr({})}},components:[],behaviours:El([KN.config({channel:`${l_}-${t}`,initialData:e,renderComponents:e=>[ai(n.translate(e.title))]})])}),TL=()=>({dom:Xh('<div class="tox-dialog__draghandle"></div>')}),EL=(e,t,o)=>((e,t,o)=>{const n=cR.parts.title(_L(e,t,A.none(),o)),s=cR.parts.draghandle(TL()),r=cR.parts.close(OL(o)),a=[n].concat(e.draggable?[s]:[]).concat([r]);return ck.sketch({dom:Xh('<div class="tox-dialog__header"></div>'),components:a})})({title:o.shared.providers.translate(e),draggable:o.dialog.isDraggableModal()},t,o.shared.providers),AL=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":o.translate(e)},styles:{left:"0px",right:"0px",bottom:"0px",top:`${n.getOr(0)}px`,position:"absolute"}},behaviours:t,components:[{dom:Xh('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}),ML=(e,t,o)=>({onClose:()=>o.closeWindow(),onBlock:o=>{const n=yi(e().element,".tox-dialog__header").map((e=>Gt(e)));cR.setBusy(e(),((e,s)=>AL(o.message,s,t,n)))},onUnblock:()=>{cR.setIdle(e())}}),DL="tox-dialog--fullscreen",BL="tox-dialog--width-lg",IL="tox-dialog--width-md",FL=e=>{switch(e){case"large":return A.some(BL);case"medium":return A.some(IL);default:return A.none()}},RL=(e,t)=>{const o=ze(t.element.dom);qa(o,DL)||(Xa(o,[BL,IL]),FL(e).each((e=>ja(o,e))))},NL=(e,t)=>{const o=ze(e.element.dom),n=Ka(o),s=G(n,(e=>e===BL||e===IL)).or(FL(t));((e,t)=>{H(t,(t=>{((e,t)=>{const o=Ha(e)?e.dom.classList.toggle(t):((e,t)=>R(Pa(e),t)?Wa(e,t):Ua(e,t))(e,t);Ga(e)})(e,t)}))})(o,[DL,...s.toArray()])},LL=(e,t,o)=>di(CL({...e,firstTabstop:1,lazySink:o.shared.getSink,extraBehaviours:[YO({}),...e.extraBehaviours],onEscape:e=>{Rr(e,Ck)},dialogEvents:t,eventOrder:{[mr()]:[KN.name(),Fl.name()],[Cr()]:["scroll-lock",KN.name(),"messages","dialog-events","alloy.base.behaviour"],[Or()]:["alloy.base.behaviour","dialog-events","messages",KN.name(),"scroll-lock"]}})),zL=(e,t={})=>V(e,(e=>"menu"===e.type?(e=>{const o=V(e.items,(e=>{const o=be(t,e.name).getOr(Ms(!1));return{...e,storage:o}}));return{...e,items:o}})(e):e)),VL=e=>j(e,((e,t)=>"menu"===t.type?j(t.items,((e,t)=>(e[t.name]=t.storage,e)),e):e),{}),HL=(e,t)=>[Yr(Ks(),r_),e(kk,((e,o,n,s)=>{Vl(bt(s.element)).fold(b,Ll),t.onClose(),o.onClose()})),e(Ck,((e,t,o,n)=>{t.onCancel(e),Rr(n,kk)})),jr(Ek,((e,o)=>t.onUnblock())),jr(Tk,((e,o)=>t.onBlock(o.event)))],PL=(e,t,o)=>{const n=(t,o)=>jr(t,((t,n)=>{s(t,((s,r)=>{o(e(),s,n.event,t)}))})),s=(e,t)=>{KN.getState(e).get().each((o=>{t(o.internalDialog,e)}))};return[...HL(n,t),n(_k,((e,t)=>t.onSubmit(e))),n(Sk,((e,t,o)=>{t.onChange(e,{name:o.name})})),n(Ok,((e,t,n,s)=>{const r=()=>s.getSystem().isConnected()?$p.focusIn(s):void 0,a=e=>At(e,"disabled")||Et(e,"aria-disabled").exists((e=>"true"===e)),i=bt(s.element),l=Vl(i);t.onAction(e,{name:n.name,value:n.value}),Vl(i).fold(r,(e=>{a(e)||l.exists((t=>tt(e,t)&&a(t)))?r():o().toOptional().filter((t=>!tt(t.element,e))).each(r)}))})),n(Ak,((e,t,o)=>{t.onTabChange(e,{newTabName:o.name,oldTabName:o.oldName})})),Qr((t=>{const o=e();yu.setValue(t,o.getData())}))]},UL=(e,t)=>{const o=t.map((e=>e.footerButtons)).getOr([]),n=P(o,(e=>"start"===e.align)),s=(e,t)=>ck.sketch({dom:{tag:"div",classes:[`tox-dialog__footer-${e}`]},components:V(t,(e=>e.memento.asSpec()))});return[s("start",n.pass),s("end",n.fail)]},WL=(e,t,o)=>({dom:Xh('<div class="tox-dialog__footer"></div>'),components:[],behaviours:El([KN.config({channel:`${d_}-${t}`,initialData:e,updateState:(e,t)=>{const n=V(t.buttons,(e=>{const t=Kh(((e,t)=>jT(e,e.type,t))(e,o));return{name:e.name,align:e.align,memento:t}}));return A.some({lookupByName:t=>((e,t,o)=>G(t,(e=>e.name===o)).bind((t=>t.memento.getOpt(e))))(e,n,t),footerButtons:n})},renderComponents:UL})])}),jL=(e,t,o)=>cR.parts.footer(WL(e,t,o)),GL=(e,t)=>{if(e.getRoot().getSystem().isConnected()){const o=_m.getCurrent(e.getFormWrapper()).getOr(e.getFormWrapper());return RO.getField(o,t).orThunk((()=>{const o=e.getFooter().bind((e=>KN.getState(e).get()));return o.bind((e=>e.lookupByName(t)))}))}return A.none()},$L=(e,t,o)=>{const n=t=>{const o=e.getRoot();o.getSystem().isConnected()&&t(o)},s={getData:()=>{const t=e.getRoot(),n=t.getSystem().isConnected()?e.getFormWrapper():t;return{...yu.getValue(n),...ce(o,(e=>e.get()))}},setData:t=>{n((n=>{const r=s.getData(),a=vn(r,t),i=((e,t)=>{const o=e.getRoot();return KN.getState(o).get().map((e=>Kn(Xn("data",e.dataValidator,t)))).getOr(t)})(e,a),l=e.getFormWrapper();yu.setValue(l,i),le(o,((e,t)=>{ve(a,t)&&e.set(a[t])}))}))},setEnabled:(t,o)=>{GL(e,t).each(o?Hm.enable:Hm.disable)},focus:t=>{GL(e,t).each(ih.focus)},block:e=>{if(!r(e))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n((t=>{Nr(t,Tk,{message:e})}))},unblock:()=>{n((e=>{Rr(e,Ek)}))},showTab:t=>{n((o=>{const n=e.getBody();KN.getState(n).get().exists((e=>e.isTabPanel()))&&_m.getCurrent(n).each((e=>{cL.showTab(e,t)}))}))},redial:r=>{n((n=>{const a=e.getId(),i=t(r),l=zL(i.internalDialog.buttons,o);n.getSystem().broadcastOn([`${i_}-${a}`],i),n.getSystem().broadcastOn([`${l_}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${c_}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${d_}-${a}`],{...i.internalDialog,buttons:l}),s.setData(i.initialData)}))},close:()=>{n((e=>{Rr(e,kk)}))},toggleFullscreen:e.toggleFullscreen};return s},qL=(e,t,o,n=!1,s)=>{const r=da("dialog"),a=da("dialog-label"),i=da("dialog-content"),l=e.internalDialog,c=Ms(l.size),d=FL(c.get()).toArray(),u=Kh(((e,t,o,n)=>ck.sketch({dom:Xh('<div class="tox-dialog__header"></div>'),components:[_L(e,t,A.some(o),n),TL(),OL(n)],containerBehaviours:El([YF.config({mode:"mouse",blockerClass:"blocker",getTarget:e=>xi(e,'[role="dialog"]').getOrDie(),snaps:{getSnapPoints:()=>[],leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))({title:l.title,draggable:!0},r,a,o.shared.providers)),m=Kh(((e,t,o,n,s)=>fL(e,t,A.some(o),n,s))({body:l.body,initialData:l.initialData},r,i,o,n)),g=zL(l.buttons),p=VL(g),h=Ce(0!==g.length,Kh(((e,t,o)=>WL(e,t,o))({buttons:g},r,o))),f=PL((()=>v),{onBlock:e=>{zM.block(b,((t,n)=>{const s=u.getOpt(b).map((e=>Gt(e.element)));return AL(e.message,n,o.shared.providers,s)}))},onUnblock:()=>{zM.unblock(b)},onClose:()=>t.closeWindow()},o.shared.getSink),b=di({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline",...d],attributes:{role:"dialog","aria-labelledby":a}},eventOrder:{[mr()]:[KN.name(),Fl.name()],[gr()]:["execute-on-form"],[Cr()]:["reflecting","execute-on-form"]},behaviours:El([$p.config({mode:"cyclic",onEscape:e=>(Rr(e,kk),A.some(!0)),useTabstopAt:e=>!a_(e)&&("button"!==We(e)||"disabled"!==Tt(e,"disabled")),firstTabstop:1}),KN.config({channel:`${i_}-${r}`,updateState:(e,t)=>(c.set(t.internalDialog.size),RL(t.internalDialog.size,e),s(),A.some(t)),initialData:e}),ih.config({}),oh("execute-on-form",f.concat([Jr(Ks(),((e,t)=>{$p.focusIn(e)})),jr(Er(),((e,t)=>{e.getSystem().broadcastOn([m_],{newFocus:t.event.newFocus})}))])),zM.config({getRoot:()=>A.some(b)}),th.config({}),YO({})]),components:[u.asSpec(),m.asSpec(),...h.map((e=>e.asSpec())).toArray()]}),v=$L({getId:x(r),getRoot:x(b),getFooter:()=>h.map((e=>e.get(b))),getBody:()=>m.get(b),getFormWrapper:()=>{const e=m.get(b);return _m.getCurrent(e).getOr(e)},toggleFullscreen:()=>{NL(b,c.get())}},t.redial,p);return{dialog:b,instanceApi:v}};var YL=tinymce.util.Tools.resolve("tinymce.util.URI");const XL=["insertContent","setContent","execCommand","close","block","unblock"],KL=e=>a(e)&&-1!==XL.indexOf(e.mceAction),JL=(e,t,o,n)=>{const s=da("dialog"),i=EL(e.title,s,n),l=(e=>{const t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[n_(A.none(),{dom:{tag:"iframe",attributes:{src:e.url}},behaviours:El([fk.config({}),ih.config({})])})]}],behaviours:El([$p.config({mode:"acyclic",useTabstopAt:C(a_)})])};return cR.parts.body(t)})(e),c=e.buttons.bind((e=>0===e.length?A.none():A.some(jL({buttons:e},s,n)))),u=((e,t)=>{const o=(e,t)=>jr(e,((e,o)=>{n(e,((n,s)=>{t(x,n,o.event,e)}))})),n=(e,t)=>{KN.getState(e).get().each((o=>{t(o,e)}))};return[...HL(o,t),o(Ok,((e,t,o)=>{t.onAction(e,{name:o.name})}))]})(0,ML((()=>y),n.shared.providers,t)),m={...e.height.fold((()=>({})),(e=>({height:e+"px","max-height":e+"px"}))),...e.width.fold((()=>({})),(e=>({width:e+"px","max-width":e+"px"})))},p=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],h=new YL(e.url,{base_uri:new YL(window.location.href)}),f=`${h.protocol}://${h.host}${h.port?":"+h.port:""}`,b=nc(),v=[KN.config({channel:`${i_}-${s}`,updateState:(e,t)=>A.some(t),initialData:e}),oh("messages",[Zr((()=>{const t=ac(ze(window),"message",(t=>{if(h.isSameOrigin(new YL(t.raw.origin))){const n=t.raw.data;KL(n)?((e,t,o)=>{switch(o.mceAction){case"insertContent":e.insertContent(o.content);break;case"setContent":e.setContent(o.content);break;case"execCommand":const n=!!d(o.ui)&&o.ui;e.execCommand(o.cmd,n,o.value);break;case"close":t.close();break;case"block":t.block(o.message);break;case"unblock":t.unblock()}})(o,x,n):(e=>!KL(e)&&a(e)&&ve(e,"mceAction"))(n)&&e.onMessage(x,n)}}));b.set(t)})),Qr(b.clear)]),Fl.config({channels:{[u_]:{onReceive:(e,t)=>{yi(e.element,"iframe").each((e=>{const o=e.dom.contentWindow;g(o)&&o.postMessage(t,f)}))}}}})],y=LL({id:s,header:i,body:l,footer:c,extraClasses:p,extraBehaviours:v,extraStyles:m},u,n),x=(e=>{const t=t=>{e.getSystem().isConnected()&&t(e)};return{block:e=>{if(!r(e))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t((t=>{Nr(t,Tk,{message:e})}))},unblock:()=>{t((e=>{Rr(e,Ek)}))},close:()=>{t((e=>{Rr(e,kk)}))},sendMessage:e=>{t((t=>{t.getSystem().broadcastOn([u_],e)}))}}})(y);return{dialog:y,instanceApi:x}},ZL=(e,t)=>Kn(Xn("data",t,e)),QL=e=>QS(e,".tox-alert-dialog")||QS(e,".tox-confirm-dialog"),ez=(e,t,o)=>t&&o?[]:[rM.config({contextual:{lazyContext:()=>A.some(Qo(ze(e.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"],lazyViewport:t=>JS(e,t.element).map((e=>({bounds:ZS(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Kt(e.element).top})}))).getOrThunk((()=>({bounds:on(),optScrollEnv:A.none()})))})],tz=e=>{const t=e.editor,o=uv(t),n=(e=>{const t=e.shared;return{open:(o,n)=>{const s=()=>{cR.hide(l),n()},r=Kh(jT({name:"close-alert",text:"OK",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"cancel",e)),a=xL(),i=yL(s,t.providers),l=di(CL({lazySink:()=>t.getSink(),header:vL(a,i),body:wL(o,t.providers),footer:A.some(SL(kL([],[r.asSpec()]))),onEscape:s,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[jr(Ck,s)],eventOrder:{}}));cR.show(l);const c=r.get(l);ih.focus(c)}}})(e.backstages.dialog),s=(e=>{const t=e.shared;return{open:(o,n)=>{const s=e=>{cR.hide(c),n(e)},r=Kh(jT({name:"yes",text:"Yes",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"submit",e)),a=jT({name:"no",text:"No",primary:!1,buttonType:A.some("secondary"),align:"end",enabled:!0,icon:A.none()},"cancel",e),i=xL(),l=yL((()=>s(!1)),t.providers),c=di(CL({lazySink:()=>t.getSink(),header:vL(i,l),body:wL(o,t.providers),footer:A.some(SL(kL([],[a,r.asSpec()]))),onEscape:()=>s(!1),extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[jr(Ck,(()=>s(!1))),jr(_k,(()=>s(!0)))],eventOrder:{}}));cR.show(c);const d=r.get(c);ih.focus(d)}}})(e.backstages.dialog),r=(t,o)=>$N.open(((t,n,s)=>{const r=n,a=((e,t,o)=>{const n=da("dialog"),s=e.internalDialog,r=EL(s.title,n,o),a=Ms(s.size),i=FL(a.get()).toArray(),l=((e,t,o)=>{const n=fL(e,t,A.none(),o,!1);return cR.parts.body(n)})({body:s.body,initialData:s.initialData},n,o),c=zL(s.buttons),d=VL(c),u=Ce(0!==c.length,jL({buttons:c},n,o)),m=PL((()=>f),ML((()=>p),o.shared.providers,t),o.shared.getSink),g={id:n,header:r,body:l,footer:u,extraClasses:i,extraBehaviours:[KN.config({channel:`${i_}-${n}`,updateState:(e,t)=>(a.set(t.internalDialog.size),RL(t.internalDialog.size,e),A.some(t)),initialData:e})],extraStyles:{}},p=LL(g,m,o),h={getId:x(n),getRoot:x(p),getBody:()=>cR.getBody(p),getFooter:()=>cR.getFooter(p),getFormWrapper:()=>{const e=cR.getBody(p);return _m.getCurrent(e).getOr(e)},toggleFullscreen:()=>{NL(p,a.get())}},f=$L(h,t.redial,d);return{dialog:p,instanceApi:f}})({dataValidator:s,initialData:r,internalDialog:t},{redial:$N.redial,closeWindow:()=>{cR.hide(a.dialog),o(a.instanceApi)}},e.backstages.dialog);return cR.show(a.dialog),a.instanceApi.setData(r),a.instanceApi}),t),a=(n,s,r,a)=>$N.open(((n,i,l)=>{const c=ZL(i,l),d=sc(),u=e.backstages.popup.shared.header.isPositionedAtTop(),m=()=>d.on((e=>{$h.reposition(e),o&&u||rM.refresh(e)})),g=qL({dataValidator:l,initialData:c,internalDialog:n},{redial:$N.redial,closeWindow:()=>{d.on($h.hide),t.off("ResizeEditor",m),d.clear(),r(g.instanceApi)}},e.backstages.popup,a.ariaAttrs,m),p=di($h.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:a.persistent?{event:"doNotDismissYet"}:{},...u?{}:{fireRepositionEventInstead:{}},inlineBehaviours:El([oh("window-manager-inline-events",[jr(_r(),((e,t)=>{Rr(g.dialog,Ck)}))]),...ez(t,o,u)]),isExtraPart:(e,t)=>QL(t)}));return d.set(p),$h.showWithinBounds(p,ui(g.dialog),{anchor:s},(()=>{const e=t.inline?St():ze(t.getContainer()),o=Qo(e);return A.some(o)})),o&&u||(rM.refresh(p),t.on("ResizeEditor",m)),g.instanceApi.setData(c),$p.focusIn(g.dialog),g.instanceApi}),n),i=(o,n,s,r)=>$N.open(((o,a,i)=>{const l=ZL(a,i),c=sc(),d=e.backstages.popup.shared.header.isPositionedAtTop(),u=()=>c.on((e=>{$h.reposition(e),rM.refresh(e)})),m=qL({dataValidator:i,initialData:l,internalDialog:o},{redial:$N.redial,closeWindow:()=>{c.on($h.hide),t.off("ResizeEditor ScrollWindow ElementScroll",u),c.clear(),s(m.instanceApi)}},e.backstages.popup,r.ariaAttrs,u),g=di($h.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:r.persistent?{event:"doNotDismissYet"}:{},...d?{}:{fireRepositionEventInstead:{}},inlineBehaviours:El([oh("window-manager-inline-events",[jr(_r(),((e,t)=>{Rr(m.dialog,Ck)}))]),rM.config({contextual:{lazyContext:()=>A.some(Qo(ze(t.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top","bottom"],lazyViewport:e=>JS(t,e.element).map((e=>({bounds:ZS(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Kt(e.element).top})}))).getOrThunk((()=>({bounds:on(),optScrollEnv:A.none()})))})]),isExtraPart:(e,t)=>QL(t)}));return c.set(g),$h.showWithinBounds(g,ui(m.dialog),{anchor:n},(()=>e.backstages.popup.shared.getSink().toOptional().bind((e=>{const o=JS(t,e.element).map((e=>ZS(e))).getOr(on()),n=Qo(ze(t.getContentAreaContainer())),s=tn(n,o);return A.some(Zo(s.x,s.y,s.width,s.height-15))})))),rM.refresh(g),t.on("ResizeEditor ScrollWindow ElementScroll ResizeWindow",u),m.instanceApi.setData(l),$p.focusIn(m.dialog),m.instanceApi}),o);return{open:(t,o,n)=>{if(!u(o)){if("toolbar"===o.inline)return a(t,e.backstages.popup.shared.anchors.inlineDialog(),n,o);if("bottom"===o.inline)return i(t,e.backstages.popup.shared.anchors.inlineBottomDialog(),n,o);if("cursor"===o.inline)return a(t,e.backstages.popup.shared.anchors.cursor(),n,o)}return r(t,n)},openUrl:(o,n)=>((o,n)=>$N.openUrl((o=>{const s=JL(o,{closeWindow:()=>{cR.hide(s.dialog),n(s.instanceApi)}},t,e.backstages.dialog);return cR.show(s.dialog),s.instanceApi}),o))(o,n),alert:(e,t)=>{n.open(e,t)},close:e=>{e.close()},confirm:(e,t)=>{s.open(e,t)}}};nn.add("silver",(e=>{(e=>{gb(e),(e=>{const t=e.options.register,o=e=>f(e,r)?{value:Nw(e),valid:!0}:{valid:!1,message:"Must be an array of strings."},n=e=>h(e)&&e>0?{value:e,valid:!0}:{valid:!1,message:"Must be a positive number."};t("color_map",{processor:o,default:["#BFEDD2","Light Green","#FBEEB8","Light Yellow","#F8CAC6","Light Red","#ECCAFA","Light Purple","#C2E0F4","Light Blue","#2DC26B","Green","#F1C40F","Yellow","#E03E2D","Red","#B96AD9","Purple","#3598DB","Blue","#169179","Dark Turquoise","#E67E23","Orange","#BA372A","Dark Red","#843FA1","Dark Purple","#236FA1","Dark Blue","#ECF0F1","Light Gray","#CED4D9","Medium Gray","#95A5A6","Gray","#7E8C8D","Dark Gray","#34495E","Navy Blue","#000000","Black","#ffffff","White"]}),t("color_map_background",{processor:o}),t("color_map_foreground",{processor:o}),t("color_cols",{processor:n,default:Hw(e)}),t("color_cols_foreground",{processor:n,default:Pw(e,Fw)}),t("color_cols_background",{processor:n,default:Pw(e,Rw)}),t("custom_colors",{processor:"boolean",default:!0}),t("color_default_foreground",{processor:"string",default:zw}),t("color_default_background",{processor:"string",default:zw})})(e),(e=>{const t=e.options.register;t("contextmenu_avoid_overlap",{processor:"string",default:""}),t("contextmenu_never_use_native",{processor:"boolean",default:!1}),t("contextmenu",{processor:e=>!1===e?{value:[],valid:!0}:r(e)||f(e,r)?{value:UI(e),valid:!0}:{valid:!1,message:"Must be false or a string."},default:"link linkchecker image editimage table spellchecker configurepermanentpen"})})(e)})(e);let t=()=>on();const{dialogs:o,popups:n,renderUI:s}=rR(e,{getPopupSinkBounds:()=>t()});qS(e,n.backstage.shared);const a=tz({editor:e,backstages:{popup:n.backstage,dialog:o.backstage}});return{renderUI:()=>{const o=s();return JS(e,n.getMothership().element).each((e=>{t=()=>ZS(e)})),o},getWindowManagerImpl:x(a),getNotificationManagerImpl:()=>((e,t,o)=>{const n=t.backstage.shared,s=()=>{const t=Qo(ze(e.getContentAreaContainer())),o=on(),n=el(o.x,t.x,t.right),s=el(o.y,t.y,t.bottom),r=Math.max(t.right,o.right),a=Math.max(t.bottom,o.bottom);return A.some(Zo(n,s,r-n,a-s))};return{open:(t,r)=>{const a=()=>{r(),$h.hide(l)},i=di(rb.sketch({text:t.text,level:R(["success","error","warning","warn","info"],t.type)?t.type:void 0,progress:!0===t.progressBar,icon:t.icon,onAction:a,iconProvider:n.providers.icons,translationProvider:n.providers.translate})),l=di($h.sketch({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:n.getSink,fireDismissalEventInstead:{},...n.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}}}));o.add(l),h(t.timeout)&&t.timeout>0&&qh.setEditorTimeout(e,(()=>{a()}),t.timeout);const c={close:a,reposition:()=>{const t=ui(i),o={maxHeightFunction:pc()},r=e.notificationManager.getNotifications();if(r[0]===c){const e={...n.anchors.banner(),overrides:o};$h.showWithinBounds(l,t,{anchor:e},s)}else F(r,c).each((e=>{const n=r[e-1].getEl(),a={type:"node",root:St(),node:A.some(ze(n)),overrides:o,layouts:{onRtl:()=>[pl],onLtr:()=>[pl]}};$h.showWithinBounds(l,t,{anchor:a},s)}))},text:e=>{rb.updateText(i,e)},settings:t,getEl:()=>i.element.dom,progressBar:{value:e=>{rb.updateProgress(i,e)}}};return c},close:e=>{e.close()},getArgs:e=>e.settings}})(e,{backstage:n.backstage},n.getMothership())}}))}();