'use strict';
const {
	excelT<PERSON><PERSON><PERSON>,
	jsonToexcel
} = require('ml-excel-to-json');
module.exports = {
	/**
	 * XXXnameXXX
	 * @url admin/file/kh/excelToJson 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, file } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		let keyMap = [{
				"label": "岗位名称",
				"key": "name"
			},
			{
				"label": "工作地址",
				"key": "work_address"
			},
			{
				"label": "学历要求",
				"key": "education_requirement"
			},
			{
				"label": "招聘类型",
				"key": "recruitment_type"
			},
			{
				"label": "岗位专业",
				"key": "major"
			},
			{
				"label": "岗位薪资",
				"key": "salary"
			},
			{
				"label": "招聘公司",
				"key": "company_name"
			},
			{
				"label": "公告原文链接",
				"key": "original_url"
			},
			{
				"label": "投递链接",
				"key": "application_url"
			},
			{
				"label": "招聘时间",
				"key": "start_date"
			},
			{
				"label": "截止时间",
				"key": "end_date"
			}
		]

		let json = await excelTojson(file);

		let sheetData = json.data

		const transformedData = sheetData.map(item => {
			const newItem = {};
			keyMap.forEach(mapItem => {
				let value = item[mapItem.label]
				switch (mapItem.key) {
					case 'start_date':
						value = vk.pubfn.timeFormat(value, 'yyyy-MM-dd hh:mm')
						break;
					case 'end_date':
						value = vk.pubfn.timeFormat(value, 'yyyy-MM-dd hh:mm')
						break;
					default:
						break;
				}
				newItem[mapItem.key] = value

			});
			return newItem;
		});
		
		
		// 匹配行政区


		console.log(transformedData);

		return transformedData

		// 业务逻辑结束-----------------------------------------------------------
		// return {
		// 	code: 0,
		// 	data: json
		// }
	}
}