'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url admin/major/classic/sys/get 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, level } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let dbName = 'major-list'
		let whereJson = {}
		if (level >= 2) {
			dbName = 'major-classic'
			whereJson.level = level - 1
		}
		res = await vk.baseDao.select({
			dbName,
			getCount: false,
			pageIndex: 1,
			pageSize: 5000,
			// 主表where条件
			whereJson,
			// 主表排序规则
			sortArr: [{ name: "_id", type: "desc" }],
		});

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}