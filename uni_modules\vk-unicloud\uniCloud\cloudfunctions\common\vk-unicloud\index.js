"use strict";function _interopDefault(e){return e&&"object"==typeof e&&"default"in e?e.default:e}var crypto$1=_interopDefault(require("crypto")),commonjsGlobal="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};let util={regExpTest:function(e,t){let n=!1;if("string"==typeof e){new RegExp(e).test(t)&&(n=!0)}else if("object"==typeof e)for(let a=0;a<e.length;a++){if(new RegExp(e[a]).test(t)){n=!0;break}}else"function"==typeof e&&(n=e(t));return n}};var util_1=util,onActionExecuting=async(e={})=>{let{serviceParam:t,middlewareService:n=[]}=e,a={code:403,msg:"access denied",filterStack:[]},{url:i}=t;for(let e in n){let r=n[e],{mode:o="onActionExecuting",enable:s=!0}=r;if(s&&"onActionExecuting"===o&&util_1.regExpTest(r.regExp,i)){let e;t.filterResponse=a;try{e=await r.main(t)||{code:-1}}catch(e){throw r.modulesName&&(e.msg=`云函数 ${i} 的中间件 ${r.modulesName}（id:${r.id}）运行异常：${e.message}`),e}if(e.filterId=r.id,a.filterStack.push(e),0!==e.code){a=e;break}a=Object.assign(a,e)}}return a},onActionExecuted=async(e={})=>{let{serviceParam:t,middlewareService:n=[],serviceRes:a}=e,{url:i}=t;for(let e in n){let r=n[e],{mode:o,enable:s=!0}=r;if(s&&"onActionExecuted"===o&&util_1.regExpTest(r.regExp,i)){let e;try{e=await r.main(t,a)}catch(e){throw r.modulesName&&(e.msg=`云函数 ${i} 的中间件 ${r.modulesName}（id:${r.id}）运行异常：${e.message}`),e}if(e){if(0!==e.code){a=e;break}a=1===r.returnMode?e:Object.assign(a,e)}}}return a},onActionIntercepted=async(e={})=>{let{serviceParam:t,middlewareService:n=[],filterResponse:a}=e,{url:i}=t;for(let e in n){let r=n[e],{mode:o,enable:s=!0}=r;if(s&&"onActionIntercepted"===o&&util_1.regExpTest(r.regExp,i)){let e=await r.main(t,a);if(e){if(0!==e.code){a=e;break}a=Object.assign(a,e)}}}return a},onActionError=async(e={})=>{let{serviceParam:t,middlewareService:n=[],serviceRes:a}=e,{url:i}=t;for(let e in n){let r=n[e],{mode:o,enable:s=!0}=r;if(s&&"onActionError"===o&&util_1.regExpTest(r.regExp,i)){let e=await r.main(t,a);if(e){if(0!==e.code){a=e;break}a=Object.assign(a,e)}}}return a},checkIsLogin={main:async e=>{let{url:t,data:n={},util:a,uid:i}=e,{uniID:r,config:o={},vk:s,db:d,_:c}=a,u=void 0!==n.needUserInfo?n.needUserInfo:n.need_user_info,l={code:-1,msg:""};void 0===u&&(u=-1==t.indexOf("."));let p=s.pubfn.getUniIdConfig(o,"tokenMaxLimit",10);1===p&&(u=!0);let f=0==t.indexOf("admin/");f&&(u=!0);let g=t.indexOf("sys.")>-1||t.indexOf("sys_")>-1||t.indexOf("/sys")>-1;if(g&&(u=!0),i)return l.code=0,l.msg="ok",l.uid=i,u&&(l.userInfo=await s.system.userDao.findById(i)),l;let m=await r.checkToken(e.uniIdToken,{needPermission:g,needUserInfo:u});if(m.code&&0!==m.code)return m;if(m.userInfo){let e=m.userInfo;e.permission=m.permission,delete e.token,delete e.password,l.userInfo=e}if(l.uid=m.uid,m.token){l.token=m.token,l.tokenExpired=m.tokenExpired;try{if(p>0){1===p&&(p=2);let t="uni-id-users",n=(await s.baseDao.findById({dbName:t,id:l.uid,fieldJson:{token:!0}})).token||[];n.length>p&&(n=n.splice(-1*p),-1===n.indexOf(e.uniIdToken)&&(n.unshift(e.uniIdToken),p=n.length),await s.baseDao.updateById({dbName:t,id:l.uid,dataJson:{token:c.set(n)}}))}}catch(e){}}if(f){if(!l.userInfo)return{code:403,msg:"needUserInfo必须为true"};{let e=l.userInfo.role||[];if(!l.userInfo.allow_login_background&&!e.includes("admin"))return{code:403,msg:"您无权限登录后台"}}}return l.code=0,l.msg="ok",l}},checkSysAuth={main:async e=>{let{url:t,data:n={},util:a}=e,{config:i,pubFun:r,vk:o,db:s,_:d}=a,c={code:-1,msg:""};const u=checkIsLogin;if(c=await u.main(e),0!==c.code)return c;if(!c.userInfo){return!1===(void 0!==n.needUserInfo?n.needUserInfo:n.need_user_info)?{code:403,msg:"请去除needUserInfo:false"}:{code:403,msg:"权限不足，请先登录"}}if(c.userInfo.role||(c.userInfo.role=[]),c.userInfo.role.includes("admin"))return c;if(!c.userInfo.allow_login_background&&0==t.indexOf("admin/"))return{code:403,msg:"您无权限登录后台"};let l=[];if(c.userInfo.role.includes("admin-lv3")){let e=await listPermission({whereJson:{level:d.in([1,2,3])},justNeedID:!0},a);o.pubfn.isNotNull(e)&&(l=l.concat(e))}if(c.userInfo.role.includes("admin-lv2")){let e=await listPermission({whereJson:{level:d.in([1,2])},justNeedID:!0},a);o.pubfn.isNotNull(e)&&(l=l.concat(e))}if(c.userInfo.role.includes("admin-lv1")){let e=await listPermission({whereJson:{level:d.in([1])},justNeedID:!0},a);o.pubfn.isNotNull(e)&&(l=l.concat(e))}if(c.userInfo.role.includes("query-all")){let e=await listPermission({whereJson:{curd_category:4,level:d.neq(4)},justNeedID:!0},a);o.pubfn.isNotNull(e)&&(l=l.concat(e))}let p=await listRole({role:c.userInfo.role},a);for(let e in p){let{permission:t}=p[e];o.pubfn.isNotNull(t)&&(l=l.concat(t))}if(0==l.length)return{code:403,msg:"权限不足"};l=[...new Set(l)];let f=await listPermission({whereJson:{permission_id:d.in(l),match_mode:d.in([1,2])}},a),g=!1;for(let e=0;e<f.length;e++){let n=f[e];if(1===n.match_mode){if(o.pubfn.wildcardTest(t,n.url)){g=!0;break}}else if(2===n.match_mode&&o.pubfn.regExpTest(t,n.url)){g=!0;break}}if(!g){await matchPermission({myPermission:l,url:t},a)&&(g=!0)}return g?(c.code=0,c.msg="ok",c):{code:403,msg:"权限不足"}}};async function listPermission(e={},t){let{vk:n,db:a,_:i}=t,{whereJson:r={},fieldJson:o={},justNeedID:s=!1}=e;s&&(o={permission_id:!0}),r.enable=!0;let d=[],c=await n.baseDao.select({dbName:"uni-id-permissions",pageIndex:1,pageSize:500,fieldJson:o,whereJson:r});if(s)for(let e=0;e<c.rows.length;e++){let t=c.rows[e];d.push(t.permission_id)}else d=c.rows;return d}async function matchPermission(e,t){let{vk:n,db:a,_:i}=t,{myPermission:r,url:o}=e;return!n.pubfn.isNull(r)&&await n.baseDao.count({dbName:"uni-id-permissions",whereJson:{enable:!0,permission_id:i.in(r),url:o,match_mode:i.nin([1,2])}})>0}async function listRole(e,t){let{vk:n,db:a,_:i}=t,{role:r}=e;return n.pubfn.isNull(r)?[]:(await n.baseDao.select({dbName:"uni-id-roles",whereJson:{role_id:i.in(r),enable:!0},fieldJson:{permission:!0}})).rows}var filterService=[{id:"pub",regExp:function(e=""){return"pub"===getType(e)},description:"pub函数为所有人都可以访问的函数",index:100,mode:"onActionExecuting",main:async function(e){let{url:t,data:n={},util:a}=e,{uniID:i}=a,r={};if(void 0!==n.needUserInfo?n.needUserInfo:n.need_user_info)r=await checkIsLogin.main(e);else if(e.uniIdToken){let t=await i.checkToken(e.uniIdToken,{needPermission:!1,needUserInfo:!1});0===t.code&&(r.uid=t.uid)}else e.uid&&(r.uid=e.uid);return r.code=0,r.msg="ok",r}},{id:"kh",regExp:function(e=""){return"kh"===getType(e)},description:"kh函数为必须登录后才能访问的函数(客户端用户)",index:200,mode:"onActionExecuting",main:checkIsLogin.main},{id:"sys",regExp:function(e=""){return"sys"===getType(e)},description:"sys函数为后端管理人员才能访问的函数(商家后台工作人员)",index:300,mode:"onActionExecuting",main:checkSysAuth.main},{id:"access_denied",regExp:function(e=""){return"access denied"===getType(e)},description:"._的是私有函数，禁止访问",index:100,mode:"onActionExecuting",main:function(){return{code:403,msg:"禁止访问私有函数！"}}}];function getType(e=""){let t="";return e.indexOf("/sys/")>-1?t="sys":e.indexOf("/kh/")>-1?t="kh":e.indexOf("/pub/")>-1&&(t="pub"),e.indexOf("/sys.")>-1||e.indexOf("/sys_")>-1||0===e.indexOf("sys.")||0===e.indexOf("sys_")?t="sys":e.indexOf("/kh.")>-1||e.indexOf("/kh_")>-1||0===e.indexOf("kh.")||0===e.indexOf("kh_")?t="kh":(e.indexOf("/pub.")>-1||e.indexOf("/pub_")>-1||0===e.indexOf("pub.")||0===e.indexOf("pub_"))&&(t="pub"),e.indexOf(".sys_")>-1?t="sys":e.indexOf(".kh_")>-1?t="kh":e.indexOf(".pub_")>-1&&(t="pub"),""==t&&e.indexOf(".")>-1&&(t="kh"),e.indexOf("._")>-1&&(t="access denied"),t}function getMiddleware(e){let t=[];if(e){let n=[...filterService,...e];n.sort((function(e,t){return e.index-t.index})),t=n.filter((e,t,a)=>{let i=[];return n.forEach((e,t)=>{i.push(e.id)}),i.indexOf(e.id)===t})}else t=filterService;return t}var getMiddleware_1=getMiddleware,filter={onActionExecuting:onActionExecuting,onActionExecuted:onActionExecuted,onActionIntercepted:onActionIntercepted,onActionError:onActionError,getMiddleware:getMiddleware_1};let routerUtil={filterService:filter,getQueryStringParameters:function(e){let{event:t,vk:n}=e,a={};if(t.httpMethod){let{path:e=""}=t;if("/"===e[0]&&(e=e.substring(1)),e){let i,{urlrewrite:r={}}=n.getUnicloud(),{rule:o}=r,s=n.pubfn.getData(r,"config.accessOnlyInRule"),d=!1;if(o)for(let t in o){let a=o[t],r=n.pubfn.regExpExecToTemplate(e,t,a);if(r){d=!0;let t=r.split("?");e=t[0],i=n.pubfn.urlStringToJson(t[1]);break}}if(!d&&s)return{mpserverlessComposedResponse:!0,statusCode:403,code:403,headers:{"content-type":"application/json"},body:JSON.stringify({code:403,msg:"access denied"})};if(a={data:{}},n.pubfn.isNotNull(i)&&(a.data=Object.assign(a.data,i)),t.queryStringParameters){let e=t.queryStringParameters;"string"==typeof e&&(e=JSON.parse(e)),a.data=Object.assign(a.data,e)}if(t.body){let e=t.body,i=t.headers&&t.headers["content-type"]?t.headers["content-type"]:"";if(i.indexOf("multipart/form-data;")>-1)e=n.formDataUtil.formParser(t),a.data=Object.assign(a.data,e);else{t.isBase64Encoded&&(e=Buffer.from(e,"base64").toString("utf-8"));try{"string"==typeof e&&(e=JSON.parse(e)),a.data=Object.assign(a.data,e)}catch(e){}try{"string"==typeof e&&i.indexOf("x-www-form-urlencoded")>-1&&(e=n.pubfn.urlStringToJson(e),"object"==typeof e&&null!==e&&(e=n.pubfn.string2Number(e),a.data=Object.assign(a.data,e)))}catch(e){}}t.encrypt&&(a.data=e)}a.$url||(a.data.$url?a.$url=a.data.$url:a.$url=e),a.data.uni_id_token&&(a.uni_id_token=a.data.uni_id_token,delete a.data.uni_id_token)}else{if(t.queryStringParameters){let e=t.queryStringParameters;"string"==typeof e.data&&(e.data=JSON.parse(e.data)),a=Object.assign(a,e)}if(t.body){let e=t.body;t.isBase64Encoded&&(e=Buffer.from(e,"base64").toString("utf-8"));try{"string"==typeof e&&(e=JSON.parse(e)),a=Object.assign(a,e)}catch(e){}}}try{let e=t.headers["uni-id-token"]||t.headers.uni_id_token;!a.uni_id_token&&e&&(a.uni_id_token=e)}catch(e){}}else a=JSON.parse(JSON.stringify(t));return a.data||(a.data={}),a.uniIdToken||(a.uniIdToken=a.uni_id_token),a.url=a.$url||"",a},returnError:returnError,filterInterception:async function({serviceParam:e,middlewareService:t,filterResponse:n}){try{n=await filter.onActionIntercepted({serviceParam:e,middlewareService:t,filterResponse:n})}catch(n){return await returnError({code:500,msg:`云函数 ${e.url} 的中间件 onActionIntercepted 运行异常!`,err:n,serviceParam:e,middlewareService:t})}return n},requireService:async function(e={}){let t,{vk:n,serviceParam:a}=e,{url:i,data:r,uniIdToken:o,util:s,filterResponse:d={},originalParam:c={}}=a;if(i.indexOf(".")>-1){let e=i.lastIndexOf("."),a=n.require("service/"+i.substring(0,e));t=n.pubfn.objectAssign({},a);const u=c.context.SOURCE;if(!t.isCloudObject&&"websocket"!==u)throw new Error("msg:禁止访问私有对象内的函数！");let l,p=i.substring(e+1);if(0==p.indexOf("_"))throw new Error("msg:禁止访问私有函数！");if("websocket"!==u&&["onWebsocketConnection","onWebsocketMessage","onWebsocketDisConnection","onWebsocketError"].indexOf(p)>-1)throw new Error("msg:禁止访问Websocket相关函数！");if("function"!=typeof t[p]){try{c.event&&c.event.httpMethod&&c.event.path&&(i=c.event.path.substring(1))}catch(e){}throw{code:404,name:"cloudObject",message:`not found【${i}】`}}Object.assign(t,{vk:n,methodName:p,isCloudObject:!0,getClientInfo:function(){let{context:e={}}=c,t={...e,os:e.OS,appId:e.APPID,locale:e.LOCALE,clientIP:e.CLIENTIP,userAgent:e.CLIENTUA,platform:e.PLATFORM,deviceId:e.DEVICEID,source:e.SOURCE,uniIdToken:o,uid:d.uid,userInfo:d.userInfo,filterResponse:d,originalParam:c,cid:e.cid};return n.pubfn.objectKeySort(t)},getUserInfo:async function(){if(d.userInfo&&d.userInfo._id)return d.userInfo;if(!l&&o){let e=await s.uniID.checkToken(o,{needUserInfo:!0});if(0!==e.code)throw new Error("token失效："+e.errMsg);e.userInfo&&e.userInfo._id&&(delete e.userInfo.token,delete e.userInfo.password,l=e.userInfo)}if(!l&&d.uid&&(l=await n.system.userDao.findById(d.uid,{token:!1,password:!1}),!l))throw new Error(`msg:异常，用户ID为${d.uid}的用户不存在`);return l},getUtil:function(){return s},getMethodName:function(){return p},getParams:function(){return r},getCloudInfo:function(){let{SPACEINFO:e={},FUNCTION_NAME:t,FUNCTION_TYPE:n,RUNTIME_ENV:a}=c.context||{},{provider:i,spaceId:r}=e;return{provider:i,spaceId:r,functionName:t,functionType:"cloudobject",runtimeEnv:a}},getUniIdToken:function(){return o},getUniCloudRequestId:function(){return c.context.requestId},getHttpInfo:function(){return c.event},getWebSocketManage:function(){const e=i.substring(0,i.lastIndexOf("."));return n.getWebSocketManage({url:e})},getCustomClientInfo:function(){return c.context.customInfo||{}}})}else t=n.require("service/"+i);return t},serviceRun:async function(e={}){let t,{serviceParam:n={},serviceMain:a}=e,{filterResponse:i}=n;if(i.uid&&(n.uid=i.uid),i.userInfo&&(n.userInfo=i.userInfo),a.isCloudObject){let{methodName:e="main"}=a;if("function"==typeof a._before){let e=await a._before();if("string"==typeof e)return{code:-1,msg:e};if("object"==typeof e)return e;if("boolean"==typeof e&&!1===e)return{code:-1,msg:""}}if(t=await a[e](n.data),"function"==typeof a._after){let e=await a._after({res:t||{}});void 0!==e&&(t=e)}}else t=await a.main(n);return"object"==typeof t&&null!==t&&(void 0===t.vk_uni_token&&"object"==typeof i&&i.token&&void 0!==i.tokenExpired&&(t.vk_uni_token={token:i.token,tokenExpired:i.tokenExpired}),t=routerUtil.returnRes(t)),t},errorCatch:async function(e={}){let{err:t,type:n,serviceParam:a,middlewareService:i,serviceMain:r={}}=e,{url:o,originalParam:s={}}=a;t||(t={});let{code:d}=t,c=t.message||t.msg||t.errMsg||"";"number"==typeof c&&(c=String(c));let u={code:500,msg:c,err:t,serviceParam:a,middlewareService:i};try{s.event&&s.event.httpMethod&&s.event.path&&(o=s.event.path.substring(1))}catch(t){}if(!t.message&&t.msg&&(t.message=t.msg||t.errMsg),"MODULE_NOT_FOUND"==d&&c.indexOf("service/")>-1||"ENOENT"==d&&c.indexOf("service")>-1?(routerUtil.check404({err:t,url:o,message:c}),Object.assign(u,{code:404,msg:t.message})):404==d&&["cloudFunction","cloudObject"].indexOf(t.name)>-1?(routerUtil.check404({err:t,url:o,message:c}),Object.assign(u,{code:404,msg:`not found【${o}】`})):"MODULE_NOT_FOUND"==d&&c.indexOf("Cannot find module")>-1?Object.assign(u,{code:500,msg:c}):"InternalServerError"==d&&c.indexOf("_id_ dup key")>-1?Object.assign(u,{code:500,msg:"vk.baseDao.add : _id不能重复添加"}):0===c.indexOf("Cannot read property 'mp-weixin' of undefined")?Object.assign(u,{code:501,msg:"请先绑定微信"}):c.indexOf("resource exhausted")>-1?Object.assign(u,{code:500,msg:"资源耗尽，请查看空间详情"}):c.indexOf("Response timeout for 10000ms")>-1?Object.assign(u,{code:502,msg:"timeout 请求超时，请重试！"}):c.indexOf("msg:token失效")>-1||c.indexOf("token失效：")>-1?(t.code=30202,Object.assign(u,{code:30202,errMsg:c,msg:c})):c.indexOf("msg:禁止访问私有对象内的函数")>-1||c.indexOf("msg:禁止访问私有函数")>-1?Object.assign(u,{code:403,msg:c.substring(4)}):0===c.indexOf("msg:")?(t.message=c.substring(4),Object.assign(u,{code:501,msg:c.substring(4)})):"ReferenceError"===t.name?Object.assign(u,{code:501,msg:`云函数 ${o} 变量引用错误：${c}`}):"TypeError"===t.name?Object.assign(u,{code:501,msg:`云函数 ${o} 运行异常，类型错误：${c}`}):"InternalServerError"==d?Object.assign(u,{code:"InternalServerError",msg:"内部错误"}):"Error"===t.name?Object.assign(u,{code:t.code,msg:c}):t.stack?Object.assign(u,{code:500,msg:"require"==n?`云函数 ${o} 编译异常!`:`云函数 ${o} 运行异常!`}):"string"==typeof t?Object.assign(u,{code:-1,msg:t}):Object.assign(u,t),"run"===n&&"function"==typeof r._after){let e=await r._after({err:t});if(void 0!==e)return e}return[30202,30203,30204].indexOf(u.code)>-1?{code:u.code,msg:u.msg}:await returnError(u)},check404:function(e={}){let{err:t,url:n,message:a}=e;a.split("stack:")[0].indexOf(n.split(".")[0])>-1&&(t.code=404,t.message=`Error: not found【${n}】`,t.stack=`Error: not found【${n}】`)},returnRes:function(e={}){return"object"==typeof e&&null!==e&&(void 0!==e.code||void 0!==e.errCode&&(e.code=e.errCode,e.msg=e.errMsg)),e},preferedPlatform:function(e){let{vk:t,context:n,config:a}=e;if("string"==typeof n.PLATFORM)if(n.uniPlatform||(n.uniPlatform=n.PLATFORM),["h5","web"].indexOf(n.PLATFORM)>-1){let e=t.pubfn.getUniIdConfig(a,"preferedWebPlatform","h5");n.PLATFORM=e}else if(["app-plus","app"].indexOf(n.PLATFORM)>-1||n.PLATFORM.indexOf("app-")>-1){let e=t.pubfn.getUniIdConfig(a,"preferedAppPlatform","app-plus");n.PLATFORM=e}},decryptClientData:function(e){let{vk:t,event:n,encryptMode:a,encryptKey:i,config:r}=e;try{n.data=t.crypto.aes.decrypt({mode:a,data:n.data,key:i})}catch(e){return{code:411,msg:"解密失败，参数不合法"}}if(!n.data)return{code:411,msg:"解密失败，参数不合法"};let{timeStamp:o=0}=n.data;return Date.now()-o>=1e3*t.pubfn.getData(r,"vk.clientCrypto.expTime",5)?{code:410,msg:"该请求已失效"}:(delete n.data.timeStamp,{code:0,msg:"解密成功"})},encryptRetrun:function(e){let{vk:t,encryptKey:n,encryptMode:a,returnRes:i}=e;try{const e=String(Date.now()),r=t.pubfn.random(6,"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"),o=t.crypto.md5(n+e+r);return i={encrypt:!0,data:`${t.crypto.aes.encrypt({mode:a,data:i,key:o})},${e},${r}`},i}catch(e){return{code:500,msg:"加密失败"}}},initContext:function(e){let{vk:t,config:n,event:a,context:i}=e,{data:r={}}=a;if(r){r.vk_appId&&(i.APPID=r.vk_appId),r.vk_appid&&(i.APPID=r.vk_appid),r.vk_platform&&(i.PLATFORM=r.vk_platform),r.vk_locale&&(i.LOCALE=r.vk_locale);let e={},o=[{name:"vk-app-name",key:"appName",decodeURIComponent:!0},{name:"vk-app-version",key:"appVersion"},{name:"vk-app-version-code",key:"appVersionCode",number:!0},{name:"vk-browser-name",key:"browserName",decodeURIComponent:!0},{name:"vk-browser-version",key:"browserVersion"},{name:"vk-app-wgt-version",key:"appWgtVersion"},{name:"vk-device-brand",key:"deviceBrand"},{name:"vk-device-model",key:"deviceModel"},{name:"vk-device-type",key:"deviceType"},{name:"vk-uni-compile-version",key:"uniCompileVersion"},{name:"vk-uni-runtime-version",key:"uniRuntimeVersion"},{name:"vk-uni-compiler-version",key:"uniCompilerVersion"}];"object"==typeof a.headers&&(!a.headers["vk-encrypt"]||"true"!==a.headers["vk-encrypt"]&&!0!==a.headers["vk-encrypt"]||(a.encrypt=!0),a.headers["vk-appId"]&&(e.appId=a.headers["vk-appId"]),a.headers["vk-appid"]&&(e.appid=a.headers["vk-appid"]),a.headers["vk-platform"]&&(e.platform=a.headers["vk-platform"]),a.headers["vk-locale"]&&(e.locale=a.headers["vk-locale"]),a.headers["vk-device-id"]&&(e.deviceId=a.headers["vk-device-id"]),a.headers["vk-client-ip"]&&(e.clientIP=a.headers["vk-client-ip"]),a.headers["vk-os"]&&(e.os=a.headers["vk-os"]),o.forEach(t=>{a.headers[t.name]&&(e[t.key]=a.headers[t.name])}));let s=a.vk_context||r.vk_context;if("object"==typeof s&&null!==s&&(e=t.pubfn.objectAssign(e,s),"object"==typeof r.vk_context&&delete r.vk_context),"object"==typeof e&&t.pubfn.isNotNull(e)&&(e.appId&&(i.APPID=e.appId),e.appid&&(i.APPID=e.appid),e.platform&&(i.PLATFORM=e.platform),e.locale&&(i.LOCALE=e.locale),e.os&&(i.OS=e.os),e.deviceId&&(i.DEVICEID=e.deviceId),e.clientIP&&!i.CLIENTIP&&(i.CLIENTIP=e.clientIP),e.userAgent&&!i.CLIENTUA&&(i.CLIENTUA=e.userAgent),e.cid&&!i.cid&&(i.cid=e.cid),o.forEach(t=>{let n=e[t.key];n&&(t.decodeURIComponent&&(n=decodeURIComponent(n)),t.number&&!isNaN(n)&&(n=Number(n)),i[t.key]=n)}),"function"===i.SOURCE&&(e.clientIP&&(i.CLIENTIP=e.clientIP),e.userAgent&&(i.CLIENTUA=e.userAgent))),t.pubfn.isNullOne(i.APPID,i.PLATFORM,i.LOCALE,i.CLIENTIP)){let e=t.pubfn.getData(n,"vk.context");t.pubfn.isNotNull(e)&&(i.APPID||(i.APPID=e.APPID),i.PLATFORM||(i.PLATFORM=e.PLATFORM),i.LOCALE||(i.LOCALE=e.LOCALE),i.CLIENTIP||(i.CLIENTIP=e.CLIENTIP))}i.locale||(i.locale=i.LOCALE),i.deviceId||(i.deviceId=i.DEVICEID),i.appId||(i.appId=i.APPID)}}};var routerUtil_1=routerUtil;async function returnError(e={}){let{code:t,msg:n,err:a,serviceParam:i,middlewareService:r}=e;console.error(n);let o={code:t,msg:n};a&&(a.stack?(n!==a.stack&&console.error(a.stack),o.err={message:a.message,stack:a.stack,code:a.code}):o.err=a);try{o.requestId=i.originalParam.context.requestId}catch(a){}let s=await filter.onActionError({serviceParam:i,middlewareService:r,serviceRes:o});return routerUtil.returnRes(s)}try{process.env.TZ="Asia/Shanghai"}catch(e){}async function main(e){let{event:t,context:n,vk:a,uid:i}=e;!a&&this&&(a=this);let{config:r,uniID:o,uniPay:s,db:d,middlewareService:c,pubFun:u,customUtil:l,crypto:p}=a.getUnicloud();if(a.pubfn.getData(r,"vk.system.serviceShutdown"))return routerUtil_1.returnRes({code:405,msg:a.pubfn.getData(r,"vk.system.serviceShutdownDescription")});routerUtil_1.initContext({vk:a,config:r,event:t,context:n});let f=routerUtil_1.getQueryStringParameters(e);const g=t.encrypt,m=a.crypto.md5(n.APPID+n.DEVICEID+f.uniIdToken||"undefined");if(g){t.data=f.data;let e=routerUtil_1.decryptClientData({vk:a,event:t,encryptMode:"aes-256-ecb",encryptKey:m,config:r});if(0!==e.code)return e;f.data=t.data}let{url:b,data:y={},uniIdToken:h}=f;if([403].indexOf(f.code)>-1)return routerUtil_1.returnRes(f);b&&"function"==typeof b.trim&&(b=b.trim()),routerUtil_1.preferedPlatform({vk:a,context:n,config:r});let w={event:t,context:n};const k=o.createInstance({context:n});d.command.$=d.command.aggregate;let _={vk:a,config:r,pubFun:u,uniID:k,uniPay:s,db:d,_:d.command,$:d.command.aggregate,customUtil:l,crypto:p,env:{APPID:n.APPID,CLIENTIP:n.CLIENTIP,DEVICEID:n.DEVICEID,LOCALE:n.LOCALE,OS:n.OS,PLATFORM:n.PLATFORM,CLIENTUA:n.CLIENTUA}};try{uniCloud.vk=a,uniCloud.env=_.env,uniCloud.context=n}catch(e){}let v={url:b,data:y,uniIdToken:h,uid:i,util:_,originalParam:w,encrypt:g};const $=routerUtil_1.filterService.getMiddleware(c);try{let e=await routerUtil_1.filterService.onActionExecuting({serviceParam:v,middlewareService:$});if(0!==e.code)return routerUtil_1.returnRes(await routerUtil_1.filterInterception({serviceParam:v,middlewareService:$,filterResponse:e}));delete y.uid,e.uid&&(y.uid=e.uid),v.filterResponse=e}catch(e){return await routerUtil_1.returnError({code:500,msg:e.msg||`云函数 ${b} 的中间件 onActionExecuting 运行异常!`,err:e,serviceParam:v,middlewareService:$})}let x,D;try{x=await routerUtil_1.requireService({vk:a,serviceParam:v})}catch(e){return await routerUtil_1.errorCatch({err:e,type:"require",serviceParam:v,middlewareService:$})}try{D=await routerUtil_1.serviceRun({serviceParam:v,serviceMain:x})}catch(e){return await routerUtil_1.errorCatch({err:e,type:"run",serviceParam:v,middlewareService:$,serviceMain:x})}try{D=await routerUtil_1.filterService.onActionExecuted({serviceParam:v,middlewareService:$,serviceRes:D})}catch(e){return await routerUtil_1.returnError({code:500,msg:e.msg||`云函数 ${b} 的中间件 onActionExecuted 运行异常!`,err:e,serviceParam:v,middlewareService:$})}let N=routerUtil_1.returnRes(D);return g&&(N=routerUtil_1.encryptRetrun({vk:a,encryptKey:m,encryptMode:"aes-256-ecb",returnRes:N})),N}var router=main,md5=function(e){return crypto$1.createHash("md5").update(e).digest("hex")};let util$1={},baseDao={init:function(e){util$1=e}};const DB_MAX_LIMIT=1e3,DB_PAGE_SIZE=10,DB_SELECT_ALL_MAX_LIMIT=1e8;baseDao.add=async function(e){let{db:t,_:n,vk:a,config:i}=util$1,{dbName:r,dataJson:o,db:s,cancelAddTime:d,cancelAddTimeStr:c}=e;if(a.pubfn.isNull(r))throw new Error("vk.baseDao.add 中 dbName 不能为空");if(a.pubfn.isNull(o))throw new Error("vk.baseDao.add 中 dataJson 不能为空");let u={...o},l=s||t;if(!u._add_time){let e=a.pubfn.getData(i,"vk.db.unicloud.cancelAddTime");void 0===d&&e&&(d=e);let t=a.pubfn.getData(i,"vk.db.unicloud.cancelAddTimeStr");if(void 0===c&&t&&(c=t),!d){let e=new Date;u._add_time=e.getTime(),c||(u._add_time_str=a.pubfn.timeFormat(e,"yyyy-MM-dd hh:mm:ss"))}}let p=await l.collection(r).add(u);return p.id?p.id:null},baseDao.adds=async function(e){let{db:t,_:n,vk:a,config:i}=util$1,{dbName:r,dataJson:o,db:s,cancelAddTime:d,cancelAddTimeStr:c,batchSize:u,needReturnIds:l}=e;if(a.pubfn.isNull(r))throw new Error("vk.baseDao.adds 中 dbName 不能为空");if(a.pubfn.isNull(o))throw new Error("vk.baseDao.adds 中 dataJson 不能为空");if("[object Array]"!==Object.prototype.toString.call(o))throw new Error("vk.baseDao.adds 中 dataJson 必须是数组对象");let p=s||t,f=a.pubfn.getData(i,"vk.db.unicloud.cancelAddTime");void 0===d&&f&&(d=f);let g=a.pubfn.getData(i,"vk.db.unicloud.cancelAddTimeStr");void 0===c&&g&&(c=g);let m=o.length,b=new Date,y=b.getTime(),h=a.pubfn.timeFormat(b,"yyyy-MM-dd hh:mm:ss"),w={};if("alipay"===a.pubfn.getUniCloudProvider()?(!u||u<0||u>1e3)&&(u=1e3):!u&&m>5e4&&(u=5e3),void 0===l&&(l=m<=1e5),u){w.ids=[];let e=a.pubfn.splitArray(o,u);o=null;for(let t=0;t<e.length;t++){let n=e[t];try{a.pubfn.getMemoryUsage().rate>=.8&&await a.pubfn.sleep(2e3)}catch(e){console.error("err: ",e)}if(!d)for(let e in n)n[e]._add_time||(n[e]._add_time=y,c||(n[e]._add_time_str=h));if(l){let e=await p.collection(r).add(n);w.ids=w.ids.concat(e.ids),e=null,w.length=w.ids.length}else{let e=await p.collection(r).add(n);w.length=e.ids.length,e=null}n=null}e=null}else{if(!d)for(let e in o)o[e]._add_time||(o[e]._add_time=y,c||(o[e]._add_time_str=h));w=await p.collection(r).add(o)}return w.ids?w.ids:w.id?w.id:null},baseDao.del=async function(e){let{vk:t,db:n,_:a}=util$1,{dbName:i,whereJson:r,db:o}=e,s=o||n,d=0;if(t.pubfn.isNotNull(r)){let e=await s.collection(i).where(r).remove();e?d=e.deleted:(console.error(e.errMsg),d=-1)}else console.error("whereJson条件不能为空");return d},baseDao.delete=async function(e){return await baseDao.del(e)},baseDao.remove=async function(e){return await baseDao.del(e)},baseDao.deleteById=async function(e){let{vk:t,db:n,_:a}=util$1,{dbName:i,id:r,db:o}=e,s=o||n,d=0;if(t.pubfn.isNull(r))throw new Error("deleteById的id不能为空,且必须是字符串");let c=await s.collection(i).doc(r).remove();return c?d=c.deleted:(console.error(c.errMsg),d=0),d},baseDao.update=async function(e){let t,{vk:n,db:a,_:i}=util$1,{dbName:r,whereJson:o,dataJson:s,db:d}=e,c=d||a,u=0;if(n.pubfn.isNull(o))throw new Error("update的whereJson不能为空，且必须是对象形式");if(n.pubfn.isNull(s))throw new Error("update的dataJson不能为空，且必须是对象形式");return s._id&&delete s._id,t=void 0===o?await c.collection(r).update(s):await c.collection(r).where(o).update(s),t?u=t.updated:console.error(t.errMsg),u},baseDao.updateById=async function(e){let{vk:t,db:n,_:a}=util$1,{dbName:i,id:r,dataJson:o,getUpdateData:s,db:d}=e,c=d||n,u=0;if(t.pubfn.isNull(r))throw new Error("updateById的id不能为空，且必须是字符串");if(t.pubfn.isNull(o))throw new Error("updateById的dataJson不能为空，且必须是对象形式");o._id===r&&delete o._id;let l=await c.collection(i).doc(r).update(o);return s?l?await baseDao.findById({db:c,dbName:i,id:r}):null:(l?u=l.updated:(console.error(l.errMsg),u=0),u)},baseDao.updateAndReturn=async function(e){let{vk:t,db:n,_:a}=util$1,{dbName:i,id:r,whereJson:o,dataJson:s,db:d}=e,c=d||n;if(t.pubfn.isNullAll(r,o))throw new Error("updateAndReturn的id和whereJson两者不能都为空");if(t.pubfn.isNull(s))throw new Error("updateAndReturn的dataJson不能为空，且必须是对象形式");if(s._id&&delete s._id,t.pubfn.isNotNull(r)){return(await c.collection(i).doc(r).updateAndReturn(s)).doc}if(t.pubfn.isNotNull(o)){return(await c.collection(i).where(o).updateAndReturn(s)).doc}throw new Error("updateAndReturn的id和whereJson两者不能都为空")},baseDao.setById=async function(e){let{vk:t,db:n,_:a}=util$1,{dbName:i,id:r,dataJson:o,db:s}=e,d=s||n;if(t.pubfn.isNull(o))throw new Error("setById的dataJson不能为空，且必须是对象形式");if(t.pubfn.isNullAll(r,o._id))throw new Error("id或dataJson._id不能均为空");if(t.pubfn.isNotNullAll(r,o._id)&&r!==o._id)throw new Error("id和dataJson._id必须一致");t.pubfn.isNotNull(o._id)&&(r=o._id,delete o._id);let c=await d.collection(i).doc(r).set(o);return c.updated>0?c.type="update":(c.type="add",c.id=c.upsertedId||r),c},baseDao.select=async function(e={}){let{vk:t,db:n,_:a}=util$1;"string"==typeof e.pageIndex&&(e.pageIndex=parseInt(e.pageIndex)),"string"==typeof e.pageSize&&(e.pageSize=parseInt(e.pageSize));let{dbName:i,whereJson:r,pageSize:o=DB_PAGE_SIZE,getOne:s=!1,getMain:d=!1,debug:c=!1,hasMore:u=!1}=e;if(s&&(o=1),o<=0&&(o=1e8),o>DB_MAX_LIMIT)return await baseDao.selectAll(e);let l,p={rows:0,count:0},f=await baseDao.getSelectData(e),{result:g,hasMore:m,total:b,getCount:y,pageIndex:h,fieldJson:w,runTime:k=0}=f;y&&(u=!1);let _,v=u?o+1:o;return g=g.skip((h-1)*o).limit(v),t.pubfn.isNotNull(w)&&(g=g.field(w)),c&&(l=Date.now()),g=await g.get(),c&&(p.count=k,p.rows=Date.now()-l,p.total=p.rows+p.count),c&&(_={runTime:p}),baseDao.returnSelectsRes({rows:g.data,getCount:y,total:b,hasMore:m,pageIndex:h,pageSize:o,getOne:s,getMain:d,needAccurateHasMore:u,debug:_})},baseDao.findById=async function(e){let{vk:t,db:n,_:a}=util$1,{dbName:i,id:r,fieldJson:o,db:s}=e,d=(s||n).collection(i).doc(r);o&&(d=d.field(o));let c=await d.get();return"[object Array]"===Object.prototype.toString.call(c.data)?c.data[0]:c.data},baseDao.findByWhereJson=async function(e){let{vk:t,db:n,_:a}=util$1,{dbName:i,whereJson:r,fieldJson:o,sortArr:s,db:d}=e,c=d||n;if(t.pubfn.isNotNull(r)){let e=c.collection(i).where(r);t.pubfn.isNotNull(s)&&(e=baseDao.getSortArrForSelect({result:e,sortArr:s})),t.pubfn.isNotNull(o)&&(e=e.field(o));let n=await e.limit(1).get();if(n.data&&n.data.length>0)return n.data[0]}else console.error("whereJson条件不能为空");return null},baseDao.count=async function(e){let t,{vk:n,db:a,_:i}=util$1,{dbName:r,whereJson:o,foreignDB:s,foreignKey:d,groupJson:c,lastWhereJson:u,db:l}=e,p=l||a;if(n.pubfn.isNotNull(s)||n.pubfn.isNotNull(c)){let e=p.collection(r).aggregate();return n.pubfn.isNotNull(o)&&e.match(o),n.pubfn.isNotNull(c)&&e.group(c),n.pubfn.isNotNull(s)&&(e=baseDao.addForeignDB({foreignDB:s,foreignKey:d,result:e})),n.pubfn.isNotNull(u)&&(e=e.match(u)),e=await e.count("total").end(),e.data[0]?e.data[0].total:0}return t=n.pubfn.isNotNull(o)?await p.collection(r).where(o).count():await p.collection(r).count(),t.total},baseDao.getSelectData=async function(e){let{vk:t,db:n,_:a}=util$1,{dbName:i,whereJson:r,pageIndex:o=1,pageSize:s=DB_PAGE_SIZE,getCount:d=!1,getOne:c=!1,db:u,debug:l=!1}=e;s<=0&&(o=1,s=1e8),c&&(s=1,d=!1);let p,f,g=u||n,m=e.sortArr,b=e.fieldJson,y=0,h=!1;if(d){let e;l&&(p=Date.now()),e=t.pubfn.isNotNull(r)?await g.collection(i).where(r).count():await g.collection(i).count(),y=e.total,o<Math.ceil(y/s)&&(h=!0),l&&(f=Date.now()-p)}let w=g.collection(i);return t.pubfn.isNotNull(r)&&(w=w.where(r)),t.pubfn.isNotNull(m)&&(w=baseDao.getSortArrForSelect({result:w,sortArr:m})),{result:w,dbName:i,whereJson:r,pageIndex:o,pageSize:s,getCount:d,sortArr:m,fieldJson:b,total:y,hasMore:h,runTime:f,runDB:g}},baseDao.selectAll=async function(e){let{db:t,_:n,vk:a,config:i}=util$1,{dbName:r,getOne:o=!1,getMain:s=!1,debug:d=!1}=e,c=DB_MAX_LIMIT,u=a.pubfn.getData(i,"vk.db.unicloud.maxLimit");u&&(c=u,(c<=0||c>DB_MAX_LIMIT)&&(c=DB_MAX_LIMIT));let l={rows:0,count:0,task:{avg:0,list:[]}},p=await baseDao.getSelectData(e),{result:f,hasMore:g,total:m,getCount:b,pageIndex:y,pageSize:h,fieldJson:w,sortArr:k,runTime:_=0,whereJson:v,runDB:$}=p;h>0&&!m&&!b&&(m=h);let x,D={};if(b&&0===m)D={data:[]};else{let e=h<m?h:m,t=Math.ceil(e/c),i=e%c;i||(i=c);let o=(y-1)*h,s=[];a.pubfn.isNull(k)&&(k=[{name:"_id",type:"asc"}]);let u="";1===k.length&&"_id"===k[0].name&&(u=k[0].type);let p="";for(let e=0;e<t;e++){let g,m=o+e*c,b=e===t-1?i:c,y=f;if(u){y=$.collection(r);let t=[];p&&t.push({_id:"asc"===u?n.gt(p):n.lt(p)}),a.pubfn.isNotNull(v)&&t.push(v),t.length>0&&(y=y.where(n.and(t))),a.pubfn.isNotNull(k)&&(y=baseDao.getSortArrForSelect({result:y,sortArr:k})),0===e&&(y=y.skip(o)),y=y.limit(b)}else y=f.skip(m).limit(b);a.pubfn.isNotNull(w)&&(y=y.field(w)),d&&(g=Date.now());let h=await y.get();if(d&&(l.task.list[e]=Date.now()-g),s=s.concat(h.data),0===h.data.length||h.data.length<c)break;if(u){let e=h.data[h.data.length-1]._id;if(e===p){console.log("数据查询出现问题，_id重复，"+p);break}p=e}}if(D.data=s,d){let e=l.task.list.reduce((e,t)=>e+t,0);l.count=_,l.rows=e,l.total=l.rows+l.count,e>0&&(l.task.avg=a.pubfn.toDecimal(e/l.task.list.length,0))}}return d&&(x={runTime:l}),baseDao.returnSelectsRes({rows:D.data,getCount:b,total:m,hasMore:g,pageIndex:y,pageSize:h,getOne:o,getMain:s,needAccurateHasMore:!1,debug:x})},baseDao.sum=async function(e){let{vk:t,db:n,_:a}=util$1,{dbName:i,fieldName:r,whereJson:o,db:s}=e,d=s||n;const c=d.command.aggregate;let u=d.collection(i).aggregate();t.pubfn.isNotNull(o)&&u.match(o),u.group({_id:null,num:c.sum("$"+r)});let l=await u.end();return l.data&&l.data[0]?l.data[0].num:0},baseDao.avg=async function(e){let{vk:t,db:n,_:a}=util$1,{dbName:i,fieldName:r,whereJson:o,db:s}=e,d=s||n;const c=d.command.aggregate;let u=d.collection(i).aggregate();t.pubfn.isNotNull(o)&&u.match(o),u.group({_id:null,num:c.avg("$"+r)});let l=await u.end();return l.data&&l.data[0]?l.data[0].num:null},baseDao.max=async function(e){let{vk:t,db:n,_:a}=util$1,{dbName:i,fieldName:r,whereJson:o,db:s}=e,d=s||n;const c=d.command.aggregate;let u=d.collection(i).aggregate();t.pubfn.isNotNull(o)&&u.match(o),u.group({_id:null,num:c.max("$"+r)});let l=await u.end();return l.data&&l.data[0]?l.data[0].num:null},baseDao.min=async function(e){let{vk:t,db:n,_:a}=util$1,{dbName:i,fieldName:r,whereJson:o,db:s}=e,d=s||n;const c=d.command.aggregate;let u=d.collection(i).aggregate();t.pubfn.isNotNull(o)&&u.match(o),u.group({_id:null,num:c.min("$"+r)});let l=await u.end();return l.data&&l.data[0]?l.data[0].num:null},baseDao.sample=async function(e){let{vk:t,db:n,_:a}=util$1,{dbName:i,whereJson:r,size:o,fieldJson:s,db:d}=e,c=d||n;c.command.aggregate;let u=c.collection(i).aggregate();return t.pubfn.isNotNull(r)&&u.match(r),u.sample({size:o}).limit(o),t.pubfn.isNotNull(s)&&u.project(s),(await u.end()).data},baseDao.selects=async function(e={}){let{vk:t,db:n,_:a}=util$1;if(t.pubfn.isNotNull(e.treeProps))return await baseDao.tree(e);"string"==typeof e.pageIndex&&(e.pageIndex=parseInt(e.pageIndex)),"string"==typeof e.pageSize&&(e.pageSize=parseInt(e.pageSize));let{db:i,dbName:r,foreignKey:o="_id",pageIndex:s=1,pageSize:d=DB_PAGE_SIZE,getCount:c=!1,getOne:u=!1,getMain:l=!1,geoNearJson:p,whereJson:f={},unwindJson:g,groupJson:m,sortArr:b=[],foreignDB:y=[],lastWhereJson:h,lastSortArr:w=[],addFields:k,fieldJson:_={},debug:v=!1,hasMore:$=!1}=e,x=i||n;d<=0&&(s=1,d=1e8),u&&(d=1,c=!1),c&&($=!1);let D,N=0,I=!1,S={rows:0,count:0};if(t.pubfn.isNotNull(f)&&(p=baseDao.createGeoNearJson({geoNearJson:p,whereJson:f})),c){if(v&&(D=Date.now()),t.pubfn.isNullAll(m,g,h,p)){let e;e=t.pubfn.isNotNull(f)?await x.collection(r).where(f).count():await x.collection(r).count(),N=e.total}else{let e=x.collection(r).aggregate();t.pubfn.isNotNull(p)?e=baseDao.getGeoNearJson({runDB:x,result:e,geoNearJson:p,whereJson:f}):t.pubfn.isNotNull(f)&&e.match(f),t.pubfn.isNotNull(g)&&(e=baseDao.getTnwind({result:e,unwindJson:g})),t.pubfn.isNotNull(m)&&e.group(m),t.pubfn.isNotNull(h)&&(t.pubfn.isNotNull(y)&&(e=baseDao.addForeignDB({foreignDB:y,foreignKey:o,result:e})),e.match(h)),e=await e.count("total").end(),N=e.data[0]?e.data[0].total:0}s<Math.ceil(N/d)&&(I=!0),v&&(S.count=Date.now()-D)}a.aggregate;let T=x.collection(r).aggregate();T=t.pubfn.isNotNull(p)?baseDao.getGeoNearJson({runDB:x,result:T,geoNearJson:p,whereJson:f}):T.match(f),t.pubfn.isNotNull(g)&&(T=baseDao.getTnwind({result:T,unwindJson:g})),t.pubfn.isNotNull(m)&&(T=T.group(m)),t.pubfn.isNotNull(b)&&(T=baseDao.getSortArrForAggregate({result:T,sortArr:b}));let C,A=t.pubfn.isNullAll(h,w);if(A){let e=$?d+1:d;T=T.skip((s-1)*d).limit(e)}if(T=baseDao.addForeignDB({foreignDB:y,foreignKey:o,result:T}),t.pubfn.isNotNull(h)&&(T=T.match(h)),t.pubfn.isNotNull(w)&&(T=baseDao.getSortArrForAggregate({result:T,sortArr:w})),!A){let e=$?d+1:d;T=T.skip((s-1)*d).limit(e)}return t.pubfn.isNotNull(k)&&(T=T.addFields(k)),t.pubfn.isNotNull(_)&&(_=baseDao.foreignDBToProject({fieldJson:_,foreignDB:y,foreignKey:o}),T=T.project(_)),v&&(D=Date.now()),T=await T.end(),v&&(S.rows=Date.now()-D,S.total=S.rows+S.count),v&&(C={runTime:S}),baseDao.returnSelectsRes({rows:T.data,getCount:c,total:N,hasMore:I,pageIndex:s,pageSize:d,getOne:u,getMain:l,needAccurateHasMore:$,debug:C})},baseDao.returnSelectsRes=function(e){let{rows:t,getCount:n,total:a,hasMore:i,pageIndex:r,pageSize:o,getOne:s,getMain:d,needAccurateHasMore:c,debug:u}=e,l={};if(n)l.total=a,l.hasMore=i;else{let e=t?t.length:0;c?e>=o+1?(l.hasMore=!0,t.pop()):l.hasMore=!1:l.hasMore=e>=o,e=t?t.length:0,l.total=(r-1)*o+e}return l.rows=t,l.code=0,l.msg="查询成功",l.pagination={pageIndex:r,pageSize:o},l.getCount=n,u&&(l.debug=u),s&&(l.rows=l.rows[0]),d?l.rows:l},baseDao.listToObjectByLimit1=function(e){let{vk:t,db:n,_:a}=util$1,{list:i,foreignDB:r}=e;if(t.pubfn.isNotNull(r))for(let e in i)for(let n in r){let{as:a,limit:o,foreignDB:s,dbName:d}=r[n];a||(a=d),t.pubfn.isNotNull(s)&&(i[e][a]=baseDao.listToObjectByLimit1({list:i[e][a],foreignDB:s})),1===o&&(t.pubfn.isArray(i[e][a])?i[e][a]&&i[e][a].length>0?i[e][a]=i[e][a][0]:i[e][a]={}:void 0===i[e][a]&&(i[e][a]={}))}return i},baseDao.addForeignDB=function(e){let{vk:t,db:n,_:a}=util$1,{foreignDB:i,foreignKey:r,result:o}=e;const s=a.aggregate;for(let e in i){let n,{dbName:d,foreignKey:c,localKey:u,localKeyType:l="",foreignKeyType:p="",as:f,limit:g,getOne:m,whereJson:b,fieldJson:y,sortArr:h,foreignDB:w,addFields:k}=i[e];f||(f=d),n=t.pubfn.isNotNull(u)?u:"object"==typeof r?r[e]:r;let _,v="string"==typeof n?"$"+n:n,$="string"==typeof c?"$"+c:c,x="$$foreignKey"+baseDao.getForeignKeyName(n),D="foreignKey"+baseDao.getForeignKeyName(n);_="array"===l.toLowerCase()?[s.cond({if:s.isArray(x),then:s.in([$,x]),else:s.eq([$,x])})]:"array"===p.toLowerCase()?[s.cond({if:s.isArray($),then:s.in([x,$]),else:s.eq([$,x])})]:[s.eq([$,x])];let N=s.pipeline().match(a.expr(s.and(_)));t.pubfn.isNotNull(b)&&(N=N.match(b)),t.pubfn.isNotNull(h)&&(N=baseDao.getSortArrForAggregate({result:N,sortArr:h})),g&&(N=N.limit(g)),t.pubfn.isNotNull(w)&&(N=baseDao.addForeignDB({foreignDB:w,result:N})),t.pubfn.isNotNull(k)&&(N=N.addFields(k)),t.pubfn.isNotNull(y)&&(y=baseDao.foreignDBToProject({fieldJson:y,foreignDB:w}),N=N.project(y)),N=N.done();let I={};I[D]=v;let S={from:d,let:I,pipeline:N,as:f};o=o.lookup(S),(1===g&&!1!==m||!0===m)&&(o=o.unwind({path:"$"+f,preserveNullAndEmptyArrays:!0}))}return o},baseDao.getForeignKeyName=function(e){return"string"==typeof e?e.replace(new RegExp("\\.","g"),"__"):"__vk__foreignKey1"},baseDao.addWhereJson=function(e,t="whereJson"){let{vk:n,db:a,_:i}=util$1,{formData:r,columns:o}=e,s={};for(let e in o){let a,d=o[e],{key:c,mode:u,defaultValue:l,type:p="",lastWhereJson:f,auxiliary:g=!0,trim:m=!0,isNumber:b=!1}=d;if("lastWhereJson"===t&&!f)continue;if("lastWhereJson"!==t&&f)continue;let y=c;if(n.pubfn.isNotNull(d.fieldName)&&(y=d.fieldName),a=n.pubfn.isNotNull(d.value)?d.value:r[c],n.pubfn.isNull(a)&&n.pubfn.isNotNull(l)&&(a=l),n.pubfn.isNull(u)&&(u=["address","province","city","area"].indexOf(p)>-1?"address":"[object Array]"===Object.prototype.toString.call(a)&&a.length>=2?"[]":"="),n.pubfn.isNotNull(a))if("string"==typeof a&&m&&"function"==typeof a.trim&&(a=a.trim()),b&&!isNaN(a)&&(a=Number(a)),"custom"===u);else if("%%"===u)try{s[y]=new RegExp(a)}catch(e){}else if("%*"===u)try{s[y]=new RegExp("^"+a)}catch(e){}else if("*%"===u)try{s[y]=new RegExp(a+"$")}catch(e){}else if(">"===u)s[y]=s[y]?s[y].gt(a):i.gt(a);else if(">="===u)s[y]=s[y]?s[y].gte(a):i.gte(a);else if("<"===u)s[y]=s[y]?s[y].lt(a):i.lt(a);else if("<="===u)s[y]=s[y]?s[y].lte(a):i.lte(a);else if("in"===u)s[y]=i.in(a);else if("nin"===u)s[y]=i.nin(a);else if("!="===u)s[y]=i.neq(a);else if("[]"===u)s[y]=i.gte(a[0]).lte(a[1]);else if("[)"===u)s[y]=i.gte(a[0]).lt(a[1]);else if("(]"===u)s[y]=i.gt(a[0]).lte(a[1]);else if("()"===u)s[y]=i.gt(a[0]).lt(a[1]);else if("address"===u){let e={};a.province&&a.province.code&&(e["province.code"]=a.province.code),a.city&&a.city.code&&(e["city.code"]=a.city.code),a.area&&a.area.code&&(e["area.code"]=a.area.code),s[y]=e}else s[y]=g?"___empty-array___"===a?[]:"___empty-object___"===a?{}:"___non-existent___"===a?i.exists(!1):"___existent___"===a?i.exists(!0):a:a}return s},baseDao.getTableData=async function(e){let{vk:t,db:n,_:a,config:i}=util$1,{db:r,dbName:o,data:s={},getCount:d,getMain:c,geoNearJson:u,whereJson:l,unwindJson:p,fieldJson:f,sortArr:g,treeProps:m,groupJson:b,foreignKey:y,foreignDB:h,lastWhereJson:w,lastSortArr:k,addFields:_,debug:v}=e,{pageIndex:$,pageSize:x,pagination:D,sortRule:N,lastSortRule:I,formData:S,columns:T,getCount:C,total:A,fieldJson:O={}}=s;D&&($=D.pageIndex,x=D.pageSize);let E={},M=[],P={};if(t.pubfn.isNotNull(g))M=g;else{let e=t.pubfn.getData(i,"vk.db.unicloud.getTableData.sortArr");t.pubfn.isNotNull(e)?M=e:M.push({name:"_id",type:"desc"})}t.pubfn.isNotNull(N)&&(M=N),t.pubfn.isNotNull(I)&&(k=I),E=baseDao.addWhereJson(s,"whereJson"),P=baseDao.addWhereJson(s,"lastWhereJson"),t.pubfn.isNotNull(l)&&(u=baseDao.createGeoNearJson({geoNearJson:u,whereJson:l}),E=a.and([E,l])),t.pubfn.isNotNull(w)&&(P=a.and([P,w])),t.pubfn.isNotNull(f)&&t.pubfn.objectAssign(O,f);let q={};if(t.pubfn.isNullAll(h,b,m,p,u,_))void 0===d&&(d=!0),!1===C&&(d=!1),q=await t.baseDao.select({db:r,dbName:o,getMain:c,getCount:d,pageIndex:$,pageSize:x,whereJson:E,sortArr:M,fieldJson:O,debug:v,hasMore:!0});else{if(void 0===d){d=!!t.pubfn.isNullAll(P,k)}!1===C&&(d=!1),q=await t.baseDao.selects({db:r,dbName:o,foreignKey:y,getMain:c,getCount:d,pageIndex:$,pageSize:x,geoNearJson:u,whereJson:E,unwindJson:p,treeProps:m,groupJson:b,sortArr:M,foreignDB:h,lastWhereJson:P,lastSortArr:k,addFields:_,fieldJson:O,debug:v,hasMore:!0})}return A&&!1===q.getCount&&(q.total=A),q},baseDao.startTransaction=async function(e){let{vk:t,db:n,_:a}=util$1;return await n.startTransaction()},baseDao.rollbackTransaction=async function(e){let{db:t,msg:n="【异常】操作失败",tips:a="事务已回滚。",err:i={},code:r=-1}=e;console.error("transaction error",i);let o={code:r,msg:n,tips:a};await t.rollback();let s={message:i.message,stack:i.stack};try{s.body=JSON.parse(i.message),"object"==typeof s.body&&void 0!==s.body.code&&(s.body.msg,1)&&(o.msg=s.body.msg)}catch(e){}return console.error("transaction errJson",s),o.err=s,o},baseDao.tree=async function(e){let{dbName:t,whereJson:n={},pageIndex:a=1,pageSize:i=DB_PAGE_SIZE,getCount:r=!1,sortArr:o=[],fieldJson:s={},lastWhereJson:d,treeProps:c={},foreignDB:u=[]}=e;e.foreignDB||(e.foreignDB=[]);let{id:l="_id",parent_id:p="parent_id",children:f="children",level:g=10,limit:m=DB_MAX_LIMIT}=c;if(g<1||g>20)throw new Error("msg:treeProps.level的范围必须在[1,20]");delete e.treeProps,e.whereJson||(e.whereJson={[p]:null});let b={dbName:t,localKey:l,foreignKey:p,as:f,limit:m,whereJson:c.whereJson,sortArr:c.sortArr,addFields:c.addFields,fieldJson:c.fieldJson||e.fieldJson,foreignDB:[...e.foreignDB]};const y=function(e,t,n){if(0===t)return e;for(let t=0;t<u.length;t++){let n=u[t];e.push(n)}return e.unshift({...b,foreignDB:y([...e],t-1)}),e};return e.foreignDB=y([],g),await baseDao.selects(e)},baseDao.checkTruthyFalsy=function(e){let t=!1,n=!1;const a=Object.keys(e);for(const i of a){const a=e[i];if(("_id"!==i||a)&&(a?t=!0:n=!0,t&&n))return 2}return t?1:0},baseDao.foreignDBToProject=function(e){let{fieldJson:t,foreignDB:n,foreignKey:a}=e,i=baseDao.checkTruthyFalsy(t);if(2===i)for(let e in t)"_id"===e||t[e]||delete t[e];if(1===i)for(let e in n){let{as:a,dbName:i}=n[e];a?t[a]=!0:t[i]=!0}return t},baseDao.getTnwind=function(e={}){let{vk:t,db:n,_:a}=util$1,{result:i,unwindJson:r}=e;if(t.pubfn.isNotNull(r)){let{path:e,includeArrayIndex:n,preserveNullAndEmptyArrays:a,replaceRoot:o,unwindWhereJson:s,replaceRootWhereJson:d}=r,c={path:e,includeArrayIndex:n,preserveNullAndEmptyArrays:a};i=i.unwind(c),t.pubfn.isNotNull(s)&&(i=i.match(s)),o&&(i=i.replaceRoot({newRoot:e}),t.pubfn.isNotNull(d)&&(i=i.match(d)))}return i},baseDao.getSortArrForSelect=function(e){let{result:t,sortArr:n}=e;for(let e in n){let a=n[e],i=void 0!==a.name?a.name:a.key,r=a.type;r=r&&"asc"!=r?"desc":"asc",t=t.orderBy(i,r)}return t},baseDao.getSortArrForAggregate=function(e){let{result:t,sortArr:n}=e,a={};for(let e in n){let t=n[e],i=void 0!==t.name?t.name:t.key,r=t.type;r=r&&"asc"!=r?-1:1,a[i]=r}return t=t.sort(a),t},baseDao.getGeoNearJson=function(e){let{runDB:t,result:n,geoNearJson:a,whereJson:i}=e,r=a.keyName,o=a.operands[0];return n=n.geoNear({near:new t.Geo.Point(o.geometry.longitude,o.geometry.latitude),spherical:!0,maxDistance:o.maxDistance,minDistance:o.minDistance,query:i,distanceMultiplier:o.distanceMultiplier,distanceField:o.distanceField||"distance",includeLocs:r,key:r}),n},baseDao.createGeoNearJson=function(e){let{whereJson:t,geoNearJson:n}=e;for(let e in t)if(t[e]&&"object"==typeof t[e])if("geoNear"===t[e].operator)n={...t[e]},n.keyName=e,delete t[e];else if(t[e].val&&t[e].val.geometry&&"Point"===t[e].val.geometry.type){let a=t[e].val;n={operator:"geoNear",operands:[{geometry:{longitude:a.geometry.longitude,latitude:a.geometry.latitude},maxDistance:a.maxDistance,minDistance:a.minDistance,distanceMultiplier:a.distanceMultiplier,distanceField:a.distanceField}],fieldName:{},keyName:e},delete t[e]}return n};var vkBaseDao=baseDao;const request=async(e={})=>{const t=uniCloud.vk;"[object object]"===Object.prototype.toString.call(e.content)&&(e.content=JSON.stringify(e.content));let n,a,i=!1;if(void 0===e.dataType&&(e.dataType="text",i=!0),"default"!=e.dataType&&"buffer"!=e.dataType&&""!==e.dataType||delete e.dataType,e.useContent&&(e.content=JSON.stringify(e.data)),e.method||(e.method="POST"),e.timeout||(e.timeout=6e4),(!e.retryCount||e.retryCount<=0)&&(e.retryCount=1),void 0===e.followRedirect&&(e.followRedirect=!0),e.method=e.method.toUpperCase(),void 0===e.headers&&void 0!==e.header&&(e.headers=e.header),e.encrypt){const a={appId:t.getConfig("vk.context").APPID};e.headers||(e.headers={}),e.headers["vk-appid"]&&(a.appId=e.headers["vk-appid"]),e.headers["vk-device-id"]&&(a.deviceId=e.headers["vk-device-id"]),e.headers["uni-id-token"]&&(a.uniIdToken=e.headers["uni-id-token"]),e.headers["content-type"]="text/plain",e.headers["vk-encrypt"]="true";let i=t.crypto.encryptCallFunction(e.data,a);e.content=i.data,n=i.decrypt,delete e.data,delete e.encrypt}for(let n=0;n<e.retryCount;n++)try{let n=t.pubfn.getUniCloudProvider();if(a=e.useProxy&&"aliyun"===n?await httpProxyForEip({url:e.url,method:e.method,headers:e.headers,data:e.data}):await uniCloud.httpclient.request(e.url,e),a)break}catch(a){if(n===e.retryCount-1)throw a;e.retryInterval&&e.retryInterval>0&&await t.pubfn.sleep(e.retryInterval)}n&&a&&a.data&&(a.data=n(a.data));try{i&&"string"==typeof a.data&&(a.data=JSON.parse(a.data))}catch(e){}return!e.needOriginalRes&&a&&a.data?a.data:a};var vk_request=request;async function httpProxyForEip(e={}){let t=e.method.toLowerCase(),n=e.data;"post"===t&&"string"!=typeof e.data&&(n=JSON.stringify(e.data));let a=await uniCloud.httpProxyForEip[t](e.url,n,e.headers);if("string"==typeof a)try{a=JSON.parse(a)}catch(e){}return{data:a.body,status:a.statusCodeValue,headers:a.headers}}const callFunction=async(e={})=>{let{name:t,url:n,data:a={},uniIdToken:i,clientInfo:r={},event:o={},uniCloud:s,encrypt:d}=e;const c=uniCloud.vk;if(o&&o.originalParam&&o.originalParam.context){let e=o.originalParam.context;i=o.uniIdToken,r={...e,appId:e.APPID,platform:e.PLATFORM,locale:e.LOCALE,clientIP:e.CLIENTIP,os:e.OS,userAgent:e.CLIENTUA,deviceId:e.DEVICEID,uniIdToken:i}}let u,l=s||uniCloud;u=l.$context&&l.$context.FUNCTION_NAME?l.$context.FUNCTION_NAME:l.$options&&l.$options.context&&l.$options.context.FUNCTION_NAME?l.$options.context.FUNCTION_NAME:"router",i||r.uniIdToken&&(i=r.uniIdToken);const p={appId:r.appId,platform:r.platform,locale:r.locale,clientIP:r.clientIP,os:r.os,userAgent:r.userAgent,deviceId:r.deviceId,uniIdToken:i};let f;if(t||(t=u),d){let e=c.crypto.encryptCallFunction(a,p);a=e.data,f=e.decrypt}let g=await l.callFunction({name:t,data:{$url:n,uniIdToken:i,data:a,vk_context:p,encrypt:d}}),m=g&&g.result;return f&&(m=f(m)),m};var vk_callFunction=callFunction;let importObject=function(e,t={}){return new Proxy(importObject,{get:function(n,a,i){return async function(n={}){return t.easy&&(n={data:n}),t.data&&("function"==typeof t.data?n.data=Object.assign({},t.data(),n.data):n.data=Object.assign({},t.data,n.data)),uniCloud.vk.callFunction({...t,...n,url:`${e}.${a}`})}}})};var vk_importObject=importObject;let util$2={formValidateItem:function(e,t,n){let a={code:0,msg:"ok"},i=getData(e,t);void 0===i&&(i=e[t]);for(let e in n){let r=n[e];if(void 0===i&&r.required){a={type:"undefined",code:-1,msg:"字段："+t+" 名称错误，请检查！",key:t,value:i};break}if(r.required&&(null==i||null==i||""===i||0==i.length)){a={type:"required",code:-1,msg:r.message,key:t,value:i};break}if(r.type&&"any"!==r.type&&void 0!==i)if("enum"===r.type&&"[object Array]"===Object.prototype.toString.call(r.enum)){if(-1===r.enum.indexOf(i)){a={type:"enum",code:-1,msg:r.message,key:t,value:i};break}}else if("integer"===r.type){if(!Number.isInteger(i)){a={type:"integer",code:-1,msg:r.message,key:t,value:i};break}}else if("float"===r.type){if(!Number.isFinite(i)){a={type:"float",code:-1,msg:r.message,key:t,value:i};break}}else if("url"===r.type){if(!/^http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w-./?%&=]*)?$/.test(i)){a={type:"url",code:-1,msg:r.message,key:t,value:i};break}}else if("hex"===r.type){if(!/^(0x)?[0-9a-fA-F]+$/.test(i)){a={type:"hex",code:-1,msg:r.message,key:t,value:i};break}}else if("email"===r.type){if(!/^[a-zA-Z0-9_.-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(i)){a={type:"email",code:-1,msg:r.message,key:t,value:i};break}}else{if(Object.prototype.toString.call(i).toLowerCase().toLowerCase()!==`[object ${r.type}]`.toLowerCase()){a={type:"type",code:-1,msg:r.message,key:t,value:i};break}}if(r.len&&i.length!=r.len){a={type:"len",code:-1,msg:r.message,key:t,value:i};break}if(r.min&&isNotNull(i))if("number"==r.type){if(i<r.min){a={type:"min",code:-1,msg:r.message,key:t,value:i};break}}else if(i.length<r.min){a={type:"min",code:-1,msg:r.message,key:t,value:i};break}if(r.max&&isNotNull(i))if("number"==r.type){if(i>r.max){a={type:"max",code:-1,msg:r.message,key:t,value:i};break}}else if(i.length>r.max){a={type:"max",code:-1,msg:r.message,key:t,value:i};break}if("function"==typeof r.validator){let e,n,o=0,s=r.validator(r,i,(function(e){return n||(n=e),e}));if(n||(n=s),void 0!==n&&!0!==n&&(o=-1,"object"==typeof n?e||(e=n.message||n.msg,n.code&&(o=n.code)):"string"==typeof n&&(e||(e=n))),o){a={type:"validator",code:o,msg:e||r.message,key:t,value:i};break}}}return a}};var formValidate=function(e={}){let t={code:0,msg:"ok"},{data:n,rules:a}=e;if(a)for(let e in a){let i=a[e];if(t=util$2.formValidateItem(n,e,i),0!=t.code)break}return t};function isNotNull(e){return-1===[void 0,null,""].indexOf(e)}function getData(e,t,n){let a=JSON.parse(JSON.stringify(e));t=t.replace(/\s+/g,"")+".";let i="";for(let e=0;e<t.length;e++){let n=t.charAt(e);"."!=n&&"["!=n&&"]"!=n?i+=n:a&&(""!=i&&(a=a[i]),i="")}return void 0===a&&void 0!==n&&(a=n),a}function deepClone(e){return JSON.parse(JSON.stringify(e))}let util$3={};function uniqueArr(e){let t=[];for(let n=0;n<e.length;n++)-1==t.indexOf(e[n])&&t.push(e[n]);return t}util$3.treeToArray=function(e,t){let n=deepClone(e);return util$3.treeToArrayFn(n,t)},util$3.treeToArrayFn=function(e,t={},n=[],a){let{id:i="_id",parent_id:r="parent_id",children:o="children",deleteChildren:s=!0}=t;for(let d in e){let c=e[d];a&&(c[r]=a),n.push(c),c[o]&&c[o].length>0&&(n=util$3.treeToArrayFn(c[o],t,n,c[i])),s&&delete c[o]}return n},util$3.arrayToTree=function(e,t){let n=deepClone(e),{id:a="_id",parent_id:i="parent_id",children:r="children",deleteParentId:o=!1,need_field:s}=t,d=[],c={};for(let e=0;e<n.length;e++)c[n[e][a]]=n[e];for(let e=0;e<n.length;e++){let t=n[e];if(s){s=uniqueArr(s.concat([a,i,r]));for(let e in t)-1===s.indexOf(e)&&delete t[e]}let u=c[t[i]];u?(u[r]||(u[r]=[]),o&&delete t[i],u[r].push(t)):d.push(t)}return d};var treeUtil=util$3;let util$4={getTargetTimezone:function(e){let t=uniCloud.vk;if("number"==typeof e)return e;let n=8;try{const{config:e}=t.getUnicloud();n=t.pubfn.getData(e.vk,"system.targetTimezone",8)}catch(e){}return n},getDateObject:function(e,t){if(!e)return"";let n;if("string"==typeof e&&!isNaN(e)&&e.length>=10&&(e=Number(e)),"number"==typeof e)10==e.toString().length&&(e*=1e3),n=new Date(e);else if("object"==typeof e)n=new Date(e.getTime());else if("string"==typeof e){let a=t=util$4.getTargetTimezone(t),i=t>=0?"+":"";t>=0&&t<10?a="0"+t:t<0&&t>-10&&(a="-0"+-1*t);let r,o=e.split(" "),s=o[0]||"",d=o[1]||"";r=s.indexOf("-")>-1?s.split("-"):s.split("/");let c=d.split(":"),u={year:Number(r[0]),month:Number(r[1])||1,day:Number(r[2])||1,hour:Number(c[0])||0,minute:Number(c[1])||0,second:Number(c[2])||0};for(let e in u)u[e]>=0&&u[e]<10&&(u[e]="0"+u[e]);let l=`${u.year}-${u.month}-${u.day}T${u.hour}:${u.minute}:${u.second}${i}${a}:00`;n=new Date(l)}return n},getTimeByTimeZone:function(e,t){let n=util$4.getDateObject(e);t=util$4.getTargetTimezone(t);let a=60*n.getTimezoneOffset()*1e3+60*t*60*1e3,i=n.getTime()+a;return n=new Date(i),n},timeFormat:function(e,t="yyyy-MM-dd hh:mm:ss",n){try{if(!e)return"";n=util$4.getTargetTimezone(n);let a=util$4.getTimeByTimeZone(e,n),i={"M+":a.getMonth()+1,"d+":a.getDate(),"h+":a.getHours(),"m+":a.getMinutes(),"s+":a.getSeconds(),"q+":Math.floor((a.getMonth()+3)/3),S:a.getMilliseconds(),Z:`${n>=0?"+":"-"}${Math.abs(n).toString().padStart(2,"0")}:00`},r=new RegExp("(y+)");if(r.test(t)){let e=t.match(r);t=t.replace(e[1],(a.getFullYear()+"").substr(4-e[1].length))}for(let e in i){let n=new RegExp("("+e+")");if(n.test(t)){let a=t.match(n);t=t.replace(a[1],1==a[1].length?i[e]:("00"+i[e]).substr((""+i[e]).length))}}return t}catch(e){return time}},getDateInfo:function(e=new Date,t){let n=util$4.getTimeByTimeZone(e,t),a=n.getFullYear()+"",i=n.getMonth()+1<10?"0"+(n.getMonth()+1):n.getMonth()+1,r=n.getDate()<10?"0"+n.getDate():n.getDate(),o=n.getHours()<10?"0"+n.getHours():n.getHours(),s=n.getMinutes()<10?"0"+n.getMinutes():n.getMinutes(),d=n.getSeconds()<10?"0"+n.getSeconds():n.getSeconds(),c=n.getMilliseconds(),u=n.getDay(),l=Math.floor((n.getMonth()+3)/3);return{year:Number(a),month:Number(i),day:Number(r),hour:Number(o),minute:Number(s),second:Number(d),millisecond:Number(c),week:Number(u),quarter:Number(l)}},getCommonTime:function(e=new Date,t){let n={},a=util$4.getDateObject(e);t=util$4.getTargetTimezone(t);const i=60*a.getTimezoneOffset()*1e3+60*t*60*1e3,{year:r,month:o,day:s,hour:d,minute:c,second:u,millisecond:l,week:p,quarter:f}=util$4.getDateInfo(a,t);n.now={year:r,month:o,day:s,hour:d,minute:c,second:u,millisecond:l,week:p,quarter:f,date_str:util$4.timeFormat(a,"yyyy-MM-dd hh:mm:ss",t),date_day_str:util$4.timeFormat(a,"yyyy-MM-dd",t),date_month_str:util$4.timeFormat(a,"yyyy-MM",t)};let g=new Date(r,o,0).getDate(),m=new Date(r,12,0).getDate();n.todayStart=new Date(`${r}/${o}/${s}`).getTime()-i,n.today12End=new Date(`${r}/${o}/${s}`).getTime()+43199999-i,n.todayEnd=new Date(`${r}/${o}/${s}`).getTime()+86399999-i,n.monthStart=new Date(`${r}/${o}/1`).getTime()-i,n.monthEnd=new Date(`${r}/${o}/${g}`).getTime()+86399999-i,n.yearStart=new Date(r+"/1/1").getTime()-i,n.yearEnd=new Date(`${r}/12/${m}`).getTime()+86399999-i,n.hourStart=new Date(`${r}/${o}/${s} ${d}:00:00`).getTime()-i,n.hourEnd=new Date(`${r}/${o}/${s} ${d}:59:59`).getTime()-i;let b=r,y=o-1;0===y&&(y=12,b=r-1);let h=new Date(b,y,0).getDate();n.lastMonthStart=new Date(`${b}/${y}/1`).getTime()-i,n.lastMonthEnd=new Date(`${b}/${y}/${h}`).getTime()+86399999-i,n.yesterdayStart=n.todayStart-864e5,n.yesterday12End=n.today12End-864e5,n.yesterdayEnd=n.todayEnd-864e5;let w=util$4.getWeekOffsetStartAndEnd(0,a,t);n.weekStart=w.startTime,n.weekEnd=w.endTime,n.months=[],n.months[0]={startTime:n.monthStart,endTime:n.monthEnd,startTimeStr:util$4.timeFormat(n.monthStart,"yyyy-MM-dd hh:mm:ss",t),endTimeStr:util$4.timeFormat(n.monthEnd,"yyyy-MM-dd hh:mm:ss",t),monthStart:n.monthStart,monthEnd:n.monthEnd};for(let e=1;e<=12;e++){let a=new Date(r,e,0).getDate(),o=new Date(`${r}/${e}/1`).getTime()-i,s=new Date(`${r}/${e}/${a}`).getTime()+86399999-i;n.months[e]={startTime:o,endTime:s,startTimeStr:util$4.timeFormat(o,"yyyy-MM-dd hh:mm:ss",t),endTimeStr:util$4.timeFormat(s,"yyyy-MM-dd hh:mm:ss",t),monthStart:o,monthEnd:s}}n.days=[],n.days[0]={startTime:n.todayStart,endTime:n.todayEnd,startTimeStr:util$4.timeFormat(n.todayStart,"yyyy-MM-dd hh:mm:ss",t),endTimeStr:util$4.timeFormat(n.todayEnd,"yyyy-MM-dd hh:mm:ss",t)};for(let e=1;e<=g;e++){let a=n.monthStart+864e5*(e-1),{startTime:i,endTime:r}=util$4.getDayOffsetStartAndEnd(0,a,t);n.days[e]={startTime:i,endTime:r,startTimeStr:util$4.timeFormat(i,"yyyy-MM-dd hh:mm:ss",t),endTimeStr:util$4.timeFormat(r,"yyyy-MM-dd hh:mm:ss",t)}}for(let e in n)"number"==typeof n[e]&&13===n[e].toString().length&&(n[e+"Str"]=util$4.timeFormat(n[e],"yyyy-MM-dd hh:mm:ss",t));return n},getMonthStartAndEnd:function(e,t){t=util$4.getTargetTimezone(t);let n={startTime:null,endTime:null},{year:a,month:i}=e;if(a>0&&i>0){const e=60*(new Date).getTimezoneOffset()*1e3+60*t*60*1e3;let r=new Date(a,i,0).getDate();n.startTime=new Date(`${a}/${i}/1`).getTime()-e,n.endTime=new Date(`${a}/${i}/${r}`).getTime()+86399999-e}return n},getHourOffsetStartAndEnd:function(e=0,t=new Date,n){let a=util$4.getDateObject(t);n=util$4.getTargetTimezone(n);let i={};const r=60*a.getTimezoneOffset()*1e3+60*n*60*1e3;a=new Date(a.getTime()+36e5*e);let o=util$4.getDateInfo(a);return i.startTime=new Date(`${o.year}/${o.month}/${o.day} ${o.hour}:00:00`).getTime()-r,i.endTime=new Date(`${o.year}/${o.month}/${o.day} ${o.hour}:00:00`).getTime()+3599999-r,i},getDayOffsetStartAndEnd:function(e=0,t=new Date,n){let a=util$4.getDateObject(t);n=util$4.getTargetTimezone(n);let i={};const r=60*a.getTimezoneOffset()*1e3+60*n*60*1e3;a=new Date(a.getTime()+864e5*e);let o=util$4.getDateInfo(a);return i.startTime=new Date(`${o.year}/${o.month}/${o.day}`).getTime()-r,i.endTime=new Date(`${o.year}/${o.month}/${o.day}`).getTime()+86399999-r,i},getWeekOffsetStartAndEnd:function(e=0,t=new Date,n){let a={},i=util$4.getDateObject(t);n=util$4.getTargetTimezone(n);const r=60*i.getTimezoneOffset()*1e3+60*n*60*1e3;let o=0===i.getDay()?7:i.getDay();i.setDate(i.getDate()-o+1+7*e);let s=util$4.getDateInfo(i);i.setDate(i.getDate()+7);let d=util$4.getDateInfo(i);return a.startTime=new Date(`${s.year}/${s.month}/${s.day}`).getTime()-r,a.endTime=new Date(`${d.year}/${d.month}/${d.day}`).getTime()-1-r,a},getMonthOffsetStartAndEnd:function(e=0,t=new Date,n){let a={},i=util$4.getDateObject(t),r=util$4.getDateInfo(i),o=r.month+e,s=r.year;o>12?(s+=Math.floor(o/12),o=Math.abs(o)%12):o<=0&&(s=s-1-Math.floor(Math.abs(o)/12),o=12-Math.abs(o)%12);let d=new Date(s,o,0).getDate();return a.startTime=util$4.getDateObject(`${s}/${o}/01`,n).getTime(),a.endTime=util$4.getDateObject(`${s}/${o}/${d}`,n).getTime()+86399999,a},getQuarterOffsetStartAndEnd:function(e=0,t=new Date,n){let a={},i=util$4.getDateObject(t);i.setMonth(i.getMonth()+3*e),n=util$4.getTargetTimezone(n);const r=60*i.getTimezoneOffset()*1e3+60*n*60*1e3;let o=util$4.getDateInfo(i).month;[1,2,3].indexOf(o)>-1?o=1:[4,5,6].indexOf(o)>-1?o=4:[7,8,9].indexOf(o)>-1?o=7:[10,11,12].indexOf(o)>-1&&(o=10),i.setMonth(o-1);let s=util$4.getDateInfo(i);i.setMonth(i.getMonth()+3);let d=util$4.getDateInfo(i);return a.startTime=new Date(`${s.year}/${s.month}/1`).getTime()-r,a.endTime=new Date(`${d.year}/${d.month}/1`).getTime()-1-r,a},getYearOffsetStartAndEnd:function(e=0,t=new Date,n){let a={},i=util$4.getDateObject(t);n=util$4.getTargetTimezone(n);const r=60*i.getTimezoneOffset()*1e3+60*n*60*1e3;let o=util$4.getDateInfo(i).year+e;return a.startTime=new Date(o+"/1/1").getTime()-r,a.endTime=new Date(o+"/12/31").getTime()+86399999-r,a},getOffsetTime:function(e=new Date,t,n){let a=util$4.getDateObject(e),i=util$4.getDateInfo(a);n=util$4.getTargetTimezone(n);const r=60*a.getTimezoneOffset()*1e3+60*n*60*1e3;let o=t.year||t.y||0,s=t.month||t.m||0,d=t.day||t.d||0,c=t.hour||t.hours||t.hh||0,u=t.minute||t.minutes||t.mm||0,l=t.second||t.seconds||t.ss||0,{mode:p="after"}=t;"before"==p&&(o*=-1,s*=-1,d*=-1,c*=-1,u*=-1,l*=-1);let f={year:i.year+o,month:i.month+s,day:i.day+d,hour:i.hour+c,minute:i.minute+u,second:i.second+l};return a=new Date(f.year,f.month-1,f.day,f.hour,f.minute,f.second),a.getTime()-r},isLeapYear:function(e){if(void 0===e){let{now:t}=util$4.getCommonTime();e=t.year}else if("object"==typeof e){let{now:t}=util$4.getCommonTime(e);e=t.year}return e%4==0&&e%100!=0||e%400==0},isQingming:function(e=new Date){let{now:t}=util$4.getCommonTime(e),{year:n,month:a,day:i}=t,r=!1;return util$4.isLeapYear(n)||util$4.isLeapYear(n-1)?4===a&&4===i&&(r=!0):4===a&&5===i&&(r=!0),r},getFullTime:function(e,t=0,n){if(!e)return"";n=util$4.getTargetTimezone(n);let a=util$4.getDateObject(e);const i=60*a.getTimezoneOffset()*1e3+60*n*60*1e3,r=a.getTime()+i;a=new Date(r);let o=a.getFullYear()+"",s=a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1,d=a.getDate()<10?"0"+a.getDate():a.getDate(),c=a.getHours()<10?"0"+a.getHours():a.getHours(),u=a.getMinutes()<10?"0"+a.getMinutes():a.getMinutes(),l=a.getSeconds()<10?"0"+a.getSeconds():a.getSeconds();return 2===t?{YYYY:Number(o),MM:Number(s),DD:Number(d),hh:Number(c),mm:Number(u),ss:Number(l),year:Number(o),month:Number(s),day:Number(d),hour:Number(c),minute:Number(u),second:Number(l)}:1===t?o+""+s+d+c+u+l:o+"-"+s+"-"+d+" "+c+":"+u+":"+l}};var timeUtil=util$4;function jsonToQueryString(e={},t="brackets"){let n=JSON.parse(JSON.stringify(e)),a=[];-1==["indices","brackets","repeat","comma"].indexOf(t)&&(t="brackets");for(let e in n){let i=n[e];if(!(["",void 0,null].indexOf(i)>=0))if(i.constructor===Array)switch(t){case"indices":for(let t=0;t<i.length;t++)a.push(e+"["+t+"]="+i[t]);break;case"brackets":i.forEach(t=>{a.push(e+"[]="+t)});break;case"repeat":i.forEach(t=>{a.push(e+"="+t)});break;case"comma":let t="";i.forEach(e=>{t+=(t?",":"")+e}),a.push(e+"="+t);break;default:i.forEach(t=>{a.push(e+"[]="+t)})}else a.push(e+"="+i)}return a.length?a.join("&"):""}function queryStringToJson(e="",t="brackets"){let n={};if(e.replace(/^\?/,"").split("&").forEach(e=>{let[a,i]=e.split("=");if(i=decodeURIComponent(i),a.match(/\[\]$/)||a.match(/\[\d+\]$/)){let e;if("brackets"===t?e=a.replace(/\[]$/,""):"indices"===t&&(e=a.replace(/\[\d+\]$/,"")),n[e]||(n[e]=[]),"indices"===t){let t=a.match(/\[(\d+)\]/)[1];n[e][t]=i}else n[e].push(i)}else n[a]||(n[a]="comma"===t?[]:i),"comma"===t?i.includes(",")?n[a]=i.split(",").map(e=>decodeURIComponent(e)):n[a]=i:"repeat"===t&&n[a]!==i&&(Array.isArray(n[a])||(n[a]=[n[a]]),n[a].push(i))}),"repeat"===t)for(let e in n)n[e].constructor===Array&&1===n[e].length&&(n[e]=n[e][0]);return n}var queryStringUtil={jsonToQueryString:jsonToQueryString,queryStringToJson:queryStringToJson};let pubfn={},util$5={};pubfn.init=function(e){util$5=e},pubfn.formValidate=formValidate,pubfn.treeUtil=treeUtil,pubfn.timeUtil=timeUtil,pubfn.jsonToQueryString=queryStringUtil.jsonToQueryString,pubfn.queryStringToJson=queryStringUtil.queryStringToJson,pubfn.sleep=e=>new Promise(t=>setTimeout(t,e)),pubfn.timeFormat=pubfn.timeUtil.timeFormat,pubfn.getDateInfo=pubfn.timeUtil.getDateInfo,pubfn.getFullTime=pubfn.timeUtil.getFullTime,pubfn.getCommonTime=pubfn.timeUtil.getCommonTime,pubfn.getOffsetTime=pubfn.timeUtil.getOffsetTime,pubfn.getHourOffsetStartAndEnd=pubfn.timeUtil.getHourOffsetStartAndEnd,pubfn.getDayOffsetStartAndEnd=pubfn.timeUtil.getDayOffsetStartAndEnd,pubfn.getWeekOffsetStartAndEnd=pubfn.timeUtil.getWeekOffsetStartAndEnd,pubfn.getWeekStartAndEnd=pubfn.timeUtil.getWeekOffsetStartAndEnd,pubfn.getMonthOffsetStartAndEnd=pubfn.timeUtil.getMonthOffsetStartAndEnd,pubfn.getQuarterOffsetStartAndEnd=pubfn.timeUtil.getQuarterOffsetStartAndEnd,pubfn.getYearOffsetStartAndEnd=pubfn.timeUtil.getYearOffsetStartAndEnd,pubfn.getMonthStartAndEnd=pubfn.timeUtil.getMonthStartAndEnd,pubfn.validator=function(e){return function(t,n,a){let i=pubfn.test(n,e);return"function"!=typeof a||!i&&n?a(!1):void a()}},pubfn.test=function(e,t="",n=!1){let a;if(t=t.toLowerCase(),n&&(""===e||null==e))return!0;switch(t){case"mobile":return new RegExp(/^1[3|4|5|6|7|8|9][0-9]{9}$/).test(e);case"tel":return new RegExp(/^(0\d{2,3}-\d{7,8})(-\d{1,4})?$/).test(e);case"card":return new RegExp(/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/).test(e);case"mobilecode":return new RegExp(/^[0-9]{6}$/).test(e);case"username":return new RegExp(/^[a-zA-Z]([-_a-zA-Z0-9]{2,31})$/).test(e);case"pwd":case"password":return new RegExp(/^([a-zA-Z0-9_@]){6,18}$/).test(e);case"paypwd":return new RegExp(/^[0-9]{6}$/).test(e);case"postal":return new RegExp(/[1-9]\d{5}(?!\d)/).test(e);case"qq":return new RegExp(/^[1-9][0-9]{4,9}$/).test(e);case"email":return new RegExp(/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/).test(e);case"money":return new RegExp(/^\d*(?:\.\d{0,2})?$/).test(e);case"url":return new RegExp(/^(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/).test(e);case"domain":return new RegExp(/^(?!:\/\/)(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z]{2,}$/).test(e);case"ip":return new RegExp(/^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/).test(e);case"date":return new RegExp(/^[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/).test(e);case"time":return new RegExp(/^(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d$/).test(e);case"datetime":return new RegExp(/^[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d$/).test(e);case"english+number":return new RegExp(/^[a-zA-Z0-9]+$/).test(e);case"english+number+_":return new RegExp(/^[a-zA-Z0-9_]+$/).test(e);case"english+number+_-":return new RegExp(/^[a-zA-Z0-9_-]+$/).test(e);case"version":return new RegExp(/^([1-9]\d|[1-9])(.([1-9]\d|\d)){2}$/).test(e);case"number":return new RegExp(/^[0-9]+$/).test(e);case"english":return new RegExp(/^[a-zA-Z]+$/).test(e);case"chinese":return new RegExp(/^[\u4e00-\u9fa5]+$/gi).test(e);case"lower":return new RegExp(/^[a-z]+$/).test(e);case"upper":return new RegExp(/^[A-Z]+$/).test(e);case"html":return new RegExp(/<("[^"]*"|'[^']*'|[^'">])*>/).test(e);case"image":return a=e.split("?")[0].toLowerCase(),new RegExp(/\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)$/).test(a);case"video":return a=e.split("?")[0].toLowerCase(),new RegExp(/\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8|3gp)$/).test(a);case"audio":return a=e.split("?")[0].toLowerCase(),new RegExp(/\.(mp3)$/).test(a);default:return!0}},pubfn.checkStr=pubfn.test,pubfn.priceFilter=function(e,t=""){return pubfn.isNull(e)?t:isNaN(e)?e:("string"==typeof e&&(e=parseFloat(e)),(e/100).toFixed(2))},pubfn.priceLeftFilter=function(e){let t="";return e&&(t=pubfn.priceFilter(e).split(".")[0]),t},pubfn.priceRightFilter=function(e){let t="";return e&&(t=pubfn.priceFilter(e).split(".")[1]),t},pubfn.percentageFilter=function(e,t=!0,n=""){return pubfn.isNull(e)?n:(isNaN(e)||("string"==typeof e&&(e=parseFloat(e)),e=parseFloat((100*e).toFixed(2)),t&&(e+="%")),e)},pubfn.discountFilter=function(e,t=!0,n=""){return pubfn.isNull(e)?n:isNaN(e)?e:("string"==typeof e&&(e=parseFloat(e)),(e=parseFloat((10*e).toFixed(2)))>10?"折扣不可以大于原价":10==e?"原价":0==e?"免费":e<0?"折扣不可以小于0":(t&&(e+=" 折"),e))},pubfn.objectDeleteInvalid=function(e){return Object.keys(e).forEach(t=>{void 0===e[t]&&delete e[t]}),e},pubfn.objectAssign=function(e,t,n=!0){return n&&pubfn.objectDeleteInvalid(t),Object.assign(e,t)},pubfn.copyObject=function(e){return void 0!==e?JSON.parse(JSON.stringify(e)):e},pubfn.deepClone=function(e){if([null,void 0,NaN,!1].includes(e))return e;if("object"!=typeof e&&"function"!=typeof e)return e;let t="[object Array]"===Object.prototype.toString.call(e)?[]:{};for(let n in e)e.hasOwnProperty(n)&&(t[n]="object"==typeof e[n]?pubfn.deepClone(e[n]):e[n]);return t},pubfn.formAssign=function(e,t){let n=pubfn.copyObject(e);return pubfn.objectAssign(n,t)},pubfn.arr_concat=function(e,t,n){n||(n="id"),e||(e=[]),t||(t=[]);let a=e.concat(t),i=[];if(-1!=n){let e=[];for(let t in a)-1==e.indexOf(a[t][n])&&(e.push(a[t][n]),i.push(a[t]))}else i=a;return i},pubfn.getData=function(e,t,n){let a=e;if(pubfn.isNotNull(a)||"object"==typeof a){let e="",n=".",i="[",r="]";t=t.replace(/\s+/g,e)+n;let o=e;for(let s=0;s<t.length;s++){let d=t.charAt(s);d!=n&&d!=i&&d!=r?o+=d:a&&(o!=e&&(a=a[o]),o=e)}}return void 0===a&&void 0!==n&&(a=n),a},pubfn.setData=function(e,t,n){let a=new RegExp("([\\w$-]+)|\\[(:\\d)\\]","g");const i=t.match(a);for(let t=0;t<i.length-1;t++){let n=i[t];"object"!=typeof e[n]&&(e[n]={}),e=e[n]}e[i[i.length-1]]=n},pubfn.isNull=function(e){let t=!1;return(null==e||""===e||"[object Object]"===Object.prototype.toString.call(e)&&0===Object.keys(e).length||"[object Array]"===Object.prototype.toString.call(e)&&0===e.length)&&(t=!0),t},pubfn.isNotNull=function(e){return!pubfn.isNull(e)},pubfn.isNullOne=function(...e){let t=!1;for(let n=0;n<e.length;n++){let a=e[n];if(pubfn.isNull(a)){t=!0;break}}return t},pubfn.isNullOneByObject=function(e){let t;for(let n in e){let a=e[n];if(pubfn.isNull(a)){t=n;break}}return t},pubfn.isNullAll=function(...e){let t=!0;for(let n=0;n<e.length;n++){let a=e[n];if(pubfn.isNotNull(a)){t=!1;break}}return t},pubfn.isNotNullAll=function(...e){return!pubfn.isNullOne(...e)},pubfn.getListItem=function(e,t,n){let a;for(let i=0;i<e.length;i++)if(e[i][t]===n){a=e[i];break}return a},pubfn.getListIndex=function(e,t,n){let a=-1;for(let i=0;i<e.length;i++)if(e[i][t]===n){a=i;break}return a},pubfn.getListItemIndex=function(e,t,n){let a,i={},r=-1;for(let i=0;i<e.length;i++)if(e[i][t]===n){r=i,a=e[i];break}return i={item:a,index:r},i},pubfn.arrayToJson=function(e,t){let n={};for(let a in e){let i=e[a];n[i[t]]=i}return n},pubfn.listToJson=pubfn.arrayToJson,pubfn.arrayObjectGetArray=function(e,t){return e.map(e=>e[t])},pubfn.enhancedRandom=function(){if(void 0!==commonjsGlobal.performance){return(commonjsGlobal.performance.now()+Math.random())%1}return Math.random()},pubfn.random=function(e,t,n){let a;if(pubfn.isNull(n))a=pubfn.randomFn(e,t);else{let i=0,r=1e5;do{i++,a=pubfn.randomFn(e,t)}while(n.indexOf(a)>-1&&i<r);i===r&&(a=void 0)}return a},pubfn.randomFn=function(e,t){let n="",a="123456789";pubfn.isNotNull(t)&&("a-z,0-9"==t?t="abcdefghijklmnopqrstuvwxyz0123456789":"A-Z,0-9"==t?t="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789":"a-z,A-Z,0-9"!=t&&"A-Z,a-z,0-9"!=t||(t="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"),a=t);for(let t=0;t<e;t++){n+=a[Math.floor(pubfn.enhancedRandom()*a.length)]}return n},pubfn.stringIdToNumberId=function(e,t){let n="",a=e.split("").reverse().join("");for(let e=0;e<t;e++)if(a.length>e){n+="0123456789"[a[e].charCodeAt()%10]}else n="0"+n;return n},pubfn.hidden=function(e="",t=0,n=0){let a=e.length-t-n,i="";for(let e=0;e<a;e++)i+="*";return e.substring(0,t)+i+e.substring(e.length-n)},pubfn.checkArrayIntersection=function(e=[],t=[]){let n=!1;for(let a=0;a<t.length;a++)e.indexOf(t[a])>-1&&(n=!0);return n},pubfn.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)},pubfn.isObject=function(e){return"[object Object]"===Object.prototype.toString.call(e)},pubfn.calcFreights=function(e,t){return pubfn.calcFreightDetail(e,t).total_amount},pubfn.calcFreightDetail=function(e,t){let{first_weight:n,first_weight_price:a,continuous_weight:i,continuous_weight_price:r,max_weight:o}=e;o||(o=1e9);let s=t,d=0,c=o,u=!1,l=0;for(;t>0;)u?(l++,t-=i,c-=i):(u=!0,d++,c=o,t-=n,c-=n),c<=0&&(u=!1);let p=a*d+r*l;return{weight:s,first_weight:n,first_weight_price:a,first_weight_count:d,continuous_weight:i,continuous_weight_price:r,continuous_weight_count:l,first_weight_amount:d*a,continuous_weight_amount:r*l,total_amount:p,formula:`${a} * ${d} + ${r} * ${l} = ${p}`}},pubfn.getNewObject=function(e,t){let n=e,a={};if(t&&t.length>0)for(let e in t){let i=t[e];pubfn.isNotNull(n[i])&&(a[i]=n[i])}else a=n;return a},pubfn.deleteObjectKeys=function(e,t=[]){let n={};if(e)for(let a in e)-1==t.indexOf(a)&&(n[a]=e[a]);return n},pubfn.arrayToTree=pubfn.treeUtil.arrayToTree,pubfn.treeToArray=pubfn.treeUtil.treeToArray,pubfn.wildcardTestOne=function(e,t){if(!t)return!1;let n=t.replace(new RegExp("\\.","g"),"(\\.)").replace(new RegExp("\\*","g"),"(.*)"),a=0!==t.indexOf("*")?"^":"",i="*"!==t[t.length-1]?"$":"";return new RegExp(a+n+i).test(e)},pubfn.wildcardTest=function(e,t){let n=0;if("string"==typeof t)pubfn.wildcardTestOne(e,t)&&n++;else if("object"==typeof t)for(let a=0;a<t.length;a++){let i=t[a];pubfn.wildcardTestOne(e,i)&&n++}return n},pubfn.regExpTestOne=function(e,t){if(!t)return!1;return new RegExp(t).test(e)},pubfn.regExpTest=function(e,t){let n=0;if("string"==typeof t)pubfn.regExpTestOne(e,t)&&n++;else if("object"==typeof t)for(let a=0;a<t.length;a++){let i=t[a];pubfn.regExpTestOne(e,i)&&n++}return n},pubfn.regExpExecToTemplate=function(e,t,n){let a=new RegExp(t).exec(e);if(a){for(let e=1;e<a.length;e++)n=n.replace("$"+e,a[e]);return n}},pubfn.createOrderNo=function(e="",t=25){let n=pubfn.timeFormat(new Date,"yyyyMMddhhmmss");n=n.substring(2);let a=t-(e+n).length;return e+n+pubfn.random(a)},pubfn.dateDiff=function(e,t="前"){if(!e)return"";"string"!=typeof e||isNaN(e)||(e=Number(e)),"number"==typeof e&&(10==e.toString().length&&(e*=1e3),e=new Date(e),e=pubfn.timeFormat(e)),"string"==typeof e&&(e=e=e.replace("T"," "),e=new Date(e.replace(/-/g,"/")));let n=864e5,a=36e5,i=(new Date).getTime()-e.getTime(),r=Math.floor(i/n),o=Math.floor(i%n/a),s=Math.floor(i%n%a/6e4),d=Math.round(i%n%a%6e4/1e3),c="1 秒";return r>0?c=r+"天":o>0?c=o+"小时":s>0?c=s+"分钟":d>0&&(c=d+"秒"),c+=t,c},pubfn.dateDiff2=function(e,t="1秒"){if(!e)return"";"string"!=typeof e||isNaN(e)||(e=Number(e)),"number"==typeof e&&(10==e.toString().length&&(e*=1e3),e=new Date(e),e=pubfn.timeFormat(e)),"string"==typeof e&&(e=e=e.replace("T"," "),e=new Date(e.replace(/-/g,"/")));let n=new Date,a=864e5,i=36e5,r=e.getTime()-n.getTime(),o=Math.floor(r/a),s=Math.floor(r%a/i),d=Math.floor(r%a%i/6e4),c=Math.round(r%a%i%6e4/1e3),u=t;return o>0?u=o+"天":s>0?u=s+"小时":d>0?u=d+"分钟":c>0&&(u=c+"秒"),u},pubfn.numStr=function(e,t=!1){"string"==typeof e&&(e=parseFloat(e));let n=e;if(e<1e3)n=e;else if(e<1e4){n=Math.floor(e/100)/10+"千"}else if(e<1e6){n=Math.floor(e/1e3)/10+"万"}else if(e<1e7){n=Math.floor(e/1e6)+"百万"}else if(e<1e8){n=Math.floor(e/1e7)+"千万"}else if(e>=1e8){n=Math.floor(e/1e7)/10+"亿"}return t&&e>=1e3&&e%10>0&&(n+="+"),n},pubfn.calcSize=function(e=0,t,n,a=2,i="auto"){let r=0,o="";if((e=parseFloat(e))<n||t.length<=1)o=t[0],r=parseFloat(e.toFixed(a));else for(let s=1;s<t.length;s++){let d=t[s];if(e/=n,"auto"===i){if(e<n){o=d,r=parseFloat(e.toFixed(a));break}}else if(i===d){o=d,r=parseFloat(e.toFixed(a));break}}return{size:r,type:o,title:r+" "+o}};const isSnakeCase=new RegExp("_(\\w)","g"),isCamelCase=new RegExp("[A-Z]","g");function parseObjectKeys(e,t){let n,a;switch(t){case"snake2camel":a=pubfn.snake2camel,n=isSnakeCase;break;case"camel2snake":a=pubfn.camel2snake,n=isCamelCase}for(const i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&n.test(i)){const n=a(i);e[n]=e[i],delete e[i],"[object Object]"===Object.prototype.toString.call(e[n])?e[n]=parseObjectKeys(e[n],t):Array.isArray(e[n])&&(e[n]=e[n].map(e=>parseObjectKeys(e,t)))}return e}pubfn.snake2camel=function(e){return e.replace(isSnakeCase,(e,t)=>t?t.toUpperCase():"")},pubfn.camel2snake=function(e){return e.replace(isCamelCase,e=>"_"+e.toLowerCase())},pubfn.snake2camelJson=function(e){return parseObjectKeys(e,"snake2camel")},pubfn.camel2snakeJson=function(e){return parseObjectKeys(e,"camel2snake")},pubfn.string2Number=function(e,t={}){switch(Object.prototype.toString.call(e).slice(8,-1).toLowerCase()){case"string":if(e&&!isNaN(e)){let{mobile:n=!0,idCard:a=!0,startFrom0:i=!0,maxLength:r=14}=t;return e.length>r||(n&&pubfn.test(e,"mobile")||a&&pubfn.test(e,"card")||i&&e.length>1&&0===e.indexOf("0")&&1!==e.indexOf("."))?e:Number(e)}return e;case"object":const n=Object.keys(e);for(let t=0;t<n.length;t++){const a=n[t];e[a]=pubfn.string2Number(e[a])}return e;case"array":for(let t=0;t<e.length;t++)e[t]=pubfn.string2Number(e[t]);return e;default:return e}},pubfn.toDecimal=function(e,t=0){if("string"==typeof e&&(e=Number(e)),t<=6&&e<1e8){let n=Math.pow(10,t);return Math.round(e*n)/n}return parseFloat(e.toFixed(t))},pubfn.getFileType=function(e){let t;return t=pubfn.checkFileSuffix(e,["png","jpg","jpeg","gif","bmp","svg"])?"image":pubfn.checkFileSuffix(e,["avi","mp4","3gp","mov","rmvb","rm","flv","mkv"])?"video":pubfn.checkFileSuffix(e,["mp3"])?"audio":"other",t},pubfn.getFileSuffix=function(e){let t,n=e.lastIndexOf(".");return n>-1&&(t=e.substring(n+1)),t},pubfn.checkFileSuffix=function(e,t){let n=!1,a=pubfn.getFileSuffix(e);for(let e=0;e<t.length;e++)if(t.indexOf(a)>-1){n=!0;break}return n},pubfn.splitArray=function(e,t){let n=[];for(let a=0;a<e.length;a+=t)n.push(e.slice(a,a+t));return n},pubfn.objectKeySort=function(e){let t=Object.keys(e).sort(),n={};for(let a in t)n[t[a]]=e[t[a]];return n},pubfn.urlStringToJson=function(e){let t={};if(""!=e&&null!=e&&null!=e){let n=e.split("&");for(let e=0;e<n.length;e++){let a=n[e].split("="),i=a[0],r=a[1];t[i]=r}}return t},pubfn.jsonToUrlString=function(e){let t="";for(let n in e)if(e.hasOwnProperty(n)){""!==t&&(t+="&"),t+=n+"="+e[n]}return t},pubfn.getQueryStringParameters=function(e){let t={};if(e.httpMethod){if(e.body){let n=e.body;e.isBase64Encoded&&(n=Buffer.from(n,"base64").toString("utf-8")),"string"==typeof n&&(n=JSON.parse(n)),t=n}else if(e.queryStringParameters){let n=e.queryStringParameters;"string"==typeof n.data&&(n.data=JSON.parse(n.data)),t=n}}else t=JSON.parse(JSON.stringify(e));return t.data||(t.data={}),t.url=t.$url||"",t},pubfn.stringToFormData=function(e){e||(e="");let t=e.split("Content-Disposition: form-data;"),n={};for(let e=0;e<t.length;e++){let a=t[e],i='name="',r=a.indexOf(i)+i.length;if(r>-1){let e=a.indexOf('"',r),t=a.substring(r,e);if(e>r){let i=a.indexOf("---",e),r=a.substring(e+1,i).trim();n[t]=r}}}return n},pubfn.getPlatform=function(e){return"web"===e?e="h5":"app"===e&&(e="app-plus"),e},pubfn.getClientUAByContext=function(e={}){return e.ua||e.CLIENTUA},pubfn.getPlatformForUniId=function(e={}){let t=e.PLATFORM;if(t=pubfn.getPlatform(t),"h5"===t){pubfn.getClientUAByContext(e).toLowerCase().indexOf("micromessenger")>-1&&(t="h5-weixin")}return t},pubfn.getUniCloudContext=function(){let e;try{e=uniCloud.context}catch(e){}return e},pubfn.getUniCloudFunctionName=function(){let e;try{let t=pubfn.getUniCloudContext();e=t.FUNCTION_NAME||t.function_name}catch(e){}return e},pubfn.getUniCloudProvider=function(){let e;try{e=uniCloud.getCloudInfos()[0].provider}catch(e){}return e},pubfn.getUniCloudRequestId=function(){let e;try{e=pubfn.getUniCloudContext().requestId}catch(e){}return e},pubfn.getUniIdConfig=function(e={},t,n){const a=e.uni;let i;if(pubfn.isArray(a)){let e;if(uniCloud.env&&uniCloud.env.APPID)e=uniCloud.env.APPID;else if(uniCloud.$context&&uniCloud.$context.APPID)e=uniCloud.$context.APPID;else{let t=uniCloud.getClientInfos()[0];t.appId&&(e=t.appId)}i=pubfn.getListItem(a,"dcloudAppid",e),pubfn.isNull(i)&&(i=pubfn.getListItem(a,"isDefaultConfig",!0))}else i=a;if(pubfn.isNotNull(t)){let e=pubfn.getData(i,t);return pubfn.isNull(e)?n:e}return i},pubfn.batchRun=async function(e){let{main:t,data:n=[],concurrency:a=50}=e,i=pubfn.isArray(t)?1:0,r=1===i?t.length:n.length;if(0===r)return{stack:[],total:r};a=Math.min(a,r);let o=0,s=new Array(r),d=[];const c=async()=>{let e,a=o;if(a!==r){o++;try{e=0===i?await t(n[a],a):await t[a]()}catch(t){e={code:t.code||-1,msg:t.message}}s[a]=e,await c()}};for(let e=0;e<a;e++){let e=c();d.push(e)}return await Promise.all(d),{stack:s,total:r,concurrency:a}},pubfn.randomAsync=async function(e,t,n,a=500){let i;if("function"==typeof n){let r=0;for(;r<a;){if(r++,i=pubfn.randomFn(e,t),await n(i))break}r===a&&(i=void 0)}else i=pubfn.randomFn(e,t);return i},pubfn.checkModule=function(e){let{vk:t}=util$5,n=t.require("package.json");return!(!n.extensions||!n.extensions[e])||!(!n.dependencies||!n.dependencies[e])},pubfn.getMaxMemorySize=function(e){let{vk:t}=util$5,n=t.require("package.json"),{memorySize:a=512}=n["cloudfunction-config"]||{};return a},pubfn.getMemoryUsage=function(e){let{vk:t}=util$5,n=t.pubfn.getMaxMemorySize(),{rss:a}=process.memoryUsage(),i=pubfn.toDecimal(a/1024/1024,0);return{rate:i/n,maxSize:n,usedSize:i}};var _function=pubfn;let util$6={},dataCache={};util$6.get=function(e){let t,n=dataCache[e];if(n){let{value:a,expired:i}=n;util$6.isExpired(e)?delete dataCache[e]:t=a}return t},util$6.set=function(e,t,n=0){let a={value:t,expired:n>0?(new Date).getTime()+1e3*n:0};dataCache[e]=a},util$6.del=function(e){delete dataCache[e]},util$6.clear=function(e){if(e)for(let t in dataCache)0==t.indexOf(e)&&delete dataCache[t];else dataCache={}},util$6.isExpired=function(e){let t=!0,n=dataCache[e];return n&&(0==n.expired||n.expired>(new Date).getTime())&&(t=!1),t},util$6.getAll=function(e){let t={};if(e)for(let n in dataCache)0==n.indexOf(e)&&(t[e]=dataCache[e]);else t=dataCache;for(let e in t)util$6.isExpired(e)&&(delete t[e],delete dataCache[e]);return t};var temporaryCache=util$6;let globalDataCache={},util$7={},globalDataDao={};globalDataCache.init=function(e){util$7=e,globalDataDao=util$7.vk.system.globalDataDao},globalDataCache.get=async function(e,t=0,n,a=!0){return"function"==typeof n?globalDataCache.autoGet(e,t,n,a):globalDataCache._get(e)},globalDataCache.autoGet=async function(e,t=0,n,a=!0){let i,{vk:r}=util$7;try{i=await globalDataCache._get(e),r.pubfn.isNull(i)&&"function"==typeof n&&(i=await n(),void 0!==i&&a&&await r.globalDataCache.set(e,i,t))}catch(e){return}return i},globalDataCache._get=async function(e){let t;try{let n=await globalDataDao.find(e);if(n){let{value:a,expired_at:i}=n;globalDataCache.isExpired(n)?await globalDataCache.del(e):t=a}}catch(e){return}return t},globalDataCache.set=async function(e,t,n=0,a){let i;e&&void 0!==e.key&&void 0!==e.value?(i=e.key,t=e.value,n=e.second,a=e.comment):i=e;let r={code:0,msg:"ok"};try{if(!i)return{code:-1,msg:"key值不能为空"};let e=n>0?Date.now()+1e3*n:0;r=await globalDataDao.set({key:i,value:t,expired_at:e,comment:a})}catch(e){return console.error(e),{code:-1,msg:"异常"}}return r},globalDataCache.del=async function(e){return await globalDataDao.del(e)},globalDataCache.clear=async function(e){if(e)return await globalDataDao.deleteByWhere({key:new RegExp("^"+e)})},globalDataCache.list=async function(e){return await globalDataDao.list(e)},globalDataCache.count=async function(e){return await globalDataDao.count(e)},globalDataCache.isExpired=function(e){let t=!0;return e&&(!e.expired_at||0==e.expired_at||e.expired_at>Date.now())&&(t=!1),t},globalDataCache.inc=async function(e,t=1,n=0){let a;e&&void 0!==e.key&&void 0!==e.value?(a=e.key,t=e.value,n=e.second):a=e;let i={code:0,msg:"ok"};try{if(!a)return{code:-1,msg:"key值不能为空"};let e=n>0?Date.now()+1e3*n:0;i=await globalDataDao.inc({key:a,value:t,expired_at:e})}catch(e){return{code:-1,msg:"异常",err:e}}return i},globalDataCache.uniqueAdd=async function(e,t=1,n=5){let{vk:a}=util$7,i={code:0,msg:"ok"};try{if(!e)return{code:-1,msg:"key值不能为空"};let r=n>0?Date.now()+1e3*n:0;await a.globalDataCache.deleteExpired(e);i.id=await globalDataDao.add({key:e,value:t,expired_at:r})}catch(e){return{code:-1,msg:"already exists"}}return i},globalDataCache.deleteExpired=async function(e){return await globalDataDao.deleteExpired(e)},globalDataCache.updateById=async function(e,t){return await globalDataDao.updateById(e,t)},globalDataCache.find=async function(e){return await globalDataDao.find(e)},globalDataCache.delByValue=async function(e,t){return await globalDataDao.delByValue(e,t)};var globalDataCache_1=globalDataCache;let util$8={aes:{}};const aesKey="5fb2cd73c7b53918728417c50762e6d45fb2cd73c7b53918728417c50762e6d4",AESMODE={AES192:"aes192",AES256ECB:"aes-256-ecb",AES256CBC:"aes-256-cbc"};util$8.aes.MODE=AESMODE;const errRes={code:411,msg:"解密失败"};util$8.aes.encrypt=function(e){let{data:t,key:n,iv:a,mode:i=AESMODE.AES192}=e,r=uniCloud.vk,{crypto:o,config:s}=r.getUnicloud();if(n||(n=r.pubfn.getData(s.vk,"crypto.aes",aesKey)),"object"==typeof t&&(t=JSON.stringify(t)),i===AESMODE.AES256ECB)return encryptUseAes256Ecb(t,n);if(i===AESMODE.AES192)return encryptUseAes192(t,n);if(i===AESMODE.AES256CBC)return encryptUseAes256Cbc(t,n,a);throw new Error(`msg:不支持 ${i} 加密算法`)},util$8.aes.decrypt=function(e){let t,{data:n,key:a,iv:i,mode:r=AESMODE.AES192}=e,o=uniCloud.vk,{crypto:s,config:d}=o.getUnicloud();if(o.pubfn.isNull(n))throw new Error("msg:待解密原文不能为空");if(a||(a=o.pubfn.getData(d.vk,"crypto.aes",aesKey)),r===AESMODE.AES256ECB)t=decryptUseAes256Ecb(n,a);else if(r===AESMODE.AES192)t=decryptUseAes192(n,a);else{if(r!==AESMODE.AES256CBC)throw new Error(`msg:不支持 ${r} 加密算法`);t=decryptUseAes256Cbc(n,a,i)}let c=null;try{c="string"==typeof t?JSON.parse(t):t}catch(e){c=t}return c},util$8.md5=function(e){let t=uniCloud.vk,{crypto:n}=t.getUnicloud(),a="string"!=typeof e?JSON.stringify(e):e;return n.createHash("md5").update(a).digest("hex")},util$8.encryptCallFunction=(e,t={})=>{try{const n=uniCloud.vk,a=Date.now(),i="aes-256-ecb";if(!t.appId){const e=uniCloud.getClientInfos()[0];t.appId=e.appId,t.appId||(t.appId=n.getConfig("vk.context.APPID"))}const r=util$8.md5(t.appId+t.deviceId+t.uniIdToken||"undefined");e=util$8.aes.encrypt({mode:i,data:{...e,timeStamp:a},key:r});return{decrypt:e=>{if(!e||!e.encrypt)return e;try{const t=e.data.split(","),n=t[0],a=util$8.md5(r+t[1]+t[2]);return util$8.aes.decrypt({mode:i,data:n,key:a})}catch(e){return{code:412,msg:"解密失败，响应体不合法",err:e}}},data:e}}catch(e){return console.error("加密失败: ",e),{}}};var crypto=util$8;function md5$1(e){let t=uniCloud.vk,{crypto:n}=t.getUnicloud();return n.createHash("md5").update(e).digest("hex")}function encryptUseAes192(e,t){let n=uniCloud.vk,{crypto:a}=n.getUnicloud();const i=(t=md5$1(t).substring(0,24)).substring(0,16),r=a.createCipheriv(AESMODE.AES192,t,i);let o=r.update(e,"utf8","base64");return o+=r.final("base64"),o}function decryptUseAes192(e,t){let n,a=uniCloud.vk,{crypto:i}=a.getUnicloud();try{const a=(t=md5$1(t).substring(0,24)).substring(0,16),r=i.createDecipheriv(AESMODE.AES192,t,a);n=r.update(e,"base64","utf8"),n+=r.final("utf8")}catch(e){throw errRes}return n}function encryptUseAes256Ecb(e,t){let n=uniCloud.vk,{crypto:a}=n.getUnicloud(),i=Buffer.from(e),r=t.length>32?t.substring(0,32):t.padEnd(32,"0");r=Buffer.from(r);const o=a.createCipheriv(AESMODE.AES256ECB,r,"");o.setAutoPadding(!1);const s=16-i.length%16,d=Buffer.alloc(s,s);i=Buffer.concat([i,d]);let c=o.update(i,null,"base64");return c+=o.final("base64"),c}function decryptUseAes256Ecb(e,t){let n=uniCloud.vk,{crypto:a}=n.getUnicloud(),i=t.length>32?t.substring(0,32):t.padEnd(32,"0");i=Buffer.from(i);try{const t=a.createDecipheriv(AESMODE.AES256ECB,i,"");t.setAutoPadding(!1);let n=t.update(e,"base64");n+=t.final();const r=n.charCodeAt(n.length-1);return n=n.slice(0,n.length-r),n}catch(e){throw errRes}}function encryptUseAes256Cbc(e,t,n){let a=uniCloud.vk,{crypto:i}=a.getUnicloud();const r=Buffer.from(e,"utf8");let o=t.length>32?t.substring(0,32):t.padEnd(32,"0");const s=Buffer.from(o,"utf8");let d=n.length>16?n.substring(0,16):n.padEnd(16,"0");const c=Buffer.from(d,"utf8"),u=i.createCipheriv("aes-256-cbc",s,c);let l=u.update(r);return l=Buffer.concat([l,u.final()]),l.toString("base64")}function decryptUseAes256Cbc(e,t,n){let a=uniCloud.vk,{crypto:i}=a.getUnicloud();try{const a=Buffer.from(e,"base64");let r=t.length>32?t.substring(0,32):t.padEnd(32,"0");const o=Buffer.from(r,"utf8");let s=n.length>16?n.substring(0,16):n.padEnd(16,"0");const d=Buffer.from(s,"utf8"),c=i.createDecipheriv("aes-256-cbc",o,d);let u=c.update(a);return u=Buffer.concat([u,c.final()]),u.toString("utf8")}catch(e){throw errRes}}let alipay={},ALIPAY_ALGORITHM_MAPPING={RSA:"RSA-SHA1",RSA2:"RSA-SHA256"};alipay.sign=function(e,t,n){let a=JSON.parse(JSON.stringify(t)),i=Object.assign({method:e,app_id:n.appid,charset:n.charset||"utf-8",version:n.version||"1.0",sign_type:n.signType||"RSA2"},a);n.appCertSn&&n.alipayRootCertSn&&(i=Object.assign({app_cert_sn:n.appCertSn,alipay_root_cert_sn:n.alipayRootCertSn},i));const r=a.bizContent||a.biz_content;r&&(i.biz_content=JSON.stringify(r));const o=alipay.sortObj(i);let s=alipay.objectToUrl(o);const d="PKCS8"===(n.keyType||"PKCS8")?"PRIVATE KEY":"RSA PRIVATE KEY";let c=alipay.formatKey(n.privateKey,d);const u=crypto$1.createSign(ALIPAY_ALGORITHM_MAPPING[i.sign_type]).update(s,"utf8").sign(c,"base64");return Object.assign(o,{sign:u})},alipay.formatUrl=function(e,t="https://openapi.alipay.com/gateway.do"){let n=t;const a=["app_id","method","format","charset","sign_type","sign","timestamp","version","notify_url","return_url","auth_token","app_auth_token","ws_service_url"];for(const t in e)if(a.indexOf(t)>-1){const a=encodeURIComponent(e[t]);n=`${n}${n.includes("?")?"&":"?"}${t}=${a}`,delete e[t]}return{execParams:e,url:n}},alipay.sortObj=function(e){new Array;let t=Object.keys(e).sort(),n={};for(let a in t)n[t[a]]=e[t[a]];return n},alipay.objectToUrl=function(e){let t="";for(let n in e)e[n]&&(t+=`&${n}=${e[n]}`);return t&&(t=t.substring(1)),t},alipay.formatKey=function(e,t){return`-----BEGIN ${t}-----\n${e}\n-----END ${t}-----`},alipay.getRequestRes=function(e,t){return e[t.replace(new RegExp("\\.","g"),"_")+"_response"]||e.error_response};var util$9=alipay;let alipay$1={},util$a={};const serviceUrl="https://openapi.alipay.com/gateway.do",configKey="mp-alipay.oauth.alipay";function returnRes(e){return e.code=e.sub_code||e.code||0,e.msg=e.sub_msg||e.msg||"",e}alipay$1.init=function(e){util$a=e},alipay$1.getConfig=function(){let{vk:e,config:t}=util$a;return e.pubfn.getUniIdConfig(t)},alipay$1.request=async function(e={}){let{vk:t}=util$a,{method:n,data:a={},successText:i}=e,r=alipay$1.auth.getAppidInfo(e),o=Date.now(),s={timestamp:t.pubfn.timeFormat(o,"yyyy-MM-dd hh:mm:ss"),...a};const d=util$9.sign(n,s,r),{url:c,execParams:u}=util$9.formatUrl(d,serviceUrl);let l=await t.request({url:c,method:"POST",data:u}),p=await util$9.getRequestRes(l,n);return p.code&&"10000"!==p.code||(p.code=0,p.msg=i),p=returnRes(p),p},alipay$1.auth={},alipay$1.auth.getAppidInfo=function(e={}){let{appid:t,privateKey:n}=e,{vk:a,config:i}=util$a;const r=alipay$1.getConfig();let o=a.pubfn.getData(r,configKey)||{};if(t&&t!==o.appid){if(!n){let e=a.pubfn.getData(i,"vk.oauth.alipay.list")||[],r=a.pubfn.getListItem(e,"appid",t)||{};if(r.appid&&(t=r.appid,n=r.privateKey),a.pubfn.isNullOne(t,n)){throw new Error(`${`在单小程序登录模式下，未找到 uni-config-center/uni-id/config.json 中配置的支付宝小程序（${configKey}）的 appid 和 privateKey`}，${`在多小程序登录模式下，未找到appid：${t} 对应的 privateKey 请先在 uni-config-center/vk-unicloud/index.js 中配置支付宝小程序（vk.oauth.alipay.list）的 appid 和 privateKey`}`)}}}else if(t=o.appid,n=o.privateKey,a.pubfn.isNullOne(t,n))throw new Error(`请先在 uni-config-center/uni-id/config.json 中配置支付宝小程序（${configKey}）的 appid 和 privateKey`);return{appid:t,privateKey:n}},alipay$1.auth.code2Session=async function(e={}){let t,{vk:n}=util$a,{platform:a,needKey:i=!1,encryptedKey:r}=e;if(a||(a="mp-alipay"),n.pubfn.isNotNull(r))t=n.crypto.aes.decrypt({data:r}),t.encryptedKey=r;else{t=await alipay$1.auth.code2SessionMp(e);try{let e=n.crypto.aes.encrypt({data:t});t.encryptedKey=e}catch(e){}}return i||(delete t.sessionKey,delete t.accessToken,delete t.refreshToken),delete t.privateKey,t.platform=a,t.openid=t.userId,t},alipay$1.auth.code2SessionMp=async function(e={}){let{appid:t,privateKey:n}=alipay$1.auth.getAppidInfo(e),a=e.code||e.js_code,{vk:i}=util$a,r=await alipay$1.request({method:"alipay.system.oauth.token",appid:t,privateKey:n,data:{code:a,grant_type:"authorization_code"}});return{...i.pubfn.snake2camelJson(r),appid:t,code:0,msg:"ok"}},alipay$1.security={},alipay$1.security.msgSecCheck=async function(e={}){return{code:-1,msg:"支付宝未提供此API"}},alipay$1.security.imgSecCheck=async function(e={}){return{code:-1,msg:"支付宝未提供此API"}},alipay$1.acode={},alipay$1.acode.getMiniCode=async function(e={}){let{page:t,scene:n="miniCode=1",line_color:a}=e,{appid:i,privateKey:r}=alipay$1.auth.getAppidInfo(e),o={url_param:t,query_param:n,describe:"推广",color:a};return await alipay$1.request({method:"alipay.open.app.qrcode.create",appid:i,privateKey:r,data:{biz_content:o},successText:"生成成功"})},alipay$1.subscribeMessage={},alipay$1.subscribeMessage.send=async function(e={}){let{touser:t,form_id:n,template_id:a,page:i,data:r}=e,{appid:o,privateKey:s}=alipay$1.auth.getAppidInfo(e),d={to_user_id:t,form_id:n,user_template_id:a,page:i,data:r};return await alipay$1.request({method:"alipay.open.app.mini.templatemessage.send",appid:o,privateKey:s,data:{biz_content:d},successText:"发送成功"})};var alipay_1=alipay$1;let baidu={},util$b={};baidu.init=function(e){util$b=e},baidu.open={},baidu.open.auth={},baidu.open.auth.getAppidInfo=function(e={}){let{appid:t,appsecret:n}=e,{vk:a,config:i}=util$b;if(!t){let e=a.pubfn.getData(i,"vk.service.openapi.baidu")||{};t=e.appid,n=e.appsecret}if(a.pubfn.isNullOne(t,n))throw new Error("请在 uniCloud/cloudfunctions/common/uni-config-center/vk-unicloud/index.js 中配置并检查百度开放平台的 appid 和 appsecret 是否正确，参数路径：vk.service.openapi.baidu");return{appid:t,appsecret:n}},baidu.open.auth.getAccessTokenFn=async function(e={}){let{appid:t,appsecret:n}=baidu.open.auth.getAppidInfo(e),{vk:a,config:i}=util$b,r=await a.request({url:`https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=${t}&client_secret=${n}`,method:"GET"});return r.error_code?(console.error("getAccessToken失败：",r),{code:r.error_code,msg:r.error_msg,err:r}):{...r,code:0,msg:"ok"}},baidu.open.auth.getAccessToken=async function(e={}){let t,{appid:n,appsecret:a}=baidu.open.auth.getAppidInfo(e),{cache:i=!0}=e,{vk:r}=util$b,o="openapi-baidu-"+n;if(i&&(t=await r.globalDataCache.get(o)),r.pubfn.isNull(t)){let n=await baidu.open.auth.getAccessTokenFn(e);0===n.code&&(t=n.access_token,await r.globalDataCache.set(o,t,n.expires_in-3600),await r.globalDataCache.deleteExpired())}return t},baidu.open.ocr={},baidu.open.ocr.business_license=async function(e={}){let{image:t,url:n}=e;return await baidu.open.request({...e,action:"ocr/v1/business_license",actionVersion:"2.0",data:{image:t,url:n}})},baidu.open.ocr.idcard=async function(e={}){let{image:t,url:n,id_card_side:a,detect_risk:i,detect_photo:r}=e;return await baidu.open.request({...e,action:"ocr/v1/idcard",actionVersion:"2.0",data:{image:t,url:n,id_card_side:a,detect_risk:i,detect_photo:r}})},baidu.open.request=async function(e={}){let{vk:t}=util$b,n=await baidu.open.auth.getAccessToken(e);if(!n)return{code:-1,msg:"获取access_token失败，请检查vk-unicloud配置下vk.service.openapi.baidu配置的appid和appsecret是否正确"};let{action:a,actionVersion:i="2.0",timeout:r,header:o={"content-type":"application/x-www-form-urlencoded"},data:s}=e,d=await t.request({url:`https://aip.baidubce.com/rest/${i}/${a}?access_token=${n}`,method:"POST",timeout:r,headers:{"content-type":"application/x-www-form-urlencoded"},data:s});return d.error_code?{code:d.error_code,msg:d.error_msg,err:d}:{...d,code:0,msg:"ok"}};var baidu_1=baidu;let douyin={},util$c={};const serviceUrl$1="https://developer.toutiao.com",configKey$1="mp-toutiao.oauth.toutiao";function returnRes$1(e){return e.code=e.err_no||0,e.msg=e.err_tips||"",e}douyin.init=function(e){util$c=e},douyin.getConfig=function(){let{vk:e,config:t}=util$c;return e.pubfn.getUniIdConfig(t)},douyin.request=async function(e={}){let{vk:t}=util$c,{url:n,data:a={}}=e,{appid:i,appsecret:r}=douyin.auth.getAppidInfo(e);if(!0===a.access_token){let t=await douyin.auth.getAccessToken(e);if(!t)return{code:-1,msg:`获取access_token失败，请检查uni-id配置${configKey$1}配置下的appid和appsecret是否正确`};a.access_token=t}-1===n.indexOf("http")&&(n=`${serviceUrl$1}/${n}`);let o=await t.request({contentType:"json",dataType:"json",...e,url:n});return o=returnRes$1(o),o},douyin.decrypt={},douyin.decrypt.getPhoneNumber=async function(e={}){let{code:t,encryptedData:n}=e;if(!t&&n)return await douyin.decrypt.getPhoneNumberByEncryptedData(e);throw new Error("抖音不支持参数code，请传入encryptedData")},douyin.decrypt.getPhoneNumberByEncryptedData=async function(e={}){let t,{vk:n}=util$c,{encryptedData:a,iv:i,sessionKey:r,encryptedKey:o,code:s}=e,d={code:0,msg:"ok"},c=n.pubfn.isNullOneByObject({encryptedData:a,iv:i});if(c)return{code:-1,msg:c+"不能为空"};if(n.pubfn.isNullAll(r,o))return{code:-1,msg:"sessionKey和encryptedKey不能同时为空"};if(o){let e=n.crypto.aes.decrypt({data:o});t=e.appid,r=e.sessionKey}else{t=douyin.auth.getAppidInfo(e).appid}return d.data=douyin.decrypt.decryptData({appid:t,encryptedData:a,iv:i,sessionKey:r}),d.phone=d.data.phoneNumber,d.mobile=d.data.phoneNumber,d},douyin.decrypt.decryptData=function(e={}){let t,{appid:n,encryptedData:a,iv:i,sessionKey:r}=e,{vk:o,crypto:s}=util$c,d=new Buffer(r,"base64"),c=new Buffer(a,"base64"),u=new Buffer(i,"base64");try{let e=s.createDecipheriv("aes-128-cbc",d,u);e.setAutoPadding(!0),t=e.update(c,"binary","utf8"),t+=e.final("utf8"),t=JSON.parse(t)}catch(e){throw new Error(e)}return t.watermark.appid!==n?{code:-1,msg:"appid不一致"}:t},douyin.auth={},douyin.auth.getAppidInfo=function(e={}){let{appid:t,appsecret:n}=e,{vk:a,config:i}=util$c;const r=douyin.getConfig();let o=a.pubfn.getData(r,configKey$1)||{};if(t&&t!==o.appid){if(!n){let e=a.pubfn.getData(i,"vk.oauth.toutiao.list")||[],r=a.pubfn.getListItem(e,"appid",t)||{};if(r.appid&&(t=r.appid,n=r.appsecret),a.pubfn.isNullOne(t,n)){throw new Error(`${`在单小程序登录模式下，未找到 uni-config-center/uni-id/config.json 中配置的抖音小程序（${configKey$1}）的 appid 和 appsecret`}，${`在多小程序登录模式下，未找到appid：${t} 对应的 appsecret 请先在 uni-config-center/vk-unicloud/index.js 中配置抖音小程序（vk.oauth.toutiao.list）的 appid 和 appsecret`}`)}}}else if(t=o.appid,n=o.appsecret,a.pubfn.isNullOne(t,n))throw new Error(`请先在 uni-config-center/uni-id/config.json 中配置抖音小程序（${configKey$1}）的 appid 和 appsecret`);return{appid:t,appsecret:n}},douyin.auth.getAccessTokenFn=async function(e={}){let{appid:t,appsecret:n}=douyin.auth.getAppidInfo(e),{vk:a,config:i}=util$c,r=await a.request({url:serviceUrl$1+"/api/apps/v2/token",method:"POST",data:{appid:t,secret:n,grant_type:"client_credential"},contentType:"json",dataType:"json"});return r.err_no?(console.error("getAccessToken失败：",r),{code:r.err_no,msg:r.err_tips,err:r}):{code:0,msg:"ok",access_token:r.data.access_token,expires_in:r.data.expires_in}},douyin.auth.getAccessToken=async function(e={}){let t,{appid:n,appsecret:a}=douyin.auth.getAppidInfo(e),{cache:i=!0}=e,{vk:r}=util$c,o="mp-toutiao-access_token-"+n;if(i&&(t=await r.globalDataCache.get(o)),r.pubfn.isNull(t)){let n=await douyin.auth.getAccessTokenFn(e);0===n.code&&(t=n.access_token,await r.globalDataCache.set(o,t,240),await r.globalDataCache.deleteExpired())}return t},douyin.auth.code2Session=async function(e={}){let t,{vk:n}=util$c,{platform:a,needKey:i=!1,encryptedKey:r}=e;if(a||(a="mp-toutiao"),n.pubfn.isNotNull(r))t=n.crypto.aes.decrypt({data:r}),t.encryptedKey=r;else{t=await douyin.auth.code2SessionMp(e);try{let e=n.crypto.aes.encrypt({data:t});t.encryptedKey=e}catch(e){}}return i||(delete t.sessionKey,delete t.accessToken,delete t.refreshToken),delete t.appsecret,t.platform=a,t},douyin.auth.code2SessionMp=async function(e={}){let{appid:t,appsecret:n}=douyin.auth.getAppidInfo(e),a=e.code||e.js_code,i=e.anonymousCode,{vk:r}=util$c,o=await douyin.request({url:"api/apps/v2/jscode2session",method:"POST",appid:t,appsecret:n,data:{appid:t,secret:n,code:a,anonymous_code:i}});return o.code?(40018!==o.code&&40019!==o.code||(o.msg="无效code，请重新获取"),o):{...r.pubfn.snake2camelJson(o.data),appid:t,appsecret:n,code:0,msg:"ok"}},douyin.security={},douyin.security.msgSecCheck=async function(e={}){let{content:t,tasks:n}=e,{appid:a,appsecret:i}=douyin.auth.getAppidInfo(e),r=await douyin.auth.getAccessToken(e);if(!r)return{code:-1,msg:`获取access_token失败，请检查uni-id配置${configKey$1}配置下的appid和appsecret是否正确`};n||(n=[{content:t}]);let o=await douyin.request({url:"api/v2/tags/text/antidirt",method:"POST",appid:a,appsecret:i,header:{"X-Token":r},data:{tasks:n}});try{let e,t;o.data.map((n,a)=>{n.predicts.map((n,i)=>{n.hit&&(e||(e=!0,t=a))})}),e?(o.taskIndex=t,o.code=87014):o.code=0}catch(e){}return 40001===o.code&&(o.msg="access_token错误"),87014===o.code&&(o.msg="内容含有违法违规内容，请检查!"),0===o.code&&(o.msg="ok"),o},douyin.security.imgSecCheck=async function(e={}){let{image:t,access_token:n=!0}=e,a=e.base64||e.image_data,{appid:i,appsecret:r}=douyin.auth.getAppidInfo(e);if(a){let e=a.indexOf("base64,");e>-1&&(a=a.substring(e+"base64,".length))}let o=await douyin.request({url:"api/apps/censor/image",method:"POST",appid:i,appsecret:r,data:{app_id:i,access_token:n,image:t,image_data:a}}),s=[];try{let e,t={porn:"图片涉黄",cartoon_leader:"领导人漫画",anniversary_flag:"特殊标志",sensitive_flag:"敏感旗帜",sensitive_text:"敏感文字",leader_recognition:"敏感人物",bloody:"图片血腥",fandongtaibiao:"未准入台标",plant_ppx:"图片涉毒",high_risk_social_event:"社会事件",high_risk_boom:"爆炸",high_risk_money:"人民币",high_risk_terrorist_uniform:"极端服饰",high_risk_sensitive_map:"敏感地图",great_hall:"大会堂",cartoon_porn:"色情动漫",party_founding_memorial:"建党纪念"};o.predicts.map((n,a)=>{n.hit&&(e=!0,t[n.model_name]&&s.push(t[n.model_name]))}),o.code=e?87014:0}catch(e){}return 87014===o.code&&(0===s.length?o.msg="内容含有违规内容，请检查!":o.msg="图片中包含违规内容："+s.join("、")),0===o.code&&(o.msg="ok"),o},douyin.acode={},douyin.acode.getMiniCode=async function(e={}){let{path:t,appname:n="douyin",width:a,line_color:i,background:r,set_icon:o,access_token:s=!0}=e,{appid:d,appsecret:c}=douyin.auth.getAppidInfo(e),u=await douyin.request({url:"api/apps/qrcode",method:"POST",appid:d,appsecret:c,data:{access_token:s,appname:n,path:t,width:a,line_color:i,background:r,set_icon:o},dataType:"default",useContent:!0,header:{"content-type":"application/json;charset=utf8"}});if(u.length<500){let e=u.toString();try{return e=JSON.parse(e),{code:e.errcode,msg:e.errmsg}}catch(e){}return{code:-1,err:err}}return u},douyin.subscribeMessage={},douyin.subscribeMessage.send=async function(e={}){let{touser:t,template_id:n,page:a,data:i,access_token:r=!0}=e,{appid:o,appsecret:s}=douyin.auth.getAppidInfo(e);return await douyin.request({url:"api/apps/subscribe_notification/developer/v1/notify",method:"POST",appid:o,appsecret:s,data:{access_token:r,app_id:o,tpl_id:n,open_id:t,data:i,page:a}})};var douyin_1=douyin;let qq={},util$d={};const serviceUrl$2="https://api.q.qq.com",configKey$2="mp-qq.oauth.qq";function returnRes$2(e){return e.code=e.errcode||e.errCode||0,e.msg=e.errmsg||e.errMsg||"",e}qq.init=function(e){util$d=e},qq.getConfig=function(){let{vk:e,config:t}=util$d;return e.pubfn.getUniIdConfig(t)},qq.request=async function(e={}){let{vk:t}=util$d,{url:n,method:a,data:i={}}=e;if(-1===n.indexOf("http")&&(n=`${serviceUrl$2}/${n}`),!0===i.access_token){delete i.access_token;let t=await qq.auth.getAccessToken(e);if(!t)return{code:-1,msg:"获取access_token失败，请检查uni-id配置下的mp-qq配置的appid和appsecret是否正确"};n+=-1===n.indexOf("?")?"?":"&",n+="access_token="+t}let r=await t.request({...e,url:n});return r=returnRes$2(r),r},qq.auth={},qq.auth.getAppidInfo=function(e={}){let{appid:t,appsecret:n}=e,{vk:a,config:i}=util$d;const r=qq.getConfig();let o=a.pubfn.getData(r,configKey$2)||{};if(t&&t!==o.appid){if(!n){let e=a.pubfn.getData(i,"vk.oauth.qq.list")||[],r=a.pubfn.getListItem(e,"appid",t)||{};if(r.appid&&(t=r.appid,n=r.appsecret),a.pubfn.isNullOne(t,n)){throw new Error(`${"在单小程序登录模式下，未找到 uni-config-center/uni-id/config.json 中配置的QQ小程序（mp-qq.oauth.qq）的 appid 和 appsecret"}，${`在多小程序登录模式下，未找到appid：${t} 对应的 appsecret 请先在 uni-config-center/vk-unicloud/index.js 中配置QQ小程序（vk.oauth.qq.list）的 appid 和 appsecret`}`)}}}else if(t=o.appid,n=o.appsecret,a.pubfn.isNullOne(t,n))throw new Error("请先在 uni-config-center/uni-id/config.json 中配置QQ小程序（mp-qq.oauth.qq）的 appid 和 appsecret");return{appid:t,appsecret:n}},qq.auth.getAccessTokenFn=async function(e={}){let{appid:t,appsecret:n}=qq.auth.getAppidInfo(e),{vk:a,config:i}=util$d,r=await a.request({url:`https://api.q.qq.com/api/getToken?grant_type=client_credential&appid=${t}&secret=${n}`,method:"GET"});return r.errcode?(console.error("getAccessToken失败：",r),{code:r.errcode,msg:r.errmsg,err:r}):{code:0,msg:"ok",access_token:r.access_token,expires_in:r.expires_in}},qq.auth.getAccessToken=async function(e={}){let t,{appid:n,appsecret:a}=qq.auth.getAppidInfo(e),{cache:i=!0}=e,{vk:r}=util$d,o="mp-qq-access_token-"+n;if(i&&(t=await r.globalDataCache.get(o)),r.pubfn.isNull(t)){let n=await qq.auth.getAccessTokenFn(e);0===n.code&&(t=n.access_token,await r.globalDataCache.set(o,t,240),await r.globalDataCache.deleteExpired())}return t},qq.auth.code2Session=async function(e={}){let t,{vk:n}=util$d,{platform:a,needKey:i=!1,encryptedKey:r}=e;if(a||(a="mp-qq"),n.pubfn.isNotNull(r))t=n.crypto.aes.decrypt({data:r}),t.encryptedKey=r;else{t=await qq.auth.code2SessionMpQQ(e);try{let e=n.crypto.aes.encrypt({data:t});t.encryptedKey=e}catch(e){}}return i||(delete t.sessionKey,delete t.accessToken,delete t.refreshToken),delete t.appsecret,t.platform=a,t},qq.auth.code2SessionMpQQ=async function(e={}){let{appid:t,appsecret:n}=qq.auth.getAppidInfo(e),a=e.code||e.js_code,{vk:i}=util$d,r=await qq.request({url:`sns/jscode2session?appid=${t}&secret=${n}&js_code=${a}&grant_type=authorization_code`,method:"GET"});return r.code?(-101600007===r.code&&(r.msg="appid错误"),-101222100===r.code&&(r.msg="appsecret错误"),-2100===r.code&&(r.msg="无效code，请重新获取"),r):(r=i.pubfn.snake2camelJson(r),{appid:t,appsecret:n,...r,code:0,msg:"ok"})},qq.security={},qq.security.msgSecCheck=async function(e={}){let{content:t,access_token:n=!0}=e,{appid:a,appsecret:i}=qq.auth.getAppidInfo(e),r=await qq.request({url:"api/json/security/MsgSecCheck",method:"POST",appid:a,appsecret:i,data:{access_token:n,appid:a,content:t}});return 40001===r.code&&(r.msg="access_token错误"),87014===r.code&&(r.msg="内容含有违法违规内容，请检查!"),r},qq.security.imgSecCheck=async function(e={}){let{vk:t}=util$d,{dataBuffer:n,formData:a,base64:i}=e,{appid:r,appsecret:o}=qq.auth.getAppidInfo(e),s=await qq.auth.getAccessToken(e);if(!s)return{code:-1,msg:"获取access_token失败，请检查uni-id配置下的mp-qq配置的appid和appsecret是否正确"};if(i&&!n){let e="base64,",t=i.indexOf(e);t>-1&&(i=i.substring(t+e.length)),n=new Buffer(i,"base64")}n&&!a&&(a=new t.formDataUtil.FormData,a.append("appid",r),a.append("media",n,{filename:Date.now()+".png",contentType:"image/png"}));let d=await qq.request({url:"api/json/security/ImgSecCheck?access_token="+s,content:a.getBuffer(),headers:a.getHeaders()});switch(d.code){case 40001:d.msg="access_token错误";break;case 87014:d.msg="图片内容含有违法违规内容，请检查!";break;case 40006:d.msg="图片大小不能超过1M"}return d},qq.acode={},qq.acode.getMiniCode=async function(e={}){let{path:t,access_token:n=!0}=e,{appid:a,appsecret:i}=qq.auth.getAppidInfo(e),r=await qq.request({url:"api/json/qqa/CreateMiniCode",method:"POST",appid:a,appsecret:i,data:{access_token:n,appid:a,path:t},dataType:"default",useContent:!0,header:{"content-type":"application/json;charset=utf8"}});if(r.length<500){let e=r.toString();try{e=JSON.parse(e)}catch(e){}return r=returnRes$2(e),40001===r.code&&(r.msg="access_token错误"),41030===r.code&&(r.msg="小程序页面不存在!"),r}return r},qq.subscribeMessage={},qq.subscribeMessage.send=async function(e={}){let{vk:t}=util$d,{touser:n,template_id:a,page:i,data:r,emphasis_keyword:o,oac_appid:s,use_robot:d}=e,c=await qq.auth.getAccessToken(e);if(!c)return{code:-1,msg:"获取access_token失败，请检查uni-id配置下的mp-qq配置的appid和appsecret是否正确"};let u=await t.request({url:`${serviceUrl$2}/api/json/subscribe/SendSubscriptionMessage?access_token=${c}`,method:"POST",data:{touser:n,template_id:a,page:i,data:r,emphasis_keyword:o,oac_appid:s,use_robot:d},useContent:!0,header:{"content-type":"application/json;charset=utf8"}});switch(u=returnRes$2(u),u.code){case 40003:u.msg="touser字段openid为空或者不正确";break;case 40037:u.msg="订阅模板id为空不正确";break;case 43101:u.msg="用户未订阅该消息";break;case 40036:u.msg="您的小程序不支持发送长期订阅消息";break;case 47003:u.msg="模板参数不准确，可能为空或者不满足规则，errmsg会提示具体是哪个字段出错";break;case 41030:u.msg="page路径不正确，需要保证在现网版本小程序中存在"}return u};var qq_1=qq;let weixin={},util$e={};function returnRes$3(e){let t=e.errcode||0,n=e.errmsg||"";return e.code=t,e.msg=n,e}weixin.init=function(e){util$e=e},weixin.getConfig=function(){let{vk:e,config:t}=util$e;return e.pubfn.getUniIdConfig(t)},weixin.request=async function(e={}){let{vk:t}=util$e,{url:n,method:a,data:i,access_token:r}=e;if(r||(r=await weixin.auth.getAccessToken(e)),!r)return{code:-1,msg:"获取access_token失败，请检查uni-id配置下的mp-weixin配置的appid和appsecret是否正确"};-1===n.indexOf("http")&&(n="https://api.weixin.qq.com/"+n),a||(a=t.pubfn.isNull(i)?"GET":"POST"),a&&"POST"===a.toUpperCase()&&void 0===e.useContent&&(e.useContent=!0),n+=-1===n.indexOf("?")?"?":"&",n+="access_token="+r;let o=await t.request({...e,url:n});return o=returnRes$3(o),o},weixin.decrypt={},weixin.decrypt.getPhoneNumber=async function(e={}){let{vk:t}=util$e,{code:n}=e;if(!n&&e.encryptedData)return await weixin.decrypt.getPhoneNumberByEncryptedData(e);let a={code:0,msg:"ok"},i=t.pubfn.isNullOneByObject({code:n});if(i)return{code:-1,msg:i+"不能为空"};let{appid:r,appsecret:o}=weixin.auth.getAppidInfo(e),s=await t.openapi.weixin.request({method:"POST",url:"wxa/business/getuserphonenumber",data:{code:n},appid:r,appsecret:o});return 0!==s.code?s:(a.data=s.phone_info,a.phone=a.data.phoneNumber,a.mobile=a.data.phoneNumber,a)},weixin.decrypt.getPhoneNumberByEncryptedData=async function(e={}){let t,{vk:n}=util$e,{encryptedData:a,iv:i,sessionKey:r,encryptedKey:o,code:s}=e,d={code:0,msg:"ok"},c=n.pubfn.isNullOneByObject({encryptedData:a,iv:i});if(c)return{code:-1,msg:c+"不能为空"};if(n.pubfn.isNullAll(r,o))return{code:-1,msg:"sessionKey和encryptedKey不能同时为空"};if(o){let e=n.crypto.aes.decrypt({data:o});t=e.appid,r=e.sessionKey}else{t=weixin.auth.getAppidInfo(e).appid}return d.data=weixin.decrypt.decryptData({appid:t,encryptedData:a,iv:i,sessionKey:r}),d.phone=d.data.phoneNumber,d.mobile=d.data.phoneNumber,d},weixin.decrypt.decryptData=function(e={}){let t,{appid:n,encryptedData:a,iv:i,sessionKey:r}=e,{vk:o,crypto:s}=util$e,d=new Buffer(r,"base64"),c=new Buffer(a,"base64"),u=new Buffer(i,"base64");try{let e=s.createDecipheriv("aes-128-cbc",d,u);e.setAutoPadding(!0),t=e.update(c,"binary","utf8"),t+=e.final("utf8"),t=JSON.parse(t)}catch(e){throw new Error(e)}return t.watermark.appid!==n?{code:-1,msg:"appid不一致"}:t},weixin.auth={},weixin.auth.getAppidInfo=function(e={}){let{appid:t,appsecret:n}=e,{vk:a,config:i}=util$e;const r=weixin.getConfig(),o="mp-weixin.oauth.weixin";let s=a.pubfn.getData(r,o)||{};if(t&&t!==s.appid){if(!n){let e=a.pubfn.getData(i,"vk.oauth.weixin.list")||[],r=a.pubfn.getListItem(e,"appid",t)||{};if(r.appid&&(t=r.appid,n=r.appsecret),a.pubfn.isNullOne(t,n)){throw new Error(`${`在单小程序登录模式下，未找到 uni-config-center/uni-id/config.json 中配置的微信小程序（${o}）的 appid 和 appsecret`}，${`在多小程序登录模式下，未找到appid：${t} 对应的 appsecret 请先在 uni-config-center/vk-unicloud/index.js 中配置微信（vk.oauth.weixin.list）的 appid 和 appsecret`}`)}}}else if(t=s.appid,n=s.appsecret,a.pubfn.isNullOne(t,n))throw new Error(`请先在 uni-config-center/uni-id/config.json 中配置微信小程序（${o}）的 appid 和 appsecret`);return{appid:t,appsecret:n}},weixin.auth.getAccessTokenFn=async function(e={}){let{appid:t,appsecret:n}=weixin.auth.getAppidInfo(e),{vk:a,config:i}=util$e,r=await a.request({url:"https://api.weixin.qq.com/cgi-bin/stable_token",method:"POST",headers:{"content-type":"application/json"},useProxy:!0,data:{grant_type:"client_credential",appid:t,secret:n,force_refresh:!1}});return r.errcode?(console.error("getAccessToken失败：",r),{code:r.errcode,msg:r.errmsg,err:r}):{code:0,msg:"ok",access_token:r.access_token,expires_in:r.expires_in}},weixin.auth.getAccessToken=async function(e={}){let t,{appid:n,appsecret:a}=weixin.auth.getAppidInfo(e),{cache:i=!0}=e,{vk:r}=util$e,o="mp-weixin-"+n;if(i&&(t=await r.globalDataCache.get(o)),r.pubfn.isNull(t)){let n=await weixin.auth.getAccessTokenFn(e);if(0===n.code){t=n.access_token;let e=n.expires_in;e>300&&(e=300),await r.globalDataCache.set(o,t,e),await r.globalDataCache.deleteExpired()}}return t},weixin.auth.code2Session=async function(e={}){let t,{vk:n,uniID:a}=util$e,{platform:i,context:r,needKey:o=!1,encryptedKey:s}=e;if(i||(i=n.pubfn.getPlatformForUniId(r)||"mp-weixin"),n.pubfn.isNotNull(s))t=n.crypto.aes.decrypt({data:s}),t.encryptedKey=s;else{if("mp-weixin"===i)t=await weixin.auth.code2SessionMpWeixin(e);else if("h5-weixin"===i)t=await weixin.h5.auth.code2Session(e);else if(["app","app-plus"].indexOf(i)>-1){t=await a.code2SessionWeixin(e);let{appid:n,appsecret:i}=weixin.app.auth.getAppidInfo(e);t.appid=n,t.appsecret=i}else if(["h5","web"].indexOf(i)>-1){t=await a.code2SessionWeixin(e);let{appid:n,appsecret:i}=weixin.pc.auth.getAppidInfo(e);t.appid=n,t.appsecret=i}else t=await a.code2SessionWeixin(e);try{if(0===t.code){let e=n.crypto.aes.encrypt({data:t});t.encryptedKey=e}}catch(e){}}return o||(delete t.sessionKey,delete t.accessToken,delete t.refreshToken),delete t.appsecret,t.platform=i,t},weixin.auth.code2SessionMpWeixin=async function(e={}){let{appid:t,appsecret:n}=weixin.auth.getAppidInfo(e),a=e.code||e.js_code,{vk:i}=util$e,r=await i.request({url:`https://api.weixin.qq.com/sns/jscode2session?appid=${t}&secret=${n}&js_code=${a}&grant_type=authorization_code`,method:"GET"});if(r.errcode){let e=r.errmsg;return 40163===r.errcode&&(e="该code已被使用，请重新获取"),40029===r.errcode&&(e="无效code，请重新获取"),{...r,code:r.errcode,msg:e}}return r=i.pubfn.snake2camelJson(r),{appid:t,appsecret:n,...r,code:0,msg:"ok"}},weixin.wxacode={},weixin.wxacode.getUnlimited=async function(e={}){let{vk:t}=util$e,{access_token:n,page:a,scene:i,check_path:r,env_version:o,width:s,auto_color:d,line_color:c,is_hyaline:u}=e;if(n||(n=await weixin.auth.getAccessToken(e)),!n)return{code:-1,msg:"获取access_token失败，请检查uni-id配置下的mp-weixin配置的appid和appsecret是否正确"};let l=await t.request({url:"https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token="+n,method:"POST",data:{page:a,scene:i,check_path:r,env_version:o,width:s,auto_color:d,line_color:c,is_hyaline:u},dataType:"default",useContent:!0,headers:{encoding:null}});if(l.length<500){let e=l.toString();try{e=JSON.parse(e)}catch(e){}return l=returnRes$3(e),40001===l.code&&(l.msg="access_token错误"),41030===l.code&&(l.msg="小程序页面不存在!"),l}return Buffer.isBuffer(l)?l:returnRes$3(l)},weixin.urlscheme={},weixin.urlscheme.generate=async function(e={}){let{vk:t}=util$e,{access_token:n,jump_wxa:a,is_expire:i,expire_time:r,expire_type:o,expire_interval:s}=e;if(n||(n=await weixin.auth.getAccessToken(e)),!n)return{code:-1,msg:"获取access_token失败，请检查uni-id配置下的mp-weixin配置的appid和appsecret是否正确"};let d=await t.request({url:"https://api.weixin.qq.com/wxa/generatescheme?access_token="+n,method:"POST",data:{jump_wxa:a,is_expire:i,expire_time:r,expire_type:o,expire_interval:s},useContent:!0});return d=returnRes$3(d),40001===d.code&&(d.msg="access_token错误"),40165===d.code&&(d.msg="小程序页面不存在!"),d},weixin.urllink={},weixin.urllink.generate=async function(e={}){let{vk:t}=util$e,{access_token:n,path:a,query:i,is_expire:r,expire_type:o,expire_time:s,expire_interval:d,cloud_base:c,env_version:u}=e;if(n||(n=await weixin.auth.getAccessToken(e)),!n)return{code:-1,msg:"获取access_token失败，请检查uni-id配置下的mp-weixin配置的appid和appsecret是否正确"};let l=await t.request({url:"https://api.weixin.qq.com/wxa/generate_urllink?access_token="+n,method:"POST",data:{path:a,query:i,is_expire:r,expire_type:o,expire_time:s,expire_interval:d,cloud_base:c,env_version:u},useContent:!0});return l=returnRes$3(l),40001===l.code&&(l.msg="access_token错误"),40165===l.code&&(l.msg="小程序页面不存在!"),l},weixin.security={},weixin.security.msgSecCheck=async function(e={}){let{vk:t}=util$e,{content:n,version:a,scene:i,openid:r,title:o,nickname:s,signature:d}=e,c=await weixin.auth.getAccessToken(e);if(!c)return{code:-1,msg:"获取access_token失败，请检查uni-id配置下的mp-weixin配置的appid和appsecret是否正确"};let u=await t.request({url:"https://api.weixin.qq.com/wxa/msg_sec_check?access_token="+c,method:"POST",data:{content:n,version:a,scene:i,openid:r,title:o,nickname:s,signature:d},useContent:!0});return u=returnRes$3(u),40001===u.code&&(u.msg="access_token错误"),87014===u.code&&(u.msg="内容含有违法违规内容，请检查!"),2===a&&u.result&&100!==u.result.label&&(u.code=u.result.label,u.msg="内容含有违法违规内容，请检查!"),u},weixin.security.imgSecCheck=async function(e={}){let{vk:t}=util$e,{dataBuffer:n,formData:a,base64:i}=e,r=await weixin.auth.getAccessToken(e);if(!r)return{code:-1,msg:"获取access_token失败，请检查uni-id配置下的mp-weixin配置的appid和appsecret是否正确"};if(i&&!n){let e="base64,",t=i.indexOf(e);t>-1&&(i=i.substring(t+e.length)),n=new Buffer(i,"base64")}n&&!a&&(a=new t.formDataUtil.FormData,a.append("media",n,{filename:Date.now()+".png",contentType:"image/png"}));let o=await t.request({url:"https://api.weixin.qq.com/wxa/img_sec_check?access_token="+r,content:a.getBuffer(),headers:a.getHeaders()});switch(o=returnRes$3(o),o.code){case 40001:o.msg="access_token错误";break;case 87014:o.msg="图片内容含有违法违规内容，请检查!";break;case 40006:o.msg="图片大小不能超过1M"}return o},weixin.security.imgSecCheckAsync=async function(e={}){let{vk:t}=util$e,{media_url:n,media_type:a,version:i=2,scene:r,openid:o}=e,s=await weixin.auth.getAccessToken(e),d=await t.openapi.weixin.request({method:"POST",url:"wxa/media_check_async",access_token:s,data:{media_url:n,media_type:a,version:i,scene:r,openid:o},headers:{"Content-Type":"application/json; charset=UTF-8"}});return d=returnRes$3(d),d},weixin.riskControl={},weixin.riskControl.getUserRiskRank=async function(e={}){let{vk:t}=util$e,{appid:n,openid:a,scene:i,mobile_no:r,client_ip:o,email_address:s,extended_info:d,is_test:c}=e,u=await weixin.auth.getAccessToken(e);if(!u)return{code:-1,msg:"获取access_token失败，请检查uni-id配置下的mp-weixin配置的appid和appsecret是否正确"};let l=await t.request({url:"https://api.weixin.qq.com/wxa/getuserriskrank?access_token="+u,method:"POST",data:{appid:n,openid:a,scene:i,mobile_no:r,client_ip:o,email_address:s,extended_info:d,is_test:c},useContent:!0});return l=returnRes$3(l),40001===l.code&&(l.msg="access_token错误"),l},weixin.subscribeMessage={},weixin.subscribeMessage.send=async function(e={}){let{vk:t}=util$e,{touser:n,template_id:a,page:i,data:r,miniprogram_state:o="formal",lang:s="zh_CN"}=e,d=await weixin.auth.getAccessToken(e);if(!d)return{code:-1,msg:"获取access_token失败，请检查uni-id配置下的mp-weixin配置的appid和appsecret是否正确"};let c=await t.request({url:"https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token="+d,method:"POST",data:{touser:n,template_id:a,page:i,data:r,miniprogram_state:o,lang:s},useContent:!0});switch(c=returnRes$3(c),c.code){case 40003:c.msg="touser字段openid为空或者不正确";break;case 40037:c.msg="订阅模板id为空不正确";break;case 43101:c.msg="用户未订阅该消息";break;case 47003:c.msg="模板参数不准确，可能为空或者不满足规则，errmsg会提示具体是哪个字段出错";break;case 41030:c.msg="page路径不正确，需要保证在现网版本小程序中存在"}return c},weixin.subscribeMessage.getCategory=async function(e={}){let{vk:t}=util$e,n=await weixin.auth.getAccessToken(e),a=await t.openapi.weixin.request({method:"GET",url:"wxaapi/newtmpl/getcategory",access_token:n,data:{}});return a=returnRes$3(a),a},weixin.subscribeMessage.getPubTemplateTitleList=async function(e={}){let{vk:t}=util$e,{ids:n,start:a=0,limit:i=30}=e,r=await weixin.auth.getAccessToken(e),o=await t.openapi.weixin.request({method:"GET",url:"wxaapi/newtmpl/getpubtemplatetitles",access_token:r,data:{ids:n,start:a,limit:i}});return o=returnRes$3(o),o},weixin.subscribeMessage.getMessageTemplateList=async function(e={}){let{vk:t}=util$e,{ids:n,start:a=0,limit:i=30}=e,r=await weixin.auth.getAccessToken(e),o=await t.openapi.weixin.request({method:"GET",url:"wxaapi/newtmpl/gettemplate",access_token:r,data:{ids:n,start:a,limit:i}});return o=returnRes$3(o),o},weixin.subscribeMessage.addMessageTemplate=async function(e={}){let{vk:t}=util$e,{tid:n,kidList:a,sceneDesc:i}=e,r=await weixin.auth.getAccessToken(e),o=await t.openapi.weixin.request({method:"POST",url:"wxaapi/newtmpl/addtemplate",access_token:r,data:{tid:n,kidList:a,sceneDesc:i},headers:{"Content-Type":"application/json; charset=UTF-8"}});return o=returnRes$3(o),o},weixin.subscribeMessage.deleteMessageTemplate=async function(e={}){let{vk:t}=util$e,{priTmplId:n}=e,a=await weixin.auth.getAccessToken(e),i=await t.openapi.weixin.request({method:"POST",url:"wxaapi/newtmpl/deltemplate",access_token:a,data:{priTmplId:n},headers:{"Content-Type":"application/json; charset=UTF-8"}});return i=returnRes$3(i),i},weixin.uniformMessage={},weixin.uniformMessage.send=async function(e={}){let{vk:t}=util$e,{appid:n,touser:a,template_appid:i,template_id:r,url:o,miniprogram:s,data:d}=e,c=await weixin.auth.getAccessToken(e);if(!c)return{code:-1,msg:"获取access_token失败，请检查uni-id配置下的mp-weixin配置的appid和appsecret是否正确"};if(!i){i=weixin.h5.auth.getAppidInfo({appid:i}).appid}let u=await t.request({url:"https://api.weixin.qq.com/cgi-bin/message/wxopen/template/uniform_send?access_token="+c,method:"POST",data:{touser:a,mp_template_msg:{appid:i,template_id:r,url:o,miniprogram:s,data:d}},useContent:!0});switch(u=returnRes$3(u),u.code){case 40003:u.msg="touser字段openid为空或者不正确";break;case 40037:u.msg="订阅模板id为空不正确";break;case 43101:u.msg="用户未订阅该消息";break;case 47003:u.msg="模板参数不准确，可能为空或者不满足规则，errmsg会提示具体是哪个字段出错";break;case 41030:u.msg="page路径不正确，需要保证在现网版本小程序中存在"}return u},weixin.livebroadcast={},weixin.livebroadcast.getLiveInfo=async function(e={}){let{vk:t}=util$e,{pageIndex:n=1,pageSize:a=100}=e,i=await weixin.auth.getAccessToken(e);if(!i)return{code:-1,msg:"获取access_token失败，请检查uni-id配置下的mp-weixin配置的appid和appsecret是否正确"};if(n<=0)return{code:-1,msg:"pageIndex必须是大于0的整数"};let r=(n-1)*a,o=a,s=await t.request({url:"https://api.weixin.qq.com/wxa/business/getliveinfo?access_token="+i,method:"POST",data:{start:r,limit:o},useContent:!0});switch(s=returnRes$3(s),s.code){case 941e4:s.msg="直播间列表为空"}return s},weixin.app={},weixin.app.auth={},weixin.app.auth.getAppidInfo=function(e={}){let{appid:t,appsecret:n}=e,{vk:a,config:i}=util$e;const r=weixin.getConfig(),o=(a.pubfn.getData(r,"preferedAppPlatform")||"app-plus")+".oauth.weixin";let s=a.pubfn.getData(r,o)||{};if(t&&t!==s.appid){if(!n){let e=a.pubfn.getData(i,"vk.oauth.weixin.list")||[],r=a.pubfn.getListItem(e,"appid",t)||{};if(r&&(t=r.appid,n=r.appsecret),a.pubfn.isNullOne(t,n)){throw new Error(`${`在单APP登录模式下，未找到 uni-config-center/uni-id/config.json 中配置的微信APP（${o}）的 appid 和 appsecret`}，${`在多APP登录模式下，未找到appid：${t} 对应的 appsecret 请先在 uni-config-center/vk-unicloud/index.js 中配置微信（vk.oauth.weixin.list）的 appid 和 appsecret`}`)}}}else if(t=s.appid,n=s.appsecret,a.pubfn.isNullOne(t,n))throw new Error(`请先在 uni-config-center/uni-id/config.json 中配置微信app（${o}）的 appid 和 appsecret`);return{appid:t,appsecret:n}},weixin.app.auth.getAccessToken=async function(e={}){let{appid:t,appsecret:n}=weixin.app.auth.getAppidInfo(e),{vk:a,config:i}=util$e,{code:r}=e,o=await a.request({url:`https://api.weixin.qq.com/sns/oauth2/access_token?appid=${t}&secret=${n}&code=${r}&grant_type=authorization_code`,method:"GET"});return o.errcode?(console.error("getAccessToken失败：",o),{code:o.errcode,msg:o.errmsg,err:o}):{...o,code:0,msg:"ok"}},weixin.app.auth.getUserInfo=async function(e={}){let{vk:t,config:n}=util$e,{access_token:a,openid:i,lang:r="zh-CN"}=e;if(!a)return{code:-1,msg:"access_token不能为空"};let o=await t.request({url:`https://api.weixin.qq.com/sns/userinfo?access_token=${a}&openid=${i}$lang=${r}`,method:"GET"});return o.errcode?(console.error("getUserInfo失败：",o),{code:o.errcode,msg:o.errmsg,err:o}):{...o,code:0,msg:"ok",avatar:o.headimgurl,gender:o.sex}},weixin.h5={},weixin.h5.request=async function(e={}){let{vk:t}=util$e,{url:n,method:a,data:i,access_token:r}=e;if(r||(r=await weixin.h5.auth.getAccessToken(e)),!r)return{code:-1,msg:"获取access_token失败，请检查uni-id配置下的h5-weixin配置的appid和appsecret是否正确"};-1===n.indexOf("http")&&(n="https://api.weixin.qq.com/"+n),a||(a=t.pubfn.isNull(i)?"GET":"POST"),a&&"POST"===a.toUpperCase()&&void 0===e.useContent&&(e.useContent=!0),n+=-1===n.indexOf("?")?"?":"&",n+="access_token="+r;let o=await t.request({...e,url:n});return o=returnRes$3(o),o},weixin.h5.auth={},weixin.h5.auth.getAppidInfo=function(e={}){let{appid:t,appsecret:n}=e,{vk:a,config:i}=util$e;const r=weixin.getConfig(),o=(a.pubfn.getData(r,"preferedWebPlatform")||"h5")+"-weixin.oauth.weixin";let s=a.pubfn.getData(r,o)||{};if(t&&t!==s.appid){if(!n){let e=a.pubfn.getData(i,"vk.oauth.weixin.list")||[],r=a.pubfn.getListItem(e,"appid",t)||{};if(r.appid&&(t=r.appid,n=r.appsecret),a.pubfn.isNullOne(t,n)){throw new Error(`${`在单公众号登录模式下，未找到 uni-config-center/uni-id/config.json 中配置的微信公众号（${o}）的 appid 和 appsecret`}，${`在多公众号登录模式下，未找到appid：${t} 对应的 appsecret 请先在 uni-config-center/vk-unicloud/index.js 中配置微信（vk.oauth.weixin.list）的 appid 和 appsecret`}`)}}}else if(t=s.appid,n=s.appsecret,a.pubfn.isNullOne(t,n))throw new Error(`请先在 uni-config-center/uni-id/config.json 中配置微信公众号（${o}）的 appid 和 appsecret`);return{appid:t,appsecret:n}},weixin.h5.auth.code2Session=async function(e={}){let{appid:t,appsecret:n}=weixin.h5.auth.getAppidInfo(e),{code:a}=e,{vk:i}=util$e,r=await i.request({url:`https://api.weixin.qq.com/sns/oauth2/access_token?appid=${t}&secret=${n}&code=${a}&grant_type=authorization_code`,method:"GET"});if(r.errcode){let e=r.errmsg;return 40163===r.errcode&&(e="该code已被使用，请重新获取"),40029===r.errcode&&(e="无效code，请重新获取"),{...r,code:r.errcode,msg:e}}return r=i.pubfn.snake2camelJson(r),{appid:t,appsecret:n,...r,code:0,msg:"ok"}},weixin.h5.auth.getAccessToken=async function(e={}){let t,{appid:n,appsecret:a}=weixin.h5.auth.getAppidInfo(e),{cache:i=!0}=e,{vk:r}=util$e,o="h5-weixin-"+n;if(i&&(t=await r.globalDataCache.get(o)),r.pubfn.isNull(t)){let n=await weixin.h5.auth.getAccessTokenFn(e);if(0===n.code)t=n.access_token,await r.globalDataCache.set(o,t,240),await r.globalDataCache.deleteExpired();else if(40164===n.code)throw{code:n.code,msg:"请将固定IP全部加入微信公众号IP白名单，详见搜索文档：固定ip",err:n}}return t},weixin.h5.auth.getAccessTokenFn=async function(e={}){let t,{appid:n,appsecret:a}=weixin.h5.auth.getAppidInfo(e),{vk:i,config:r}=util$e;if("aliyun"===i.pubfn.getUniCloudProvider()){let e=await uniCloud.httpProxyForEip.get("https://api.weixin.qq.com/cgi-bin/token",{grant_type:"client_credential",appid:n,secret:a});if("string"==typeof e)try{e=JSON.parse(e)}catch(e){t={errcode:-1,errmsg:"异常"+e.message}}t="object"==typeof e?e.body:{errcode:-1,errmsg:"异常:getRes未获取到值"}}else t=await i.request({url:`https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${n}&secret=${a}`,method:"GET"});return t.errcode?(console.error("getAccessToken失败：",t),{code:t.errcode,msg:t.errmsg,err:t}):{code:0,msg:"ok",access_token:t.access_token,expires_in:t.expires_in}},weixin.h5.auth.getJsapiTicket=async function(e={}){let t,{appid:n,appsecret:a}=weixin.h5.auth.getAppidInfo(e),{cache:i=!0}=e,{vk:r}=util$e,o=`h5-weixin:${n}:jsapi_ticket`;if(i&&(t=await r.globalDataCache.get(o)),r.pubfn.isNull(t)){let n=await weixin.h5.auth.getJsapiTicketFn(e);0===n.code?(t=n.jsapi_ticket,await r.globalDataCache.set(o,t,240),await r.globalDataCache.deleteExpired()):console.log("获取微信公众号jsapi_ticket失败:",n)}return{code:0,msg:"ok",appid:n,jsapi_ticket:t}},weixin.h5.auth.getJsapiTicketFn=async function(e={}){let{appid:t,appsecret:n}=weixin.h5.auth.getAppidInfo(e),{vk:a,config:i}=util$e,r=await weixin.h5.auth.getAccessToken(e);if(!r)return{code:-1,msg:"获取access_token失败，请检查uni-id配置下的h5-weixin配置的appid和appsecret是否正确"};let o=await a.request({url:`https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=${r}&type=jsapi`,method:"GET"});return o.errcode?(console.error("getJsapiTicketFn失败：",o),{code:o.errcode,msg:o.errmsg,err:o}):{code:0,msg:"ok",appid:t,jsapi_ticket:o.ticket,expires_in:o.expires_in}},weixin.h5.auth.getJsapiSign=async function(e={}){let{vk:t,crypto:n}=util$e,{appid:a,appsecret:i}=weixin.h5.auth.getAppidInfo(e),{jsapi_ticket:r}=await t.openapi.weixin.h5.auth.getJsapiTicket(e),{url:o}=e,s=t.pubfn.random(6),d=parseInt((new Date).getTime()/1e3)+"";return{code:0,msg:"ok",url:o,appid:a,config:{appId:a,timestamp:d,nonceStr:s,signature:(e=>{let t=n.createHash("sha1");return t.update(e),e=t.digest("hex")})((e=>Object.keys(e).filter(t=>"sign"!==t&&void 0!==e[t]&&""!==e[t]).sort().map(t=>t+"="+e[t]).join("&"))({jsapi_ticket:r,noncestr:s,timestamp:d,url:o}))}}},weixin.h5.subscribeMessage={},weixin.h5.subscribeMessage.send=async function(e={}){let{vk:t}=util$e,{touser:n,template_id:a,page:i,data:r,miniprogram:o}=e,s=await weixin.h5.auth.getAccessToken(e);if(!s)return{code:-1,msg:"获取access_token失败，请检查uni-id配置下的h5-weixin配置的appid和appsecret是否正确"};let d=await t.request({url:"https://api.weixin.qq.com/cgi-bin/message/subscribe/bizsend?access_token="+s,method:"POST",data:{touser:n,template_id:a,page:i,data:r,miniprogram:o},useContent:!0});switch(d=returnRes$3(d),d.code){case 40003:d.msg="touser字段openid为空或者不正确";break;case 40037:d.msg="订阅模板id为空不正确";break;case 43101:d.msg="用户未订阅该消息";break;case 47003:d.msg="模板参数不准确，可能为空或者不满足规则，errmsg会提示具体是哪个字段出错";break;case 41030:d.msg="page路径不正确，需要保证在现网版本小程序中存在"}return d},weixin.h5.templateMessage={},weixin.h5.templateMessage.send=async function(e={}){let{vk:t}=util$e,{touser:n,template_id:a,url:i,miniprogram:r,data:o}=e,s=await weixin.h5.auth.getAccessToken(e);if(!s)return{code:-1,msg:"获取access_token失败，请检查uni-id配置下的h5-weixin配置的appid和appsecret是否正确"};let d=await t.request({url:"https://api.weixin.qq.com/cgi-bin/message/template/send?access_token="+s,method:"POST",data:{touser:n,template_id:a,url:i,miniprogram:r,data:o},useContent:!0});switch(d=returnRes$3(d),d.code){case 40003:d.msg="touser字段openid为空或者不正确";break;case 40037:d.msg="订阅模板id为空不正确";break;case 43101:d.msg="用户未订阅该消息";break;case 47003:d.msg="模板参数不准确，可能为空或者不满足规则，errmsg会提示具体是哪个字段出错";break;case 41030:d.msg="page路径不正确，需要保证在现网版本小程序中存在"}return d},weixin.loginByWeixin=async function(e={},t,n){let a,i,r;e.context?(a=e.data||{},i=e.context,r=e.custom,e.appid&&(a.appid=e.appid),e.appsecret&&(a.appsecret=e.appsecret)):(a=e,i=t,r=n);let o,s,{vk:d,uniID:c,_:u}=util$e,{code:l,platform:p,type:f,appid:g}=a;if(p||(p=d.pubfn.getPlatformForUniId(i),a.platform=p),g&&["mp-weixin","h5-weixin","app-plus"].indexOf(p)>-1){let e;"mp-weixin"===p?e=weixin.auth.getAppidInfo(a):"h5-weixin"===p?e=weixin.h5.auth.getAppidInfo(a):"app-plus"===p&&(e=weixin.app.auth.getAppidInfo(a));let{appid:t,appsecret:n}=e,r=d.pubfn.copyObject(weixin.getConfig());d.pubfn.setData(r,p+".oauth.weixin",{appid:t,appsecret:n}),a.platform=p,s=c.createInstance({context:i,config:r})}s||(s=c);try{delete a.role,o=await s.loginByWeixin(a);try{if(o.uid&&!o.msg&&(o.msg="register"===o.type?"注册成功":"登录成功"),o.uid&&"register"===o.type){let e={},t={last_login_date:Date.now(),last_login_ip:i.CLIENTIP};if(["h5-weixin","app-plus"].indexOf(p)>-1){let t=await d.openapi.weixin.app.auth.getUserInfo({access_token:o.accessToken,openid:o.openid});e={nickname:t.nickname,avatar:t.headimgurl}}["mp-weixin"].indexOf(p)>-1&&(e={nickname:a.nickname,avatar:a.avatar}),d.pubfn.isNotNull(r)&&(e=d.pubfn.objectAssign(e,r)),d.pubfn.isNotNull(t)&&(e=d.pubfn.objectAssign(e,t)),d.pubfn.isNotNull(e)&&(o.userInfo=await d.baseDao.updateAndReturn({dbName:"uni-id-users",whereJson:{_id:o.uid||"___",nickname:u.exists(!1)},dataJson:e}))}}catch(e){console.error("保存用户头像昵称异常：",e)}if(0===o.code)try{let t=d.crypto.aes.encrypt({data:{code:0,sessionKey:o.sessionKey,openid:o.openid,unionid:o.unionid,uid:o.uid,accessToken:o.accessToken,accessTokenExpired:o.accessTokenExpired,refreshToken:o.refreshToken}});o.encryptedKey=t,e.needKey||(delete o.sessionKey,delete o.accessToken,delete o.refreshToken)}catch(e){}}catch(e){console.error("loginByWeixin异常：",e);let t=e.message||"";throw e.message.indexOf("code been used")>-1&&(t="该code已被使用，请重新获取"),e.message.indexOf("invalid code")>-1&&(t="无效code，请重新获取"),new Error("msg:"+t)}return o},weixin.pc={},weixin.pc.auth={},weixin.pc.auth.getAppidInfo=function(e={}){let{appid:t,appsecret:n}=e,{vk:a,config:i}=util$e;const r=weixin.getConfig(),o=(a.pubfn.getData(r,"preferedWebPlatform")||"h5")+".oauth.weixin";let s=a.pubfn.getData(r,o)||{};if(t&&t!==s.appid){if(!n){let e=a.pubfn.getData(i,"vk.oauth.weixin.list")||[],r=a.pubfn.getListItem(e,"appid",t)||{};if(r&&(t=r.appid,n=r.appsecret),a.pubfn.isNullOne(t,n)){throw new Error(`${`在单微信PC网站登录模式下，未找到 uni-config-center/uni-id/config.json 中配置的微信微信PC网站（${o}）的 appid 和 appsecret`}，${`在多微信PC网站登录模式下，未找到appid：${t} 对应的 appsecret 请先在 uni-config-center/vk-unicloud/index.js 中配置微信（vk.oauth.weixin.list）的 appid 和 appsecret`}`)}}}else if(t=s.appid,n=s.appsecret,a.pubfn.isNullOne(t,n))throw new Error(`请先在 uni-config-center/uni-id/config.json 中配置微信PC网站（${o}）的 appid 和 appsecret`);return{appid:t,appsecret:n}},weixin.order={},weixin.order.uploadShippingInfo=async function(e={}){let{vk:t}=util$e,{order_key:n,logistics_type:a,delivery_mode:i,is_all_delivered:r,shipping_list:o,upload_time:s,payer:d}=e;s||(s=t.pubfn.timeFormat(new Date,"yyyy-MM-ddThh:mm:ss.S+08:00",8)),o.forEach(e=>{e.item_desc.length>117&&(e.item_desc=e.item_desc.substring(0,117)+"...")});let{appid:c,appsecret:u}=weixin.auth.getAppidInfo(e);return await weixin.request({appid:c,appsecret:u,method:"POST",url:"wxa/sec/order/upload_shipping_info",data:{order_key:n,logistics_type:a,delivery_mode:i,is_all_delivered:r,shipping_list:o,upload_time:s,payer:d}})},weixin.order.uploadCombinedShippingInfo=async function(e={}){let{vk:t}=util$e,{order_key:n,sub_orders:a,upload_time:i,payer:r}=e;i||(i=t.pubfn.timeFormat(new Date,"yyyy-MM-ddThh:mm:ss.S+08:00",8)),a.forEach(e=>{e.shipping_list.forEach(e=>{e.item_desc.length>117&&(e.item_desc=e.item_desc.substring(0,117)+"...")})});let{appid:o,appsecret:s}=weixin.auth.getAppidInfo(e);return await weixin.request({appid:o,appsecret:s,method:"POST",url:"wxa/sec/order/upload_combined_shipping_info",data:{order_key:n,sub_orders:a,upload_time:i,payer:r}})},weixin.order.getOrder=async function(e={}){let{transaction_id:t,merchant_id:n,sub_merchant_id:a,merchant_trade_no:i}=e,{appid:r,appsecret:o}=weixin.auth.getAppidInfo(e);return await weixin.request({appid:r,appsecret:o,method:"POST",url:"wxa/sec/order/get_order",data:{transaction_id:t,merchant_id:n,sub_merchant_id:a,merchant_trade_no:i}})},weixin.order.getOrderList=async function(e={}){let{pay_time_range:t,order_state:n,openid:a,last_index:i,page_size:r}=e,{appid:o,appsecret:s}=weixin.auth.getAppidInfo(e);return await weixin.request({appid:o,appsecret:s,method:"POST",url:"wxa/sec/order/get_order_list",data:{pay_time_range:t,order_state:n,openid:a,last_index:i,page_size:r}})},weixin.order.notifyConfirmReceive=async function(e={}){let{transaction_id:t,merchant_id:n,sub_merchant_id:a,merchant_trade_no:i,received_time:r}=e,{appid:o,appsecret:s}=weixin.auth.getAppidInfo(e);return await weixin.request({appid:o,appsecret:s,method:"POST",url:"wxa/sec/order/notify_confirm_receive",data:{transaction_id:t,merchant_id:n,sub_merchant_id:a,merchant_trade_no:i,received_time:r}})},weixin.order.setMsgJumpPath=async function(e={}){let{path:t}=e,{appid:n,appsecret:a}=weixin.auth.getAppidInfo(e);return await weixin.request({appid:n,appsecret:a,method:"POST",url:"wxa/sec/order/set_msg_jump_path",data:{path:t}})},weixin.order.isTradeManaged=async function(e={}){let{appid:t,appsecret:n}=weixin.auth.getAppidInfo(e);return await weixin.request({appid:t,appsecret:n,method:"POST",url:"wxa/sec/order/is_trade_managed",data:{appid:t}})},weixin.order.isTradeManagementConfirmationCompleted=async function(e={}){let{appid:t,appsecret:n}=weixin.auth.getAppidInfo(e);return await weixin.request({appid:t,appsecret:n,method:"POST",url:"wxa/sec/order/is_trade_management_confirmation_completed",data:{appid:t}})},weixin.order.opspecialorder=async function(e={}){let{order_id:t,type:n,delay_to:a}=e,{appid:i,appsecret:r}=weixin.auth.getAppidInfo(e);return await weixin.request({appid:i,appsecret:r,method:"POST",url:"wxa/sec/order/opspecialorder",data:{order_id:t,type:n,delay_to:a}})},weixin.logistics={},weixin.logistics.getAllDelivery=async function(e={}){let{appid:t,appsecret:n}=weixin.auth.getAppidInfo(e);return await weixin.request({appid:t,appsecret:n,method:"GET",url:"cgi-bin/express/business/delivery/getall"})},weixin.logistics.traceWaybill=async function(e={}){let{openid:t,sender_phone:n,receiver_phone:a,delivery_id:i,waybill_id:r,goods_info:o,trans_id:s,order_detail_path:d}=e,{appid:c,appsecret:u}=weixin.auth.getAppidInfo(e);return await weixin.request({appid:c,appsecret:u,method:"POST",header:{"content-type":"application/json"},url:"cgi-bin/express/delivery/open_msg/trace_waybill",data:{openid:t,sender_phone:n,receiver_phone:a,delivery_id:i,waybill_id:r,goods_info:o,trans_id:s,order_detail_path:d}})},weixin.logistics.queryFollowTrace=async function(e={}){let{waybill_token:t}=e,{appid:n,appsecret:a}=weixin.auth.getAppidInfo(e);return await weixin.request({appid:n,appsecret:a,method:"POST",header:{"content-type":"application/json"},url:"cgi-bin/express/delivery/open_msg/query_follow_trace",data:{waybill_token:t}})},weixin.logistics.updateFollowWaybillGoods=async function(e={}){let{waybill_token:t,goods_info:n}=e,{appid:a,appsecret:i}=weixin.auth.getAppidInfo(e);return await weixin.request({appid:a,appsecret:i,method:"POST",header:{"content-type":"application/json"},url:"cgi-bin/express/delivery/open_msg/update_follow_waybill_goods",data:{waybill_token:t,goods_info:n}})},weixin.logistics.getDeliveryList=async function(e={}){let{appid:t,appsecret:n}=weixin.auth.getAppidInfo(e);return await weixin.request({appid:t,appsecret:n,method:"POST",header:{"content-type":"application/json"},url:"cgi-bin/express/delivery/open_msg/get_delivery_list",data:{}})};var weixin_1=weixin;let huawei={},util$f={};const mpConfigKey="mp-harmony.oauth.huawei";async function code2SessionRequest(e={}){let{vk:t}=util$f,{clientId:n,clientSecret:a}=huawei.auth.getConfigInfo(e),i=await huawei.auth.getUserAccessToken(e),r=await huawei.request({url:"https://account.cloud.huawei.com/rest.php?nsp_svc=GOpen.User.getInfo",method:"POST",data:{access_token:i,getNickName:1}});if(r.code)return r;let o=t.pubfn.snake2camelJson(r);return{openid:o.openID,unionid:o.unionID,accessToken:i,clientId:n,clientSecret:a,nickName:o.displayName,avatar:o.headPictureURL,mobile:o.mobileNumber,code:0,msg:"ok"}}function returnRes$4(e){return e.code=e.code||e.error||0,e.msg=e.msg||e.error_description||e.error||"",isNaN(e.code)||(e.code=Number(e.code)),e}huawei.init=function(e){util$f=e},huawei.getConfig=function(){let{vk:e,config:t}=util$f;return e.pubfn.getUniIdConfig(t)},huawei.request=async function(e={}){let{vk:t}=util$f,n=await t.request({contentType:"json",headers:{"Content-Type":"application/x-www-form-urlencoded"},...e});if(n=returnRes$4(n),n.code)throw n;return n},huawei.decrypt={},huawei.decrypt.getPhoneNumber=async function(e={}){let{clientId:t,clientSecret:n}=huawei.auth.getConfigInfo(e),{code:a,access_token:i}=e;i||(i=await huawei.auth.getUserAccessToken(e));let r=await huawei.request({url:"https://account.cloud.huawei.com/rest.php?nsp_svc=GOpen.User.getInfo",method:"POST",data:{access_token:i}});return r.code?r:{mobile:r.mobileNumber,phone:r.mobileNumber,openid:r.openID,unionid:r.unionID,clientId:t,code:0,msg:"ok"}},huawei.auth={},huawei.auth.getConfigInfo=function(e={}){let{vk:t,config:n}=util$f,{platform:a,context:i}=e;return a||(i||(i=t.pubfn.getUniCloudContext()),a=t.pubfn.getPlatformForUniId(i)),e.platform=a,0==a.indexOf("app")&&(a="app-harmony"),"mp-harmony"===a?huawei.auth.getMpConfigInfo(e):huawei.auth.getAppConfigInfo(e)},huawei.auth.getMpConfigInfo=function(e={}){let{clientId:t,clientSecret:n,service:a,platform:i}=e,{vk:r,config:o}=util$f;const s=huawei.getConfig();let d=r.pubfn.getData(s,mpConfigKey)||{};if(t&&t!==d.clientId){if(!n){let e=r.pubfn.getData(o,"vk.oauth.huawei.list")||[],i=r.pubfn.getListItem(e,"clientId",t)||{};if(i.clientId&&(t=i.clientId,n=i.clientSecret,a=i.service),r.pubfn.isNullOne(t,n)){throw new Error(`${`在单小程序登录模式下，未找到 uni-config-center/uni-id/config.json 中配置的华为元服务（${mpConfigKey}）的 clientId 和 clientSecret`}，${`在多小程序登录模式下，未找到clientId：${t} 对应的 clientSecret 请先在 uni-config-center/vk-unicloud/index.js 中配置华为元服务（vk.oauth.huawei.list）的 clientId 和 clientSecret`}`)}}}else if(t=d.clientId,n=d.clientSecret,a=d.service,r.pubfn.isNullOne(t,n))throw new Error(`请先在 uni-config-center/uni-id/config.json 中配置华为元服务（${mpConfigKey}）的 clientId 和 clientSecret`);return{clientId:t,clientSecret:n,service:a,platform:i}},huawei.auth.getAppConfigInfo=function(e={}){let{clientId:t,clientSecret:n,service:a,platform:i}=e,{vk:r,config:o}=util$f;const s=huawei.getConfig(),d=s.preferedAppPlatform+".oauth.huawei";let c=r.pubfn.getData(s,d)||{};if(t&&t!==c.clientId){if(!n){let e=r.pubfn.getData(o,"vk.oauth.huawei.list")||[],i=r.pubfn.getListItem(e,"clientId",t)||{};if(i.clientId&&(t=i.clientId,n=i.clientSecret,a=i.service),r.pubfn.isNullOne(t,n)){throw new Error(`${`在单App登录模式下，未找到 uni-config-center/uni-id/config.json 中配置的华为元服务（${d}）的 clientId 和 clientSecret`}，${`在多App登录模式下，未找到clientId：${t} 对应的 clientSecret 请先在 uni-config-center/vk-unicloud/index.js 中配置华为元服务（vk.oauth.huawei.list）的 clientId 和 clientSecret`}`)}}}else if(t=c.clientId,n=c.clientSecret,a=c.service,r.pubfn.isNullOne(t,n))throw new Error(`请先在 uni-config-center/uni-id/config.json 中配置华为App（${d}）的 clientId 和 clientSecret`);return{clientId:t,clientSecret:n,service:a,platform:i}},huawei.auth.getUserAccessToken=async function(e={}){if(e.access_token)return e.access_token;let{clientId:t,clientSecret:n}=huawei.auth.getConfigInfo(e),{code:a}=e,i=await huawei.request({url:"https://oauth-login.cloud.huawei.com/oauth2/v3/token",method:"POST",data:{grant_type:"authorization_code",client_id:t,client_secret:n,code:a}});if(i.access_token)return i.access_token;throw console.error("getUserAccessToken失败：",i),{code:i.error,msg:i.error_description,err:i}},huawei.auth.code2Session=async function(e={}){let t,{vk:n}=util$f,{platform:a,context:i,needKey:r=!1,encryptedKey:o}=e;if(a||(a=i.PLATFORM),n.pubfn.isNotNull(o))t=n.crypto.aes.decrypt({data:o}),t.encryptedKey=o;else{t=await code2SessionRequest(e);try{let e=n.crypto.aes.encrypt({data:t});t.encryptedKey=e}catch(e){}}return r||(delete t.sessionKey,delete t.accessToken,delete t.refreshToken),delete t.clientSecret,t.platform=a,t},huawei.subscribeMessage={},huawei.subscribeMessage.send=async function(e={}){let{vk:t}=util$f,{touser:n,template_id:a,page:i,data:r}=e,{service:o,platform:s}=huawei.auth.getConfigInfo(e),d="mp-harmony"===s?"华为元服务":"华为App";if(!o)throw new Error(`uni-id配置内的 ${d}中的 service 不能为空`);let c,u=t.pubfn.isNullOneByObject({appid:o.appid,projectId:o.projectId,keyId:o.keyId,privateKey:o.privateKey,subAccount:o.subAccount});if(u)throw new Error(`uni-id配置内的 ${d}中的 service.${u} 不能为空`);if(i){c={actionType:1};let e=i.split("?")[0];c.uri=e;let n=i.split("?")[1];n&&(c.data=t.pubfn.queryStringToJson(n))}let l=Math.floor(Date.now()/1e3),p={header:{kid:o.keyId,alg:"PS256",typ:"JWT"},payload:{aud:"https://oauth-login.cloud.huawei.com/oauth2/v3/token",iss:o.subAccount,iat:l,exp:l+3600}},f=`${string2Base64(JSON.stringify(p.header),"binary")}.${string2Base64(JSON.stringify(p.payload),"utf-8")}`,g=`${f}.${signUsePS256(f,o.privateKey)}`,m={msgId:t.pubfn.createOrderNo("msg",32),toOpenId:n,templateId:a,appId:o.appid,templateParams:r,clickAction:c},b=await t.request({url:`https://push-api.cloud.huawei.com/v1/${o.projectId}/service_notification/send`,method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer "+g},data:m,contentType:"json"});switch(b=returnRes$4(b),b.message&&b.message.indexOf("Parameter is not valid")>-1&&(b.code||(b.code=-1),b.msg="参数不合法"),b.code){case ********:b.msg="touser字段openid为空或者不正确";break;case ********:b.msg="订阅模板id为空或不正确";break;case ********:b.msg="可能原因：1.下发超过订阅次数 2.请求消息体超过默认大小 3.用户未订阅该消息 4.未开通推送服务";break;case ********:b.msg="模板参数不准确，可能为空或者不满足规则，errmsg会提示具体是哪个字段出错"}return b};var huawei_1=huawei;function encodeBase64(e){return e.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function string2Base64(e,t){return encodeBase64(Buffer.from(e,t).toString("base64"))}function signUsePS256(e,t){let{vk:n}=util$f;const{crypto:a}=n.getUnicloud(),i=a.createSign("RSA-SHA256");return encodeBase64((i.update(e),i.sign({key:t,padding:a.constants.RSA_PKCS1_PSS_PADDING,saltLength:a.constants.RSA_PSS_SALTLEN_DIGEST},"base64")))}let openapi={};openapi.alipay=alipay_1,openapi.baidu=baidu_1,openapi.douyin=douyin_1,openapi.qq=qq_1,openapi.weixin=weixin_1,openapi.huawei=huawei_1,openapi.init=function(e){openapi.alipay.init(e),openapi.baidu.init(e),openapi.douyin.init(e),openapi.qq.init(e),openapi.weixin.init(e),openapi.huawei.init(e)};var openapi_1=openapi,builder=class{constructor(){this._shouldUseCache=!1,this._cachedBuffer=null,this._lineBreak="\r\n",this._boundary="------FormDataBaseBoundary"+Math.random().toString(36).substring(2),this.dataList=[]}_addData(e){if(this._shouldUseCache=!1,0===this.dataList.length)return void this.dataList.push(e);const t=this.dataList[this.dataList.length-1];switch(`${Buffer.isBuffer(t)?"buffer":"other"}_${Buffer.isBuffer(e)?"buffer":"other"}`){case"buffer_buffer":this.dataList.push(this._lineBreak),this.dataList.push(e);break;case"buffer_other":this.dataList.push(this._lineBreak+e);break;case"other_buffer":this.dataList[this.dataList.length-1]=t+"\r\n",this.dataList.push(e);break;case"other_other":this.dataList[this.dataList.length-1]=t+"\r\n"+e}}append(e,t,n){this._addData("--"+this._boundary);let a=`Content-Disposition: form-data; name="${encodeURIComponent(e)}"`;if(Buffer.isBuffer(t)){if(!n.filename||!n.contentType)throw new Error("filename and contentType required");a+=`; filename="${encodeURIComponent(n.filename)}"`,this._addData(a),this._addData("Content-Type: "+n.contentType),this._addData(""),this._addData(t)}else this._addData(a),this._addData(""),this._addData(t)}getHeaders(e){const t={"Content-Type":"multipart/form-data; boundary="+this._boundary};return Object.assign(t,e)}getBuffer(){if(this._shouldUseCache)return this._cachedBuffer;this._shouldUseCache=!0;let e=Buffer.alloc(0);return this.dataList.forEach(t=>{e=Buffer.isBuffer(t)?Buffer.concat([e,t]):Buffer.concat([e,Buffer.from(""+t)])}),e=Buffer.concat([e,Buffer.from(`${this._lineBreak}--${this._boundary}--`)]),this._cachedBuffer=e,e}};const BOUNDARY_REG=/^multipart\/.+?(?:;\s*boundary=(?:(?:"(.+)")|(?:([^\s]+))))$/i,LEADING_REG=/Content-Disposition:\sform-data;\sname="(.+?)"(?:;\sfilename="(.+?)")?/i,TYPE_REG=/Content-Type:\s(.+?)$/i,lineBreak="\r\n";function split(e,t){let n=0,a=0,i=[];for(;-1!==(a=e.indexOf(t,n));)i.push(e.slice(n,a)),n=a+t.length,a=e.indexOf(t,n);return i}function readParam(e){let t=e.indexOf("\r\n")+"\r\n".length,n=t,a=e.lastIndexOf("\r\n"),i=[];for(;-1!==(n=e.indexOf("\r\n",t));)if(i.push(e.slice(t,n)),t=n+"\r\n".length,0===i[i.length-1].length){i.push(e.slice(t,a));break}return i}var parser=e=>{const t=(e.headers["content-type"]||e.headers["Content-Type"]).match(BOUNDARY_REG),n=t[1]||t[2],a=split(Buffer.from(e.body,"base64"),Buffer.from("--"+n)).map(e=>readParam(e).filter(e=>e.length>0)).filter(e=>2===e.length||3===e.length||4===e.length).map(e=>{const t={},n=e[0].toString().match(LEADING_REG);switch(t.name=decodeURIComponent(n[1]),e.length){case 2:t.value=e[1].toString();break;case 3:t.filename=decodeURIComponent(n[2]),t.contentType=e[1].toString().match(TYPE_REG)[1],t.fileContent=e[2];break;case 4:t.filename=decodeURIComponent(n[2]),t.contentType=e[1].toString().match(TYPE_REG)[1],t.fileContent=e[3]}return t}),i={};return a.forEach(e=>{const t=e.name;delete e.name,i[t]=e.fileContent?e:e.value}),i},formDataUtils={FormData:builder,formParser:parser};const dbName_role="uni-id-roles",dbName_menu="opendb-admin-menus",dbName_permission="uni-id-permissions",DB_MAX_LIMIT$1=1e3;let dao={},util$g={};dao.init=function(e){util$g=e},dao.findRoleById=async(e="___")=>{let t,{vk:n,db:a,_:i}=util$g;return t=await n.baseDao.findByWhereJson({dbName:dbName_role,whereJson:{role_id:e}}),t},dao.listPermissionByRoleIds=async e=>{let t,{vk:n,db:a,_:i}=util$g;if(!Array.isArray(e))return[];if(0===e.length)return[];if(e.includes("admin"))return[];let r=await n.baseDao.select({dbName:dbName_role,getMain:!0,getCount:!1,pageIndex:1,pageSize:DB_MAX_LIMIT$1,whereJson:{role_id:i.in(e)}});const o=[];return r.forEach(e=>{Array.prototype.push.apply(o,e.permission)}),t=Array.from(new Set(o)),t},dao.roleBindPermission=async(e={})=>{let{vk:t,db:n,_:a}=util$g,i={code:0,msg:""},{role_id:r="___",permissionList:o=[],reset:s=!1}=e;if(!s){let e=await dao.findRoleById(r),{permission:t=[]}=e;o=t.concat(o),o=[...new Set(o)]}return i.num=await t.baseDao.update({dbName:dbName_role,whereJson:{role_id:r},dataJson:{permission:a.set(o)}}),i},dao.roleBindMenu=async(e={})=>{let{vk:t,db:n,_:a}=util$g,i={code:0,msg:""},{role_id:r="___",menuList:o=[],reset:s=!1,addPermission:d=!1}=e,c=[],u=await dao.findRoleById(r),{menu:l=[],permission:p=[]}=u;if(s?c=compareArray(o,l):(o=l.concat(o),o=[...new Set(o)]),i.num=await t.baseDao.update({dbName:dbName_role,whereJson:{role_id:r},dataJson:{menu:a.set(o)}}),d){let e=await dao.findMenuByIdsToPermission(o),n=[];if(s&&t.pubfn.isNotNull(c)){n=compareArray(e,await dao.findMenuByIdsToPermission(c))}p=p.concat(e),p=compareArray(n,p),p=[...new Set(p)],dao.roleBindPermission({role_id:r,permissionList:p,reset:!0})}return i},dao.findPermissionById=async(e="___")=>{let t,{vk:n,db:a,_:i}=util$g;return t=await n.baseDao.findByWhereJson({dbName:dbName_permission,whereJson:{permission_id:e}}),t},dao.findMenuByIdsToPermission=async e=>{let{vk:t,db:n,_:a}=util$g,i=await dao.findMenuByIds(e);if(t.pubfn.isNull(i))return[];let r=[];for(let e in i){let n=i[e].permission;t.pubfn.isNotNull(n)&&(r=r.concat(n))}return r=[...new Set(r)],r},dao.listPermissionToTree=async(e={})=>{let t,{vk:n,db:a,_:i}=util$g,{getCount:r=!1,pageSize:o=DB_MAX_LIMIT$1,pageIndex:s=1,whereJson:d={parent_id:i.in([null,""])},sortArr:c=[{name:"sort",type:"asc"}],treeProps:u={}}=e,{level:l=3,limit:p=DB_MAX_LIMIT$1,whereJson:f}=u;d&&!d.permission_id&&(d.permission_id=i.exists(!0)),t=await n.baseDao.selects({dbName:dbName_permission,pageIndex:s,pageSize:o,getCount:r,whereJson:d,sortArr:c,treeProps:{id:"permission_id",parent_id:"parent_id",children:"children",level:l,limit:p,whereJson:f,sortArr:c}});let g={id:"permission_id",parent_id:"parent_id",children:"children"},m=t.rows;m=n.pubfn.treeToArray(m,g),t.list=n.pubfn.copyObject(m);for(let e in m){let t=m[e],a="",i="";if(n.pubfn.isNotNull(t.level)){a=` - ${["未分类","子弹级","炸弹级","榴弹级","核弹级"][t.level]}（LV：${t.level}）`}if(n.pubfn.isNotNull(t.curd_category)){i=" - "+["未分类","增","删","改","查","特殊"][t.curd_category]}m[e].label=`${t.permission_name}（${t.permission_id}）${i}${a}`}return m=n.pubfn.arrayToTree(m,g),t.rows=m,t},dao.findMenuById=async(e="___")=>{let t,{vk:n,db:a,_:i}=util$g;return t=await n.baseDao.findByWhereJson({dbName:dbName_menu,whereJson:{menu_id:e}}),t},dao.findMenuByIds=async e=>{let t,{vk:n,db:a,_:i}=util$g;return n.pubfn.isNull(e)?[]:(t=(await n.baseDao.select({dbName:dbName_menu,pageIndex:1,pageSize:DB_MAX_LIMIT$1,whereJson:{menu_id:i.in(e)}})).rows,t)},dao.listMenuByRole=async(e={})=>{let{vk:t,db:n,_:a}=util$g,i={code:0,msg:"",menus:[],menuList:[]},{role:r,treeProps:o={}}=e,s=[],d={enable:!0},c={enable:!0};if(!(r.indexOf("admin")>-1)){if(t.pubfn.isNull(r))return i;let e=await t.baseDao.select({dbName:dbName_role,pageSize:DB_MAX_LIMIT$1,whereJson:{role_id:a.in(r),enable:!0},fieldJson:{menu:!0}});for(let n in e.rows){let{menu:a}=e.rows[n];t.pubfn.isNotNull(a)&&(s=s.concat(a))}if(0==s.length)return i;s=[...new Set(s)],d.menu_id=a.in(s),c.menu_id=a.in(s)}d.parent_id=a.in([null,""]);let u=[{name:"sort",type:"asc"}],{level:l=3,limit:p=DB_MAX_LIMIT$1}=o,f=await t.baseDao.selects({dbName:dbName_menu,pageIndex:1,pageSize:DB_MAX_LIMIT$1,whereJson:d,sortArr:u,treeProps:{id:"menu_id",parent_id:"parent_id",children:"children",level:l,limit:p,whereJson:c,sortArr:u}});return i.menus=f.rows,i.menuList=t.pubfn.treeToArray(f.rows,{id:"menu_id",parent_id:"parent_id",children:"children"}),i},dao.menuBindPermission=async(e={})=>{let{vk:t,db:n,_:a}=util$g,i={code:0,msg:""},{menu_id:r="___",permissionList:o=[],reset:s=!1}=e;if(!s){let e=await dao.findMenuById(r),{permission:t=[]}=e;o=t.concat(o),o=[...new Set(o)]}return i.num=await t.baseDao.update({dbName:dbName_menu,whereJson:{menu_id:r},dataJson:{permission:a.set(o)}}),i},dao.listMenuToTree=async(e={})=>{let t,{vk:n,db:a,_:i}=util$g,{getCount:r=!1,pageSize:o=DB_MAX_LIMIT$1,pageIndex:s=1,whereJson:d={parent_id:i.in([null,""]),menu_id:i.exists(!0)},sortArr:c=[{name:"sort",type:"asc"}],treeProps:u={}}=e,{level:l=3,limit:p=DB_MAX_LIMIT$1,whereJson:f}=u;d&&!d.menu_id&&(d.menu_id=i.exists(!0)),t=await n.baseDao.selects({dbName:dbName_menu,pageIndex:s,pageSize:o,getCount:r,whereJson:d,sortArr:c,treeProps:{id:"menu_id",parent_id:"parent_id",children:"children",level:l,limit:p,whereJson:f,sortArr:c}});let g={id:"menu_id",parent_id:"parent_id",children:"children"},m=t.rows;m=n.pubfn.treeToArray(m,g),t.list=n.pubfn.copyObject(m);for(let e in m){let t=m[e];m[e].label=`${t.name}（${t.menu_id}）`}return m=n.pubfn.arrayToTree(m,g),t.rows=m,t};var sysDao=dao;function compareArray(e,t){let n=new Set(e);return t.filter(e=>!n.has(e))}const dbName="vk-global-data";let dao$1={},util$h={};dao$1.init=function(e){util$h=e},dao$1.find=async e=>{let{vk:t,db:n,_:a}=util$h,i={};return i=await t.baseDao.findById({dbName:dbName,id:e}),i},dao$1.del=async e=>{let{vk:t,db:n,_:a}=util$h,i={};return i=await t.baseDao.deleteById({dbName:dbName,id:e}),i},dao$1.deleteByWhere=async e=>{let{vk:t,db:n,_:a}=util$h,i={};return i=await t.baseDao.del({dbName:dbName,whereJson:e}),i},dao$1.deleteExpired=async e=>{let{vk:t,db:n,_:a}=util$h,i={},r={};"string"==typeof e?r._id=e:"object"==typeof e&&(r=e);let o=(new Date).getTime();return i=await t.baseDao.del({dbName:dbName,whereJson:{...r,expired_at:a.gt(0).lte(o)}}),i},dao$1.update=async e=>{let{vk:t,db:n,_:a}=util$h,i={},{key:r,value:o,comment:s,expired_at:d}=e;return i=await t.baseDao.updateById({dbName:dbName,id:r,dataJson:{value:a.set(o),comment:s,expired_at:d}}),i},dao$1.add=async e=>{let{vk:t,db:n,_:a}=util$h,i={},{key:r,value:o,comment:s,expired_at:d}=e;return i=await t.baseDao.add({dbName:dbName,dataJson:{_id:r,key:r,value:o,comment:s,expired_at:d}}),i},dao$1.count=async e=>{let{vk:t,db:n,_:a}=util$h,i={};return i=await t.baseDao.count({dbName:dbName,whereJson:e}),i},dao$1.set=async e=>{let{vk:t,db:n,_:a}=util$h,i={code:0,msg:"ok"},r=new Date;e._add_time=r.getTime(),e._add_time_str=t.pubfn.timeFormat(r,"yyyy-MM-dd hh:mm:ss");let o=await n.collection(dbName).doc(e.key).set(e);return o.updated?(i.mode="update",i.updated=o.updated):(i.id=o.upsertedId||e.key,i.mode="add"),i.num=1,i},dao$1.inc=async e=>{let{vk:t,db:n,_:a}=util$h,{key:i,value:r,expired_at:o}=e,s={},d=await t.baseDao.updateById({dbName:dbName,id:i,dataJson:{value:a.inc(r),expired_at:o}});if(0==d){0===await dao$1.count({_id:i})&&(s.id=await dao$1.add(e),s.num=1,s.mode="add")}else s.num=d,s.mode="update";return s},dao$1.list=async e=>{let{vk:t,db:n,_:a}=util$h,i={};return i=await t.baseDao.select({...e,dbName:dbName}),i},dao$1.updateById=async(e,t)=>{let{vk:n,db:a,_:i}=util$h,r={};return r=await n.baseDao.updateById({dbName:dbName,id:e,dataJson:t}),r},dao$1.delByValue=async(e,t)=>{let{vk:n,db:a,_:i}=util$h,r={};return r=await n.baseDao.del({dbName:dbName,whereJson:{_id:e,value:t}}),r};var globalDataDao$1=dao$1;const dbName$1={openData:"opendb-open-data"},db=uniCloud.database(),_=db.command;let dao$2={get:async e=>{let t,n=await db.collection(dbName$1.openData).doc(e).get(),a=n.data&&n.data.length>0?n.data[0]:null;if(!a)return null;if(a.expired>0&&Date.now()>a.expired)return null;try{t=JSON.parse(a.value)}catch(e){t=a.value}return t},set:async(e,t,n=0)=>{n>0&&(n=Date.now()+1e3*n);let a=await db.collection(dbName$1.openData).doc(e).set({value:JSON.stringify(t),expired:n});return a.id?a.id:null},getAccessToken:async(e={})=>{let{appId:t,platform:n}=e,a=`uni-id:${n}:${t}:access-token`;return await dao$2.get(a)},setAccessToken:async(e,t,n)=>{let{appId:a,platform:i}=e,r=`uni-id:${i}:${a}:access-token`;return await dao$2.set(r,t,n)},getSessionKey:async(e={})=>{let{appId:t,platform:n,openid:a}=e,i=`uni-id:${n}:${t}:${a}:session-key`;return await dao$2.get(i)},setSessionKey:async(e={},t,n)=>{let{appId:a,platform:i,openid:r}=e,o=`uni-id:${i}:${a}:${r}:session-key`;return await dao$2.set(o,t,n)}};var opendbOpenDataDao=dao$2;const dbName$2="vk-ws-connection";let dao$3={},util$i={};dao$3.init=function(e){util$i=e},dao$3.findById=async(e,t)=>{let{vk:n,_:a}=util$i,i={};return i="object"==typeof e?await n.baseDao.findById({...e,dbName:dbName$2}):await n.baseDao.findById({dbName:dbName$2,id:e,fieldJson:t}),i},dao$3.findByWhereJson=async(e,t)=>{let{vk:n,db:a,_:i}=util$i,r={};return r=await n.baseDao.findByWhereJson({dbName:dbName$2,whereJson:e,fieldJson:t}),r},dao$3.add=async e=>{let{vk:t,_:n}=util$i,a={};return a=e.db&&e.dataJson?await t.baseDao.add({...e,cancelAddTimeStr:!0,dbName:dbName$2}):await t.baseDao.add({dbName:dbName$2,cancelAddTimeStr:!0,dataJson:e}),a},dao$3.adds=async e=>{let{vk:t,db:n,_:a}=util$i,i={};return i=await t.baseDao.adds({dbName:dbName$2,dataJson:e}),i},dao$3.del=async e=>{let{vk:t,db:n,_:a}=util$i,i={};return i=await t.baseDao.del({dbName:dbName$2,whereJson:e}),i},dao$3.deleteById=async(e,t)=>{let{vk:n,_:a}=util$i,i={};return i=await n.baseDao.deleteById({db:t,dbName:dbName$2,id:e}),i},dao$3.update=async(e={})=>{let{vk:t,db:n,_:a}=util$i,i={};return i=await t.baseDao.update({...e,dbName:dbName$2}),i},dao$3.updateById=async(e={})=>{let{vk:t,db:n,_:a}=util$i,i={};return i=await t.baseDao.updateById({...e,dbName:dbName$2}),i},dao$3.updateAndReturn=async(e={})=>{let{vk:t,db:n,_:a}=util$i,i={};return i=await t.baseDao.updateAndReturn({...e,dbName:dbName$2}),i},dao$3.setById=async(e={})=>{let{vk:t,db:n,_:a}=util$i,i={};return i=await t.baseDao.setById({...e,dbName:dbName$2}),i},dao$3.count=async e=>{let{vk:t,db:n,_:a}=util$i,i={};return i=await t.baseDao.count({dbName:dbName$2,whereJson:e}),i},dao$3.sum=async e=>{let{vk:t,db:n,_:a}=util$i,i={};return i=await t.baseDao.sum({...e,dbName:dbName$2}),i},dao$3.max=async e=>{let{vk:t,db:n,_:a}=util$i,i={};return i=await t.baseDao.max({...e,dbName:dbName$2}),i},dao$3.min=async e=>{let{vk:t,db:n,_:a}=util$i,i={};return i=await t.baseDao.min({...e,dbName:dbName$2}),i},dao$3.avg=async e=>{let{vk:t,db:n,_:a}=util$i,i={};return i=await t.baseDao.avg({...e,dbName:dbName$2}),i},dao$3.select=async(e={})=>{let{vk:t,db:n,_:a}=util$i,i={};return i=await t.baseDao.select({...e,dbName:dbName$2}),i},dao$3.selects=async(e={})=>{let{vk:t,db:n,_:a}=util$i,i={};return i=await t.baseDao.selects({...e,dbName:dbName$2}),i},dao$3.getTableData=async(e={})=>{let{vk:t,db:n,_:a}=util$i,i={};return i=await t.baseDao.getTableData({...e,dbName:dbName$2}),i},dao$3.listConnection=async(e={})=>{let{vk:t,db:n,_:a}=util$i,i={},{url:r,user_id:o,device_id:s,appid:d,channel:c}=e,u={},l=Object.keys(e);for(let n=0;n<l.length;n++){let i=l[n],r=e[i];t.pubfn.isArray(r)?u[i]=a.in(r):u[i]=r}return u.url=r,i=await dao$3.select({getMain:!0,getCount:!1,pageIndex:1,pageSize:1e3,whereJson:u}),i},dao$3.listCid=async(e={})=>{let{user_id:t,device_id:n,url:a}=e;if(!t&&!a&&!n)return[];let i=[];const r=await dao$3.listConnection(e);return r&&0!==r.length?(i=r.map(e=>e._id),i=Array.from(new Set(i)),i):[]};var vkWsConnectionDao=dao$3;const dbName$3={user:"uni-id-users"};let dao$4={},util$j={};dao$4.init=function(e){util$j=e},dao$4.findById=async(e,t)=>{let{vk:n,_:a}=util$j,i={};return i="object"==typeof e?await n.baseDao.findById({...e,dbName:dbName$3.user}):await n.baseDao.findById({dbName:dbName$3.user,id:e,fieldJson:t}),i},dao$4.findByWhereJson=async(e,t)=>{let{vk:n,db:a,_:i}=util$j,r={};return r=await n.baseDao.findByWhereJson({dbName:dbName$3.user,whereJson:e,fieldJson:t}),r},dao$4.add=async e=>{let{vk:t,_:n}=util$j,a={};return a=e.db&&e.dataJson?await t.baseDao.add({...e,dbName:dbName$3.user}):await t.baseDao.add({dbName:dbName$3.user,dataJson:e}),a},dao$4.adds=async e=>{let{vk:t,db:n,_:a}=util$j,i={};return i=await t.baseDao.adds({dbName:dbName$3.user,dataJson:e}),i},dao$4.del=async e=>{let{vk:t,db:n,_:a}=util$j,i={};return i=await t.baseDao.del({dbName:dbName$3.user,whereJson:e}),i},dao$4.deleteById=async(e,t)=>{let{vk:n,_:a}=util$j,i={};return i=await n.baseDao.deleteById({db:t,dbName:dbName$3.user,id:e}),i},dao$4.update=async(e={})=>{let{vk:t,db:n,_:a}=util$j,i={};return i=await t.baseDao.update({...e,dbName:dbName$3.user}),i},dao$4.updateById=async(e={})=>{let{vk:t,db:n,_:a}=util$j,i={};return i=await t.baseDao.updateById({...e,dbName:dbName$3.user}),i},dao$4.updateAndReturn=async(e={})=>{let{vk:t,db:n,_:a}=util$j,i={};return i=await t.baseDao.updateAndReturn({...e,dbName:dbName$3.user}),i},dao$4.setById=async(e={})=>{let{vk:t,db:n,_:a}=util$j,i={};return i=await t.baseDao.setById({...e,dbName:dbName$3.user}),i},dao$4.count=async e=>{let{vk:t,db:n,_:a}=util$j,i={};return i=await t.baseDao.count({dbName:dbName$3.user,whereJson:e}),i},dao$4.sum=async e=>{let{vk:t,db:n,_:a}=util$j,i={};return i=await t.baseDao.sum({...e,dbName:dbName$3.user}),i},dao$4.max=async e=>{let{vk:t,db:n,_:a}=util$j,i={};return i=await t.baseDao.max({...e,dbName:dbName$3.user}),i},dao$4.min=async e=>{let{vk:t,db:n,_:a}=util$j,i={};return i=await t.baseDao.min({...e,dbName:dbName$3.user}),i},dao$4.avg=async e=>{let{vk:t,db:n,_:a}=util$j,i={};return i=await t.baseDao.avg({...e,dbName:dbName$3.user}),i},dao$4.select=async(e={})=>{let{vk:t,db:n,_:a}=util$j,i={};return i=await t.baseDao.select({...e,dbName:dbName$3.user}),i},dao$4.selects=async(e={})=>{let{vk:t,db:n,_:a}=util$j,i={};return i=await t.baseDao.selects({...e,dbName:dbName$3.user}),i},dao$4.getTableData=async(e={})=>{let{vk:t,db:n,_:a}=util$j,i={};return i=await t.baseDao.getTableData({...e,dbName:dbName$3.user}),i},dao$4.getTokenById=async e=>{let{vk:t,_:n,uniID:a}=util$j,i=null,{token:r=[],role:o=[]}=await dao$4.findById({id:e,fieldJson:{token:!0,role:!0}});const s=t.getUniIdTokenManage();if(r.length>0)for(let e=r.length-1;e>=0;e--){if(i=r[e],!s.verifyToken(i).code)break;i=null}if(!i){i=(await s.createToken({uid:e,needPermission:!1,needAddDB:!0})).token}return i},dao$4.addTokenById=async(e,t)=>{let{vk:n,db:a,_:i}=util$j,r={};return r=await dao$4.updateById({id:e,dataJson:{token:i.push(t)}}),r};var userDao=dao$4;let util$k={},smsUtil={init:function(e){util$k=e}},aliyun={specialUrlEncode:function(e){return(e=encodeURIComponent(e)).replace(/\+/g,"%20").replace(/\*/g,"%2A").replace(/%7E/g,"~")},sign:function(e,t){let{crypto:n}=util$k;return n.createHmac("sha1",e).update(t).digest("base64")}};smsUtil.sendSms=async function(e){let{vk:t,config:n}=util$k,{provider:a,appid:i,smsKey:r,smsSecret:o,signName:s,phone:d,templateId:c,data:u}=e,l={};if("aliyun"===a){let a=t.pubfn.getData(n,"vk.service.sms.aliyun");t.pubfn.isNotNull(a)&&(r||(e.smsKey=a.accessKeyId),o||(e.smsSecret=a.accessKeySecret),s||(e.signName=a.signName)),l=await smsUtil.sendSmsByAliyun(e)}else{if("unicloud"!==a)return{code:-1,msg:`暂不支持${a}供应商`};{let a=t.pubfn.getUniIdConfig(n,"service.sms");t.pubfn.isNotNull(a)&&(r||(e.smsKey=a.smsKey),o||(e.smsSecret=a.smsSecret),s||(e.signName=a.signName)),l=await smsUtil.sendSmsByUnicloud(e)}}return l.requestParam={provider:a,phone:d},l},smsUtil.sendSmsByAliyun=async function(e){let{vk:t,config:n}=util$k,{provider:a,appid:i,smsKey:r,smsSecret:o,signName:s,phone:d,templateId:c,data:u}=e,l={code:0,msg:""};try{if(t.pubfn.isNullOne(r,o))return{code:-1,msg:"阿里云短信配置错误，请检查vk-unicloud配置下的vk.service.sms.aliyun配置的accessKeyId和accessKeySecret是否正确"};let e="https://dysmsapi.aliyuncs.com",n=t.pubfn.timeFormat(new Date,"yyyy-MM-ddThh:mm:ssZ",0),a=(new Date).getTime().toString().substring(7)+t.pubfn.random(30);"object"==typeof u&&(u=JSON.stringify(u));let i={SignatureMethod:"HMAC-SHA1",SignatureNonce:a,AccessKeyId:r,SignatureVersion:"1.0",Timestamp:n,Format:"json",Action:"SendSms",Version:"2017-05-25",PhoneNumbers:d,SignName:s,TemplateParam:u,TemplateCode:c};delete i.Signature;let p=[];for(let e in i)p.push(e);p.sort();let f=!1,g="";for(let e in p){let t=p[e];g+="&"+aliyun.specialUrlEncode(t)+"="+aliyun.specialUrlEncode(i[t])}g=g.substring(1);let m="GET&"+aliyun.specialUrlEncode("/")+"&"+aliyun.specialUrlEncode(g),b=aliyun.sign(o+"&",m),y=aliyun.specialUrlEncode(b),h="Signature="+y+"&"+g;f&&(console.log("\r\n随机数\r\n"),console.log(i.SignatureNonce),console.log("\r\n=========\r\n"),console.log(i.Timestamp),console.log("\r\n====sortedQueryString====\r\n"),console.log(g),console.log("\r\n=====stringToSign====\r\n"),console.log(m),console.log("\r\n=====sign====\r\n"),console.log(b),console.log("\r\n=====signature====\r\n"),console.log(y),console.log("\r\n=========\r\n"),console.log(e+"/?"+h));t.pubfn.urlStringToJson(h);let w=await t.request({url:`${e}?${h}`,method:"GET"});return l="OK"===w.Code?{code:0,msg:"ok",requestRes:w}:{code:-1,msg:w.Message,requestRes:w},l}catch(e){return{code:-1,msg:"短信发送失败",err:{message:e.message,stack:e.stack}}}},smsUtil.sendSmsByUnicloud=async function(e){let{provider:t,appid:n,smsKey:a,smsSecret:i,signName:r,phone:o,templateId:s,data:d}=e,c={code:0,msg:""};try{let e=await uniCloud.sendSms({smsKey:a,smsSecret:i,phone:o,templateId:s,data:d});c=0==e.code||0==e.errCode?{code:0,msg:"ok",requestRes:e}:{code:-1,msg:e.errMessage||e.errMsg,requestRes:e}}catch(e){return console.log(e),{code:-1,msg:"短信发送失败",err:{message:e.message,stack:e.stack}}}return c},smsUtil.sendSmsVerifyCode=async function(e){let t,{vk:n,config:a,uniID:i}=util$k,{provider:r,phone:o,code:s,type:d,expiresIn:c=180}=e,u={code:0,msg:""};if("unicloud"===r){t=n.pubfn.getUniIdConfig(a,"service.sms.templateId");let e=n.pubfn.getUniIdConfig(a,"service.sms.codeExpiresIn");e&&(c=e);let i=n.pubfn.getUniIdConfig(a,"service.sms.name"),d=Math.ceil(c/60).toString();u=await n.system.smsUtil.sendSms({provider:r,phone:o,templateId:t,data:{code:s,name:i,action:"身份验证",expMinute:d}})}else t=n.pubfn.getData(a,`vk.service.sms.${r}.templateCode.verifyCode`),u=await n.system.smsUtil.sendSms({provider:r,phone:o,templateId:t,data:{code:s}});return 0===u.code&&await i.setVerifyCode({mobile:o,code:s,expiresIn:c,type:d}),u};var smsUtil_1=smsUtil;let system={};system.sysDao=sysDao,system.globalDataDao=globalDataDao$1,system.opendbOpenDataDao=opendbOpenDataDao,system.vkWsConnectionDao=vkWsConnectionDao,system.userDao=userDao,system.smsUtil=smsUtil_1,system.init=function(e){let t=["sysDao","globalDataDao","opendbOpenDataDao","vkWsConnectionDao","userDao","smsUtil"];for(let n in t){let a=t[n];"function"==typeof system[a].init&&system[a].init(e)}};var system_1=system;class ReentrantLock{constructor(e={}){let{id:t,timeout:n=5,debug:a=!1}=e,i=uniCloud.vk;if(i.pubfn.isNull(t))throw new Error("msg:锁的id不能为空");this.key=t,this.timeout=n,this.debug=a,this.password=i.pubfn.random(16),this.log("锁生成成功，解锁密码："+this.password)}log(...e){this.debug&&console.log(this.key+":",...e)}async lock(){let e=uniCloud.vk,t=this.key,n=e.getCacheManage();return new Promise(async(e,a)=>{try{const a=()=>{e(this)};0===(await n.setnx(t,this.password,this.timeout)).code?(this.log("锁未被占用，立即获取锁，并设置锁的密码"+this.password),a()):(this.log("锁已被占用"),e())}catch(e){a(e)}})}async unlock(){let e=uniCloud.vk,t=this.key,n=e.getCacheManage();return new Promise(async(e,a)=>{try{await n.delByValue(t,this.password)>0?(this.log("锁解锁成功"),e({code:0,msg:"解锁成功"})):(this.log("锁解锁失败",this.password),e({code:-1,msg:"解锁失败"}))}catch(e){a(e)}})}async withLock(e){uniCloud.vk.pubfn.isNull(key)&&(this.key=crypto$1.createHash("md5").update(e.toString()).digest("hex")),await this.lock();try{return await e()}finally{await this.unlock()}}}var reentrantLockManage=function(...e){return new ReentrantLock(...e)};class CacheManage{constructor(e={}){let{mode:t="db"}=e;this.cache="redis"===t?new RedisCache(e):new GlobalDataCache(e),this.options={mode:t}}async get(e){return await this.cache.get(e)}async set(e,t,n=0){return await this.cache.set(e,t,n)}async setnx(e,t,n){return await this.cache.setnx(e,t,n)}async del(e){return await this.cache.del(e)}async delByValue(e,t){return await this.cache.delByValue(e,t)}async clear(e){return await this.cache.clear(e)}async count(e){return await this.cache.count(e)}async keys(e){return await this.cache.keys(e)}async exists(e){return await this.cache.exists(e)}async expire(e,t){return await this.cache.expire(e,t)}async ttl(e){return await this.cache.ttl(e)}async pttl(e){return await this.cache.pttl(e)}}class GlobalDataCache{constructor(e={}){this.options=e}async get(e){return await uniCloud.vk.globalDataCache.get(e)}async set(e,t,n=0){let a={code:0,msg:"ok"},i=await uniCloud.vk.globalDataCache.set(e,t,n);return a.code=i.code,a.msg=i.msg,a.mode=i.mode,a.key=e,a}async setnx(e,t,n){let a={code:0,msg:"ok"};return 0!==(await uniCloud.vk.globalDataCache.uniqueAdd(e,t,n)).code&&(a.code=-1,a.msg="already exists"),a.key=e,a}async del(e){return await uniCloud.vk.globalDataCache.del(e)}async delByValue(e,t){return await uniCloud.vk.globalDataCache.delByValue(e,t)}async clear(e){return await uniCloud.vk.globalDataCache.clear(e)}async count(e){return await uniCloud.vk.globalDataCache.count({key:new RegExp("^"+e)})}async keys(e){let{rows:t}=await uniCloud.vk.globalDataCache.list({pageIndex:1,pageSize:-1,fieldJson:{_id:!0,key:!0},whereJson:{key:new RegExp("^"+e)}});return t.map(e=>e._id)}async exists(e){return await uniCloud.vk.globalDataCache.count({key:e})}async expire(e,t){let n=0;return t&&(n=Date.now()+1e3*t),await uniCloud.vk.globalDataCache.updateById(e,{expired_at:n})}async ttl(e){let t=(await uniCloud.vk.globalDataCache.find(e)).expired_at,n=uniCloud.vk.pubfn.toDecimal((t-Date.now())/1e3,0);return n<0?0:n}async pttl(e){let t=(await uniCloud.vk.globalDataCache.find(e)).expired_at,n=uniCloud.vk.pubfn.toDecimal(t-Date.now(),0);return n<0?0:n}}class RedisCache{constructor(e={}){this.options=e}getRedis(){return this.redis||(this.redis=uniCloud.vk.redis()),this.redis}async get(e){let t=this.getRedis(),n=await t.get(e);try{n=JSON.parse(n)}catch(e){}return n=uniCloud.vk.pubfn.string2Number(n),n}async set(e,t,n=0){let a,i=this.getRedis(),r={code:0,msg:"ok"},o=await i.exists(e);return r.mode=o?"update":"add","object"==typeof t&&(t=JSON.stringify(t)),a=n>0?await i.set(e,t,"EX",n):await i.set(e,t),r.key=e,a||(r.code=-1,r.msg="fail"),r}async setnx(e,t,n=0){let a,i=this.getRedis(),r={code:0,msg:"ok"};return"object"==typeof t&&(t=JSON.stringify(t)),a=n>0?await i.set(e,t,"EX",n,"NX"):await i.set(e,t,"NX"),r.key=e,a||(r.code=-1,r.msg="already exists"),r}async del(...e){let t=this.getRedis();return await t.del(...e)}async delByValue(e,t){let n=await this.get(e);return String(n)===String(t)?await this.del(e):0}async clear(e){let t=this.getRedis();if(e){let n="0",a=[];do{const[i,r]=await t.scan(n,"MATCH",e+"*");n=i,a=a.concat(r)}while("0"!==n);return a.length>0?await t.del(a):0}}async count(e){let t=this.getRedis(),n="0",a=0;do{const[i,r]=await t.scan(n,"MATCH",e+"*");n=i,a+=r.length}while("0"!==n);return a}async keys(e){let t=this.getRedis(),n="0",a=[];do{const[i,r]=await t.scan(n,"MATCH",e+"*");n=i,a=a.concat(r)}while("0"!==n);return a}async exists(e){let t=this.getRedis();return await t.exists(e)}async expire(e,t){let n=this.getRedis();return t?await n.expire(e,t):await n.persist(e)}async ttl(e){let t=this.getRedis();return await t.ttl(e)}async pttl(e){let t=this.getRedis();return await t.pttl(e)}}var cacheManage=function(e={}){let{mode:t}=e;return t||(t=uniCloud.vk.getConfig("vk.cacheManage.mode","db")),new CacheManage({mode:t})};class CloudStorageManage{constructor(){}getExtStorageManager(e){return getExtStorageManager(e)}async uploadFile(e={}){uniCloud.vk;return await this._publicHandle(e,{unicloud:async(e={})=>{void 0===e.cloudPathAsRealPath&&(e.cloudPathAsRealPath=!0);let{fileID:t}=await uniCloud.uploadFile(e),n=(await this.getTempFileURL({provider:e.provider,fileList:[t]})).fileList[0].tempFileURL,a=getCloudPath(n);return{provider:e.provider,cloudPath:a,fileID:t,fileURL:n,url:n}},extStorage:async e=>{void 0===e.allowUpdate&&(e.allowUpdate=!0);const t=getExtStorageManager();let n=await t.uploadFile(e);if(e.isPrivate){await t.updateFileStatus({fileID:n.fileID,isPrivate:!0})}return{provider:e.provider,cloudPath:n.cloudPath,fileID:n.fileID,fileURL:n.fileURL,url:n.fileURL,isPrivate:!!e.isPrivate}}})}async getTempFileURL(e={}){const t=uniCloud.vk;return await this._publicHandle(e,{unicloud:async e=>("aliyun"!==t.pubfn.getUniCloudProvider()&&(e.fileList=e.fileList.map(e=>0===e.indexOf("https://")?e.replace("https://","cloud://"):e)),await uniCloud.getTempFileURL(e)),extStorage:async e=>{const t=getExtStorageManager();return await t.getTempFileURL(e)}})}async deleteFile(e={}){uniCloud.vk;return await this._publicHandle(e,{unicloud:uniCloud.deleteFile,extStorage:async e=>{const t=getExtStorageManager();return await t.deleteFile(e)}})}async downloadFile(e={}){const t=uniCloud.vk;return await this._publicHandle(e,{unicloud:async e=>{if("aliyun"===t.pubfn.getUniCloudProvider()){return{fileContent:await t.request({url:e.fileID,method:"GET",dataType:"default",header:{"cache-control":"no-cache"}})}}return await uniCloud.downloadFile(e)},extStorage:async e=>{const t=getExtStorageManager();return await t.downloadFile(e)}})}async updateFileStatus(e={}){uniCloud.vk;return await this._publicHandle(e,{unicloud:async e=>{throw Error("内置存储不支持updateFileStatus")},extStorage:async e=>{const t=getExtStorageManager();return await t.updateFileStatus(e)}})}async _publicHandle(e,t){const n=uniCloud.vk.getConfig("vk.service.cloudStorage.defaultProvider","unicloud");let a=e.provider||n,i=t[a];if(!i)throw Error("不支持provider:"+a);return e.provider=a,await i(e)}}var cloudStorageManage=new CloudStorageManage;function getExtStorageManager(e){const t=uniCloud.vk,n=t.pubfn.isNotNull(e)?e:t.getConfig("vk.service.cloudStorage.extStorage");if(!n||!n.provider){throw Error("未找到扩展存储配置，请查看文档 "+"https://vkdoc.fsq.pub/client/uniCloud/cloudfunctions/uploadFile.html#%E9%BB%98%E8%AE%A4%E4%B8%8A%E4%BC%A0%E8%87%B3%E6%89%A9%E5%B1%95%E5%AD%98%E5%82%A8")}let a={};for(const e in n)n.hasOwnProperty(e)&&t.pubfn.isNotNull(n[e])&&(a[e]=n[e]);return uniCloud.getExtStorageManager(a)}function getCloudPath(e){const t=e.match(/https?:\/\/[^\/]+\/([^?#]+)/);return t&&t[1]?t[1]:null}const login=async(e={})=>{const t=uniCloud.vk,{db:n,_:a,config:i,uniID:r}=t.getUnicloud();let o={code:0,msg:""},{clientInfo:s,type:d,customData:c={},userInfo:u,fieldInfo:l}=e,p=Date.now(),f=s,g=f.APPID||f.appId,m=(t.pubfn.getPlatformForUniId(f),t.pubfn.getUniIdConfig(i)),b="";if(t.pubfn.isNull(u)||u.dcloud_appid&&g&&-1===u.dcloud_appid.indexOf(g)){if(d&&"login"===d)return t.pubfn.isNull(u)?{code:-1,msg:"账号未注册",needRegister:!0}:{code:-1,msg:`账号未在应用【${g}】注册`,needRegister:!0,dcloudAppid:g};{(m.autoSetInviteCode||c.my_invite_code)&&(l.my_invite_code=await t.daoCenter.userDao.getValidInviteCode({my_invite_code:c.my_invite_code}));const e=f.channel||f.scene;let n={appid:f.APPID||f.appId||"",uni_platform:f.uniPlatform||f.PLATFORM||"",os_name:f.osName||f.OS||"",app_name:f.appName||"",app_version:f.appVersion||"",app_version_code:f.appVersionCode||"",channel:e?e+"":"",client_ip:f.CLIENTIP||f.clientIP||""},a=await t.daoCenter.userDao.add({...c,...l,register_env:n,dcloud_appid:[g],register_date:p});u=await t.daoCenter.userDao.findById(a),b="register"}}else b="login";let y=u.role||[],h=u.permission=await t.system.sysDao.listPermissionByRoleIds(y),w=r.createToken({uid:u._id,needPermission:!0,role:y,permission:h});if(isPromise(w)&&(w=await w),!w.token){return{code:-1,msg:("login"===b?"登录失败":"注册失败")+"，请检查uni-id配置是否正确"}}u=await t.daoCenter.userDao.updateAndReturn({whereJson:{_id:u._id},dataJson:{...l,token:a.push(w.token),last_login_date:p,last_login_ip:f.CLIENTIP||f.clientIP||""}});let k="login"===b?"登录成功":"注册成功";return o={code:0,msg:k,errCode:0,errMsg:k,type:b,uid:u._id,needUpdateUserInfo:!0,userInfo:u,token:w.token,tokenExpired:w.tokenExpired,passwordConfirmed:!!u.password,mobileConfirmed:!!u.mobile_confirmed,emailConfirmed:!!u.email_confirmed},o=t.pubfn.objectKeySort(o),o};var vk_login=login;function isPromise(e){return!!e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof e.then&&"function"==typeof e.catch}const WebSocketManage={};async function router$1(e={}){const t=uniCloud.vk;let{name:n,url:a,uniIdToken:i,uid:r,data:o={},clientInfo:s,context:d={},encrypt:c}=e,u={uniIdToken:i,$url:`${a}.${n}`,data:o,vk_context:s},l=s.cid,p=await t.router({event:u,context:d,uid:r,vk:t});return p.code?["onWebsocketConnection","onWebsocketMessage"].indexOf(n)>-1&&await WebSocketManage.sendSysError(l,p,c):["onWebsocketConnection"].indexOf(n)>-1&&await WebSocketManage.send({encrypt:c,cid:l,data:{vkWebSocket:{type:"connect",data:{cid:l}}}}),p}async function onWebsocketConnection(e={}){let{url:t,channel:n,uniIdToken:a,data:i,clientInfo:r,context:o,encrypt:s}=e;uniCloud.vk;const d=Date.now();let c;if(a){const e=verifyToken(a);0===e.code&&(c=e.uid)}await addConnection({_id:r.cid,_add_time:d,appid:r.appid,user_id:c,device_id:r.deviceId,url:t,channel:n}),await router$1({name:"onWebsocketConnection",url:t,uniIdToken:a,data:i,clientInfo:r,context:o,encrypt:s})}async function getCidList(e){const t=uniCloud.vk;let{cid:n,user_id:a,device_id:i,url:r,appid:o,channel:s}=e;if(t.pubfn.isNullAll(n,a,i,r))throw new Error("cid、user_id、device_id、url不能同时为空");let d=[];return d=n?"object"==typeof n?n:[n]:await t.system.vkWsConnectionDao.listCid({user_id:a,device_id:i,url:r,appid:o,channel:s}),d=Array.from(new Set(d)),d}async function getConnectionByCid(e){const t=uniCloud.vk;return await t.system.vkWsConnectionDao.findById(e)}async function addConnection(e){const t=uniCloud.vk;return await t.system.vkWsConnectionDao.setById({dataJson:e})}async function delConnectionByCid(e){const t=uniCloud.vk;return await t.system.vkWsConnectionDao.deleteById(e)}function decryptClientData(e={}){let{data:t,deviceId:n}=e,a={code:0};const i=uniCloud.vk,{config:r}=i.getUnicloud(),o=i.crypto.md5(n);try{t=i.crypto.aes.decrypt({mode:"aes-256-ecb",data:t,key:o});let{timeStamp:e=0}=t;Date.now()-e>=1e3*i.pubfn.getData(r,"vk.clientCrypto.expTime",5)&&(a={code:410,msg:"该请求已失效"})}catch(e){a={code:411,msg:"解密失败，参数不合法"}}return 0!==a.code?console.log("websocket解密失败: ",a):a={code:0,data:t},a}function verifyToken(e){return uniCloud.vk.getUniIdTokenManage().verifyToken(e)}WebSocketManage.onWebsocketConnection=async function(e,t){},WebSocketManage.onWebsocketMessage=async function(e,t){let{connectionId:n,payload:a={}}=e;try{a=JSON.parse(a)}catch(e){}if("object"!=typeof a||!a.data||!a.deviceId)return;let{deviceId:i,data:r={},encrypt:o}=a;if(o){let e=decryptClientData({data:r,deviceId:i});if(0!==e.code)return await WebSocketManage.sendSysError(n,e,o),e;r=e.data}let{url:s,channel:d,uniIdToken:c,data:u={},clientInfo:l={}}=r;if(l.cid=n,u.vkWebSocket&&"connect"===u.vkWebSocket.type)return await onWebsocketConnection({...r,data:u.vkWebSocket.data,context:t,encrypt:o});await router$1({name:"onWebsocketMessage",url:s,uniIdToken:c,data:u,clientInfo:l,context:t})},WebSocketManage.onWebsocketDisConnection=async function(e,t){let{connectionId:n}=e;const a=await getConnectionByCid(n);a&&(await delConnectionByCid(n),await router$1({name:"onWebsocketDisConnection",url:a.url,uid:a.user_id,data:{cid:n},clientInfo:{appid:a.appid,cid:a._id,deviceId:a.device_id},context:t}))},WebSocketManage.onWebsocketError=async function(e,t){let{connectionId:n,errorMessage:a}=e;const i=await getConnectionByCid(n);i&&await router$1({name:"onWebsocketError",url:i.url,uid:i.user_id,data:{cid:n,errMsg:a},clientInfo:{appid:i.appid,cid:i._id,deviceId:i.device_id},context:t})},WebSocketManage.getWebSocketServer=function(){return uniCloud.webSocketServer()},WebSocketManage.send=async function(e={}){const t=uniCloud.vk;let{cid:n,user_id:a,device_id:i,url:r,appid:o,data:s,encrypt:d}=e;if(t.pubfn.isNullAll(n,a,i,r))return{code:-1,msg:"cid、user_id、device_id、url不能同时为空"};const c=Date.now();if(d){const e=t.crypto.md5(c);s={data:t.crypto.aes.encrypt({mode:"aes-256-ecb",data:s,key:e}),timeStamp:c,encrypt:!0}}let u=await getCidList(e);if(u&&u.length>0){const e=WebSocketManage.getWebSocketServer();return await e.send(u,s)}},WebSocketManage.signedURL=async function(e={}){const t=uniCloud.vk;let{name:n,url:a}=e;n||(n=t.pubfn.getUniCloudFunctionName());const i=WebSocketManage.getWebSocketServer();return await i.signedURL(n,{url:a})},WebSocketManage.close=async function(e={}){const t=uniCloud.vk;let{cid:n,user_id:a,url:i,appid:r}=e;if(t.pubfn.isNullAll(n,a))return{code:-1,msg:"cid和user_id不能均为空"};let o=await getCidList(e);const s=WebSocketManage.getWebSocketServer();return await s.close(o)},WebSocketManage.sendSysError=async function(e,t,n){return await WebSocketManage.send({cid:e,encrypt:n,data:{vkWebSocket:{type:"error",data:t}}})},WebSocketManage.forceLogout=async function(e={}){const t=uniCloud.vk;let{cid:n,user_id:a,url:i,appid:r,data:o}=e;if(t.pubfn.isNullAll(n,a))return{code:-1,msg:"cid和user_id不能均为空"};let s=await getCidList(e);WebSocketManage.getWebSocketServer();return await WebSocketManage.send({cid:s,encrypt:!0,data:{vkWebSocket:{type:"forceLogout",data:o}}})};class WebSocket{constructor(e={}){let{url:t,appid:n}=e;this.url=t,this.appid=n}async onWebsocketConnection(e,t){WebSocketManage.onWebsocketConnection(e,t)}async onWebsocketMessage(e,t){WebSocketManage.onWebsocketMessage(e,t)}async onWebsocketDisConnection(e,t){WebSocketManage.onWebsocketDisConnection(e,t)}async onWebsocketError(e,t){WebSocketManage.onWebsocketError(e,t)}_handleParams(e={}){let t=["url","appid","channel"];for(let n=0;n<t.length;n++){let a=t[n];!e[a]&&this[a]&&(e[a]=this[a])}return e}async send(e={}){return this._handleParams(e),await WebSocketManage.send(e)}async signedURL(e={}){return this._handleParams(e),await WebSocketManage.signedURL(e)}async close(e={}){return this._handleParams(e),await WebSocketManage.close(e)}async sendSysError(e,t,n){return await WebSocketManage.sendSysError(e,t,n)}async forceLogout(e={}){return this._handleParams(e),await WebSocketManage.forceLogout(e)}}function getWebSocketManage(e){return new WebSocket(e)}var webSocketManage=getWebSocketManage;function tokenSign(e,t){const n=uniCloud.vk,{crypto:a}=n.getUnicloud(),i=e.split(".");let r,o=Buffer.from(i[0],"base64").toString("utf-8");try{o=JSON.parse(o)}catch(e){}if(!o||!o.alg)return null;if("HS256"===o.alg&&(r="sha256"),!r)return null;return encodeBase64$1(a.createHmac(r,t).update(e).digest("base64"))}function encodeBase64$1(e){return e.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function string2Base64$1(e,t){return encodeBase64$1(Buffer.from(e,t).toString("base64"))}function sign(e,t){const n=uniCloud.vk,{crypto:a}=n.getUnicloud(),i=a.createHmac("sha256",t);return encodeBase64$1((i.update(e),i.digest("base64")))}class UniIdTokenManage{constructor(e={}){}verifyToken(e){const t=uniCloud.vk,{config:n}=t.getUnicloud(),a=e.split(".");if(3!==a.length)return{code:30204,err:{name:"JsonWebTokenError",message:"invalid signature"}};const i=t.pubfn.getUniIdConfig(n);if(!(tokenSign(`${a[0]}.${a[1]}`,i.tokenSecret)===a[2]))return{code:30204,err:{name:"JsonWebTokenError",message:"invalid signature"}};let r=Buffer.from(a[1],"base64").toString("utf-8");try{r=JSON.parse(r)}catch(e){return{code:30204,err:{name:"JsonWebTokenError",message:"invalid signature"}}}return parseInt(Date.now()/1e3)>=r.exp?{code:30203,err:{name:"TokenExpiredError",message:"jwt expired",expired:r.exp}}:{code:0,message:"",...r}}async createToken(e={}){let{uid:t,needPermission:n=!1,needAddDB:a=!0}=e;if(!t)return{code:30101,message:"uid不能为空"};const i=uniCloud.vk,{config:r}=i.getUnicloud(),o=i.pubfn.getUniIdConfig(r);let s=[],d=[];if(n){s=(await i.system.userDao.findById(_id)).role||[],d=await i.system.sysDao.listPermissionByRoleIds(s)||[]}let c=o.tokenExpiresIn,u=parseInt(Date.now()/1e3);const l={uid:t,role:s,permission:d,iat:u,exp:u+c},p=`${string2Base64$1(JSON.stringify({alg:"HS256",typ:"JWT"}),"binary")}.${string2Base64$1(JSON.stringify(l),"utf-8")}`,f=`${p}.${sign(p,o.tokenSecret)}`,g=1e3*l.exp;return a&&await i.system.userDao.addTokenById(t,f),{token:f,tokenExpired:g}}}function getUniIdTokenManage(e){return new UniIdTokenManage(e)}var uniIdTokenManage=getUniIdTokenManage;let vkunicloud={};class VK{constructor(e){this.router=router,this.md5=md5,this.baseDao=vkBaseDao,this.request=vk_request,this.callFunction=vk_callFunction,this.importObject=vk_importObject,this.pubfn=_function,this.temporaryCache=temporaryCache,this.globalDataCache=globalDataCache_1,this.crypto=crypto,this.openapi=openapi_1,this.formDataUtil=formDataUtils,this.system=system_1,this.getReentrantLockManage=reentrantLockManage,this.getCacheManage=cacheManage,this.baseDir=null,cloudStorageManage&&(this.uploadFile=(...e)=>cloudStorageManage.uploadFile(...e),this.getTempFileURL=(...e)=>cloudStorageManage.getTempFileURL(...e),this.deleteFile=(...e)=>cloudStorageManage.deleteFile(...e),this.downloadFile=(...e)=>cloudStorageManage.downloadFile(...e),this.getExtStorageManager=(...e)=>cloudStorageManage.getExtStorageManager(...e)),this.login=vk_login,this.getWebSocketManage=webSocketManage,this.getUniIdTokenManage=uniIdTokenManage,e&&this.init(e)}require(e){return vkunicloud.requireFn(this.baseDir+"/"+e)}requireFn(e){try{return vkunicloud.requireFn(e)}catch(t){let{message:n=""}=t;return void(-1==n.indexOf("Cannot find module")&&-1==n.indexOf("no such file or directory")&&console.error(`模块【${e}】加载异常：`,t.message))}}use(e,t){for(let n in e)e[n]&&"function"==typeof e[n].init&&e[n].init(t),this[n]=e[n]}getGlobalObject(){return"object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof commonjsGlobal?commonjsGlobal:void 0}init(e={}){let t=this;if(e.requireFn&&(vkunicloud.requireFn=e.requireFn),e.baseDir&&(t.baseDir=e.baseDir),e.configCenter||(e.configCenter=t.requireFn("uni-config-center")),e.uniID||(e.uniID=t.requireFn("uni-id")),e.uniPay||(e.uniPay=t.requireFn("uni-pay")),e.middlewareService||(e.middlewareService=t.requireFn("./middleware/index")),e.daoCenter||(e.daoCenter=t.requireFn("./dao/index")),e.crypto||(e.crypto=t.requireFn("crypto")),e.urlrewrite||(e.urlrewrite=t.requireFn("./util/urlrewrite")),e.config||(e.config=e.configCenter({pluginId:"vk-unicloud"}).requireFile("index.js")),!e.config)throw new Error("配置文件：uniCloud/cloudfunctions/common/uni-config-center/vk-unicloud/index.js \n不存在或编译错误，请检查！");if(!e.db)try{e.db=uniCloud.database()}catch(e){}if(t.vkPay=t.requireFn("vk-uni-pay"),e.pubFun||(e.pubFun=t.requireFn("./util/pubFunction")),e.pubFun&&(t.myfn=e.pubFun),e.redis||(e.redis=t.requireFn("vk-redis")),e.redis)t.redisUtil=e.redis,t.redis=e.redis.redis,t.newRedis=e.redis.newRedis;else try{t.redis=uniCloud.redis}catch(e){}e.uniMap||(e.uniMap=t.requireFn("uni-map-common")),e.uniMap&&(t.uniMap=e.uniMap),e.db&&(vkunicloud.db=e.db,vkunicloud._=e.db.command),vkunicloud.pubfn=t.pubfn,e.configCenter&&(vkunicloud.configCenter=e.configCenter),e.config&&(vkunicloud.config=e.config),e.uniID&&(vkunicloud.uniID=e.uniID),e.uniPay&&(vkunicloud.uniPay=e.uniPay),e.middlewareService&&(vkunicloud.middlewareService=e.middlewareService),e.pubFun&&(vkunicloud.pubFun=e.pubFun),e.customUtil&&(vkunicloud.customUtil=e.customUtil),e.crypto&&(vkunicloud.crypto=e.crypto),e.urlrewrite&&(vkunicloud.urlrewrite=e.urlrewrite);const n={vk:t,...vkunicloud};t.use({daoCenter:e.daoCenter,baseDao:t.baseDao,openapi:t.openapi,globalDataCache:t.globalDataCache,system:t.system,pubFun:e.pubFun,pubfn:t.pubfn},n);try{let n=t.getGlobalObject();"object"==typeof n&&e.middlewareService&&(n.vk=t)}catch(e){}try{Object.getPrototypeOf(uniCloud).vk=t}catch(e){}}getUnicloud(){return vkunicloud}getConfig(e,t){if(_function.isNull(e))return vkunicloud.config;let n=_function.getData(vkunicloud.config,e);return _function.isNull(n)&&void 0!==t&&(n=t),n}createInstance(e){return new VK(e)}}var src=new VK;module.exports=src;
