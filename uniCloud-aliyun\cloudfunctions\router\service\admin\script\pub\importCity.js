'use strict';
const city = require('./city.json')
module.exports = {
	/**
	 * XXXnameXXX
	 * @url admin/script/pub/import-city 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		console.log('城市数据', city.length);
		// 分析数据并导入
		let ary = []

		let levelOptions = ['province', 'city', 'county']

		function getChildren(list, level) {
			list.forEach(item => {
				if (item.children) getChildren(item.children, level + 1)
				delete item.children
				item.level = levelOptions[level]
				ary.push(item)
			});
		}

		getChildren(city, 0)

		let ids = await vk.baseDao.adds({
			dbName: "city-dicts",
			dataJson: ary
		});
		console.log('success');
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}