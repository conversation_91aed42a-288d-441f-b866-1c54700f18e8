'use strict';
module.exports = {
	/**
	 * 国聘脚本
	 * @url admin/script/pub/iguopin 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		let keyMap = [{
				field: "job_name",
				key: "name",
			},
			{
				field: "contents",
				key: "introduce",
			},
			{
				field: "company_name",
				key: "company_name",
			},
			{
				field: "education_cn",
				key: "education",
			},
			{
				field: "nature_cn",
				key: "recruitment_type",
			},
			{
				field: "end_time",
				key: "end_date",
			},
		];

		let result = await vk.request({
			url: `https://gp-api.iguopin.com/api/jobs/v1/recom-job`,
			method: "POST",
			header: {
				"content-type": "application/json; charset=utf-8",
			},
			data: {
				"search": {
					"page": 1,
					"page_size": 200,
					"keyword": "",
					"nature": [
						"115xW5oQ"
					]
				},
				"recom": {
					"update_time": true,
					"company_nature": true,
					"hot_job": true
				}
			}
		});

		function wageData(item) {
			let { min_wage, max_wage } = item
			if (!min_wage && !max_wage) return '面议'
			min_wage = min_wage || 0
			max_wage = max_wage || 0
			let min = min_wage > 1000 ? `${min_wage/1000}k` : min_wage
			let max = max_wage > 1000 ? `${max_wage/1000}k` : max_wage
			return item.min_wage == item.max_wage ? max + item.wage_unit_cn : `${min}-${max}${item.wage_unit_cn}`

		}

		let edu_ary = [];
		let major_ary = [];
		let has_ary = []
		let transformedData = result.data.list.map(item => {
			let obj = {
				"name": item.job_name,
				"company_name": item.company_name,
				"job_property": [item.company_info.nature_cn] || [],
				"province": item.district_list[0].province,
				"city": item.district_list[0].city,
				"county": item.district_list[0].district,
				"recruitment_type": [item.nature_cn],
				"major": item.major_cn || [],
				"major_alias": item.major_cn || [],
				"education": item.education_cn,
				"end_date": item.end_time,
				"salary": wageData(item),
				"original_url": "",
				"application_url": "",
				"introduce": item.contents,
				"origin_data_id": item.job_id,
				"origin_type": 1
			}
			edu_ary.push(obj.education)
			major_ary.push(...obj.major)
			has_ary.push(item.job_id)
			return obj
		})

		// 查询是否是数据库存在的数据
		let has_data = await vk.baseDao.select({
			dbName: "jobs",
			getCount: false,
			getMain: true,
			pageIndex: 1,
			pageSize: 5000,
			whereJson: {
				origin_data_id: _.in(has_ary)
			},
			fieldJson: {
				_id: true,
				origin_data_id: true,
			},
			sortArr: [{ name: "_id", type: "desc" }],
		});
		// 暂且过滤数据
		transformedData = transformedData.filter(item => has_data.findIndex(items => items.origin_data_id == item.origin_data_id) === -1)


		// 校验专业，地区，学历

		// 查找相应的学历
		let edu_list = await vk.baseDao.select({
			dbName: "education-list",
			getMain: true,
			getCount: false,
			pageIndex: 1,
			pageSize: 5000,
			whereJson: {
				name: _.in(edu_ary),
			},
			fieldJson: {
				name: true,
				_id: true,
			},
		});

		// 查找相应的专业或者专业类别
		let major_list = await vk.baseDao.select({
			dbName: "major-list",
			getMain: true,
			getCount: false,
			pageIndex: 1,
			pageSize: 5000,
			whereJson: {
				name: _.in(major_ary),
			},
			fieldJson: {
				name: true,
				code: true,
			},
		});

		let major_classic_list = await vk.baseDao.select({
			dbName: "major-classic",
			getMain: true,
			getCount: false,
			pageIndex: 1,
			pageSize: 5000,
			whereJson: {
				level: 1,
				name: _.in(major_ary),
			},
			fieldJson: {
				name: true,
				_id: true,
			},
		});
		// 匹配信息
		transformedData = transformedData.map((item) => {
			// 匹配查询到的学历
			let edu_info = edu_list.find((items) => item.education === items.name);
			if (edu_info) item.education_id = edu_info._id;
			// 匹配查询到的专业
			let major_info = major_list.filter((aItem) => item.major.some((bItem) => aItem.name === bItem));
			const major_code = major_info.length > 0 ? major_info.map((items) => items.code) : [];
			// 匹配专业类别
			let major_classic_info = major_classic_list.filter((aItem) => item.major.some((bItem) => aItem.name === bItem));
			const major_classic = major_classic_info.length > 0 ? major_classic_info.map((items) => items.name) : [];
			// 将专业信息存储成数据
			item.major = [...major_classic, ...major_code]

			return item;
		});

		// 挑出异常数据和正常数据
		let error_data = [];
		let normal_data = [];

		transformedData.forEach((item) => {
			item.err = [];
			if (!item.province && !item.city && !item.county) item.err.push("地区信息不完整");

			if (item.err.length === 0) {
				delete item.err;
				normal_data.push(item);
			} else {
				error_data.push(item);
			}
		});

		// 把正常的数据加入职位列表
		if (normal_data.length > 0) {
			await vk.baseDao.adds({
				dbName: "jobs",
				dataJson: normal_data,
			});
		}
		if (error_data.length > 0) {
			await vk.baseDao.adds({
				dbName: "jobs-pending",
				dataJson: error_data,
			});
		}

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}