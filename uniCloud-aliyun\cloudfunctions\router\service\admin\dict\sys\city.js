'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url admin/base/area/sys/get 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, pageSize, level, name, parent_code } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		res = await vk.baseDao.select({
			dbName: "city-dicts",
			getCount: false,
			pageIndex: 1,
			pageSize: pageSize || 20,
			whereJson: {
				level,
				name: new RegExp(name),
				parent_code,
			},
		});



		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}